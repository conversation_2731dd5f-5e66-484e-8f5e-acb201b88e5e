<template>
  <n-popover trigger="click">
    <template #trigger>
      <div class="marquee" :style="{ height: height }">
        <span
          class="marquee__title"
          ref="marqueeTitle"
          :style="{
            color,
            fontSize: fontSize + 'px',
            animationDuration: speed + 's',
            '--speed': speed,
          }"
          v-html="title"
        ></span>
      </div>
    </template>
    <span>{{ content }}</span>
  </n-popover>
</template>

<script setup lang="ts">
const props = defineProps({
  content: {
    type: String,
    default: "",
  },
  color: {
    type: String,
    default: "#000",
  },
  fontSize: {
    type: Number,
    default: 14,
  },
  // speed: {
  //     type: Number,
  //     default: 0
  // },
  // bgColor: {
  //     type: String,
  //     default: '#CC2529'
  // },
  height: {
    type: String,
    default: "0.8rem",
  },
});
const DEFAULT_SPEED = 50; // 默认速度，每秒跑的距离，单位：px/s
const speed = ref(0);
const title = ref("");
const marqueeTitle = ref<any>(null);

const calcSpeed = () => {
  if (title.value !== "" && speed.value === 0) {
    nextTick(() => {
      const width = marqueeTitle.value.clientWidth;
      speed.value = Number(width / DEFAULT_SPEED).toFixed(2);
    });
  }
};

onMounted(() => {
  if (props.content) {
    title.value = props.content;
    calcSpeed();
  }
});

watch(
  () => props.content,
  () => {
    title.value = props.content;
    calcSpeed();
  }
);
</script>

<style scoped>
.marquee {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  word-break: break-all;
  white-space: nowrap;
}
.marquee__title {
  cursor: default;
  display: inline-block;
  padding-left: 80%;
  animation: marqueeMove calc(var(--speed) * 1s) linear infinite;
}
@keyframes marqueeMove {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}
</style>
