syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsCategoryPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsCategoryModel data = 3;
}

message GoodsCategoryDetailResp {
    common.Result result = 1;
    GoodsCategoryModel data = 2;
}

// 商品分类树信息
message GoodsCategoryTree {
    string id = 1; // 商品分类ID
    string categoryName = 2; // 商品分类名称
    int32 categoryLevel = 3; // 商品分类等级
    repeated GoodsCategoryTree children = 4; // 下级商品分类
    string categoryCnName = 5; // 商品分类中文名称
    string categoryCombineName = 6; // 商品分类组合名称
    int32 adjustRank = 20; //搜索权重分（负数表示降权，分类树中的权重相加，有效值范围：正负100万）
    int32 goodsCount = 30; //商品数量（含下级分类的商品数）
    bool enabled = 7; //是否启用
}

message GoodsCategoryTreeResp {
    common.Result result = 1;
    GoodsCategoryTree data = 2;
}

// 商品分类信息
message GoodsCategoryModel {
    string id = 1; // 商品类别id
    string cateCode = 2; // 类别编码
    string cateCodeUnique = 3; // 类别编码—多系统唯一记录编码
    string extendData = 4; // 扩展数据字段
    string parentId = 5; // 商品类别父id
    int32 cateLevel = 6; // 商品类别级别,1:一级类目,2:二级类目,3:三级类目
    int32 showType = 7; // 默认展示模式
    int32 idx = 8; // 排序
    string cateName = 9; // 商品类别名称
    string cateAlias = 10; // 商品类别-别名
    string cateAppName = 11; // 商品类别-小程序别名
    string cateLogo = 12; // 商品类别logo
    string cateIcon = 13; // 商品类别Icon
    double cateFee = 14; // 商品类别服务费率(只有三级有)
    double cateMoney = 15; // 商品类别平台使用费(只有一级有)
    string taxCode = 16; // 商品税收类型编码，只需要设置一级类型，其子集默认赋值处理
    repeated string priceUnitIdList = 17; // 可下单包装Id编码集合
    bool enabled = 18; //是否启用
    string cateCnName = 19; // 商品类别中文名称
    int32 adjustRank = 20; //搜索权重分（负数表示降权，分类树中的权重相加，有效值范围：正负100万）
    int32 goodsCount = 30; //商品数量（含下级分类的商品数）
}


message UpdateCategoryGoodsCountResp {
    common.Result result = 1;
    UpdateCategoryGoodsCountModel data = 2;
}

message UpdateCategoryGoodsCountModel {
    int32 totalGoodsCount = 10; //商品数量总计
    int32 hasGoodsLeafCategoryCount = 20; //包含商品的末级分类数量
    int32 changedCategoryCount = 30; //更新了商品数的分类数量
}

message GoodsCategoryEnableResp {
    common.Result result = 1;
    GoodsCategoryEnableModel data = 2;
}

message GoodsCategoryEnableModel {
    repeated string categoryIds = 1; //所有有关联的分类
    repeated GoodsCategoryRelationModel relation = 2;
}

message GoodsCategoryRelationModel {
    string marketingRuleId = 1;
    string marketingRuleName = 2;
    // 营销分类
    repeated MarketingCategoryRelationModel marketingCategories = 3;
}

message MarketingCategoryRelationModel {
    string marketingCategoryId = 1;
    string cateName = 2;
}