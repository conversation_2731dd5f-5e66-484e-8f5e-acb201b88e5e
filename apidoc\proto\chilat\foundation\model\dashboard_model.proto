syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";


import "common.proto";

message DashboardResp {
  common.Result result = 1;
  DashboardModel data = 2;
}

// 仪表盘
message DashboardModel {
  int32 todayInquiryCount = 1; // 今日询盘单数
  int32 historyInquiryCount = 2; // 历史询盘单数
  int32 todayCustomerCount = 3; // 今日询盘客户数
  int32 historyCustomerCount = 4; // 历史询盘客户数
  int32 todayGoodsCount = 5; // 今日询盘商品数
  int32 historyGoodsCount = 6; // 历史询盘商品数
  int32 todayRegisterCount = 7; // 今日注册用户数
  int32 historyRegisterCount = 8; // 历史注册用户数
  repeated CategoryStatsModel topCategory = 9; //品类询盘数top10
  repeated SkuStatsModel topSku = 10; //商品销量top10
  repeated CustomerStatsModel topCustomer = 11; //客户询盘次数top10
  repeated CustomerInquiryRateModel customerInquiryRate = 12; //购物频次
}

message CategoryStatsModel {
  string id = 1;
  string cateName = 2; //分类名称
  int32 count = 3; //数量
}

message SkuStatsModel {
  string id = 1;
  string goodsNo = 2;
  string goodsName = 3; //商品名称
  int32 count = 4; //数量
}

message CustomerStatsModel {
  string id = 1;
  string username = 2; //用户名
  string count = 3;
}

message CustomerInquiryRateModel {
  string desc = 1;
  int32 times = 2; //次数
  int32 count = 3; // 总数
}