syntax = "proto3";
package xpormayor.commodity;

option java_package = "com.xpormayor.rpc.commodity.param";

import "common.proto";

message GoodsLookingSaveParam {
    string goodsName = 1; // 商品名称
    repeated string goodsImages = 2; // 商品图片
    double lowPrice = 3; // 最低价格
    double highPrice = 4; // 最高价格
    string description = 5; // 商品描述
    string email = 6; // 邮箱
    string whatsapp = 7; // whatsapp
    bool similar = 8; // 相似商品
    LookingOrigin origin = 9; // 找货来源
    string countryId = 11; //国家id
    string countryCode = 12; //国家代码（服务器端自动赋值）
    string countryName = 13; //国家名称（服务器端自动赋值）
    string areaCode = 14; //区号
    int32 quantity = 15; //意向数量
}

enum LookingOrigin {
    LOOKING_ORIGIN_XPORMAYOR = 0; //x项目
    LOOKING_ORIGIN_CHILAT = 1; //chilat2.0
}
