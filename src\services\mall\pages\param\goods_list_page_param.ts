/* eslint-disable */
import * as _m0 from "protobufjs/minimal";

export const protobufPackage = "mall.pages";

export interface GoodsListQueryParam {
  /** 商品ID */
  goodsId: string;
  /** 类目ID（必填） */
  categoryId: string;
  /** 选择商品分类过滤项（可将末级分类转为：上级分类ID查询，末级分类过滤） */
  childCategoryId: string;
  /** 品牌ID，多个用下划线（_）分隔 */
  brandIds: string;
  /** 关键字，支持以图搜图 */
  keyword: string;
  /** 最低价限制，支持以图搜图 */
  minPrice: number;
  /** 最高价限制，支持以图搜图 */
  maxPrice: number;
  /** 小于等于“最小购买数量”（起订量） */
  leMinBuyQuantity: number;
  /** 大于等于“最小购买数量”（起订量） */
  geMinBuyQuantity: number;
  /** 营销规则代码 */
  marketingRuleCode: string;
  /** 排序字段（11:人气升序；12:人气倒序；21:销量升序；22:销量倒序；31:价格升序；32:价格倒序），支持以图搜图 */
  sortField: number;
  /** 页号（从1开始，默认1） */
  pageNo: number;
  /** 每页显示条数（后端自动匹配到最接近的页面条数） */
  pageSize: number;
  /** 商品ID */
  goodsIds: string[];
}

function createBaseGoodsListQueryParam(): GoodsListQueryParam {
  return {
    goodsId: "",
    categoryId: "",
    childCategoryId: "",
    brandIds: "",
    keyword: "",
    minPrice: 0,
    maxPrice: 0,
    leMinBuyQuantity: 0,
    geMinBuyQuantity: 0,
    marketingRuleCode: "",
    sortField: 0,
    pageNo: 0,
    pageSize: 0,
    goodsIds: [],
  };
}

export const GoodsListQueryParam = {
  encode(message: GoodsListQueryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(42).string(message.goodsId);
    }
    if (message.categoryId !== "") {
      writer.uint32(82).string(message.categoryId);
    }
    if (message.childCategoryId !== "") {
      writer.uint32(122).string(message.childCategoryId);
    }
    if (message.brandIds !== "") {
      writer.uint32(162).string(message.brandIds);
    }
    if (message.keyword !== "") {
      writer.uint32(242).string(message.keyword);
    }
    if (message.minPrice !== 0) {
      writer.uint32(361).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(481).double(message.maxPrice);
    }
    if (message.leMinBuyQuantity !== 0) {
      writer.uint32(488).int32(message.leMinBuyQuantity);
    }
    if (message.geMinBuyQuantity !== 0) {
      writer.uint32(496).int32(message.geMinBuyQuantity);
    }
    if (message.marketingRuleCode !== "") {
      writer.uint32(522).string(message.marketingRuleCode);
    }
    if (message.sortField !== 0) {
      writer.uint32(560).int32(message.sortField);
    }
    if (message.pageNo !== 0) {
      writer.uint32(640).int32(message.pageNo);
    }
    if (message.pageSize !== 0) {
      writer.uint32(720).int32(message.pageSize);
    }
    for (const v of message.goodsIds) {
      writer.uint32(802).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListQueryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListQueryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 5:
          if (tag !== 42) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.childCategoryId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.brandIds = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.keyword = reader.string();
          continue;
        case 45:
          if (tag !== 361) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 60:
          if (tag !== 481) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 61:
          if (tag !== 488) {
            break;
          }

          message.leMinBuyQuantity = reader.int32();
          continue;
        case 62:
          if (tag !== 496) {
            break;
          }

          message.geMinBuyQuantity = reader.int32();
          continue;
        case 65:
          if (tag !== 522) {
            break;
          }

          message.marketingRuleCode = reader.string();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.sortField = reader.int32();
          continue;
        case 80:
          if (tag !== 640) {
            break;
          }

          message.pageNo = reader.int32();
          continue;
        case 90:
          if (tag !== 720) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.goodsIds.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListQueryParam {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      childCategoryId: isSet(object.childCategoryId) ? globalThis.String(object.childCategoryId) : "",
      brandIds: isSet(object.brandIds) ? globalThis.String(object.brandIds) : "",
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      leMinBuyQuantity: isSet(object.leMinBuyQuantity) ? globalThis.Number(object.leMinBuyQuantity) : 0,
      geMinBuyQuantity: isSet(object.geMinBuyQuantity) ? globalThis.Number(object.geMinBuyQuantity) : 0,
      marketingRuleCode: isSet(object.marketingRuleCode) ? globalThis.String(object.marketingRuleCode) : "",
      sortField: isSet(object.sortField) ? globalThis.Number(object.sortField) : 0,
      pageNo: isSet(object.pageNo) ? globalThis.Number(object.pageNo) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      goodsIds: globalThis.Array.isArray(object?.goodsIds) ? object.goodsIds.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: GoodsListQueryParam): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.childCategoryId !== "") {
      obj.childCategoryId = message.childCategoryId;
    }
    if (message.brandIds !== "") {
      obj.brandIds = message.brandIds;
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.leMinBuyQuantity !== 0) {
      obj.leMinBuyQuantity = Math.round(message.leMinBuyQuantity);
    }
    if (message.geMinBuyQuantity !== 0) {
      obj.geMinBuyQuantity = Math.round(message.geMinBuyQuantity);
    }
    if (message.marketingRuleCode !== "") {
      obj.marketingRuleCode = message.marketingRuleCode;
    }
    if (message.sortField !== 0) {
      obj.sortField = Math.round(message.sortField);
    }
    if (message.pageNo !== 0) {
      obj.pageNo = Math.round(message.pageNo);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    if (message.goodsIds?.length) {
      obj.goodsIds = message.goodsIds;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListQueryParam>, I>>(base?: I): GoodsListQueryParam {
    return GoodsListQueryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListQueryParam>, I>>(object: I): GoodsListQueryParam {
    const message = createBaseGoodsListQueryParam();
    message.goodsId = object.goodsId ?? "";
    message.categoryId = object.categoryId ?? "";
    message.childCategoryId = object.childCategoryId ?? "";
    message.brandIds = object.brandIds ?? "";
    message.keyword = object.keyword ?? "";
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    message.leMinBuyQuantity = object.leMinBuyQuantity ?? 0;
    message.geMinBuyQuantity = object.geMinBuyQuantity ?? 0;
    message.marketingRuleCode = object.marketingRuleCode ?? "";
    message.sortField = object.sortField ?? 0;
    message.pageNo = object.pageNo ?? 0;
    message.pageSize = object.pageSize ?? 0;
    message.goodsIds = object.goodsIds?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
