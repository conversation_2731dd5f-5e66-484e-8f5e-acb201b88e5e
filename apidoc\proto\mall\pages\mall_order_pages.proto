syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "mall/pages/param/mall_order_pages_param.proto";
import "mall/pages/model/mall_order_pages_model.proto";

// 订单支付相关页面
service MallOrderPage {

  //分页查询订单列表
  rpc getOrderList (GetOrderListRequestParam) returns (GetOrderListResp);

  //取消订单
  rpc cancelOrder (CancelOrderParam) returns (common.Result);

  //查询订单详情
  rpc getOrderDetail (GetOrderDetailParam) returns (GetOrderDetailResp);

  //未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
  rpc openCashDesk (OpenCashDeskParam) returns (OpenCashDeskResp);

  //查询收银台信息，包括待支付金额、支付方式列表、是否已支付
  rpc getCashDeskInfo (GetCashDeskInfoParam) returns (GetCashDeskInfoResp);

  //收银台提交支付
  rpc submitPayment (SubmitPaymentParam) returns (SubmitPaymentResp);

  //查询支付结果
  rpc queryPayResult (QueryPayResultParam) returns (QueryPayResultResp);
}