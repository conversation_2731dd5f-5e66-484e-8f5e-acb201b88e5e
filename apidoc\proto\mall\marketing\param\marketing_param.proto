syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.param";

import "common.proto";

message MarketingCategorySaveParam {
  string id = 1; // 营销分类id
  string parentId = 5; // 营销分类父id
  int32 cateLevel = 6; // 营销分类级别,1:一级类目,2:二级类目,3:三级类目
  int32 idx = 8; // 排序
  string cateName = 9; // 营销分类名称
  string cateAlias = 10; // 营销分类-别名
  string cateLogo = 12; // 营销分类logo
  bool enabled = 18; //是否启用
}