/**
问卷：客户体验与意见调查问卷
surveyCode: v2503-CustomerExperienceOpinion


*/

syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main.param";


import "common.proto";
import "common/business.proto";


message SaveSurveyFormParam {
  string surveyCode = 10; //问卷代码（不区分英文字母大小写）
  repeated SurveyFormAnswer answers = 20; //回答列表
}

message SurveyFormAnswer {
  string questionCode = 10; //问题代码
  repeated SurveyAnswerOptionValue optionValues = 20; //问题选项值（与textAnswer二选一；若非必选，则传空数组）
  string textAnswer = 30; //文本回答内容（与textAnswer二选一；若非必选，则传空字符串）
}

message SurveyAnswerOptionValue {
  string value = 10; //选项值
  string otherText = 20; //选项的附加回答
}