{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build  --dotenv .env.production", "dev": "nuxt dev  --dotenv .env.development", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@pinia/nuxt": "^0.5.1", "@videojs-player/vue": "^1.0.0", "@vite-pwa/nuxt": "^0.10.5", "@vueuse/core": "^10.9.0", "@vueuse/nuxt": "^10.9.0", "nuxt": "3.11.2", "pinia-plugin-persistedstate": "^3.2.1", "video.js": "^8.10.0", "videojs-youtube": "^3.0.1", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@bg-dev/nuxt-naiveui": "^1.12.3", "@nuxtjs/device": "^3.1.1", "@unocss/nuxt": "^0.59.0", "nuxt-icon": "^0.6.10", "nuxt-lodash": "^2.5.3", "protoc-gen-ts-api": "1.0.8", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.75.0", "ts-proto": "^1.165.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}}