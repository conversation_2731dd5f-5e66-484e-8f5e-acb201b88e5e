syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main";

import "common.proto";
import "chilat/basis/param/stock_param.proto";

// 旧商城兼容API
service CompatibleApi {
    // 有效订单更新库存（不检查库存是否充足）
    rpc updateStockForOrderSales (chilat.basis.UpdateStockForOrderParam) returns (common.ApiResult);
    // 取消订单退还库存
    rpc updateStockForOrderCancel (chilat.basis.UpdateStockForCancelParam) returns (common.ApiResult);
}