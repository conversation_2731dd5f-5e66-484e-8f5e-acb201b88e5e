syntax = "proto3";

package chilat.coupon;

import "chilat/coupon/coupon_common.proto";
import "common.proto";

option java_package = "com.chilat.rpc.coupon.model";

//管理系统-优惠券保存/修改返回
message CouponSaveUpdateResp {
    common.Result result = 1;
}

//管理后台-优惠券详请
message CouponDetailModelResp {
    common.Result result = 1;
    CouponInfoDetailModel data = 2; //优惠券基本规则参数
}

//详情组装Model
message CouponInfoDetailModel {
    string id = 1;
    string couponName = 2;//优惠券名称
    string couponAlias = 3; //优惠券别名
    string userInstructions = 4; //用户使用说明
    string backendRemark = 5; //后台备注
    coupon.CouponTypeStatus couponType = 6; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    coupon.CouponWayStatus couponWay = 7; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
    double useConditionsAmount = 8; //使用条件(0不限制，达到多少金额可以使用，每满xxx可用)
    double preferentialAmount = 9; //优惠额度
    double discount = 10; //折扣（券类型为折扣券使用）
    int32 maxNumQuantity = 11; //最大发放数量
    int32 issuedQuantity = 12; //已发放数量
    coupon.CouponDistributionStatus couponDistributionMethod = 13; //优惠券发放方式: couponDistributionActive、主动领取 couponDistributionAutomatic、自动发放 couponDistributionExchange、优惠码兑换
    coupon.CouponStatus couponStatus = 14; //主体卡券的状态: couponDraft 草稿 couponNotActive 失效 couponActive 生效 couponLock 锁定 couponUnLock 解锁 couponExpire 过期 couponCancel 删除
    int64 couponIssueStartDate = 15; //优惠券发放开始日期
    int64 couponIssueEndDate = 16; //优惠券发放结束日期
    string activeId = 17;//活动编码
    int32 receivedQuantity = 20;//已领取数量
    int32 userQuantity = 21;//已使用数量
    //基本规则
    CouponEffectiveTypeStatus couponEffectiveType = 22;//有效期类型 （distributionDateEffectiveDays 发放日期+有效天数  distributionDateEffectiveHours 发放日期+有效小时 fixedTime 固定时间）
    int64 couponStartExpirationDate = 23;//固定开始时间
    int64 couponEndExpirationDate = 24;//固定结束时间
    int32 effectiveNum = 27; //有效类型为distributionDateEffectiveDays 时取有效天数,有效类型为distributionDateEffectiveHours 时取、有效小时,fixedTime 固定时间
    repeated SystemCouponModel systemCouponModelsList = 28;    //卡券后台操作日志集合
    repeated CouponInfoUseRuleModel couponInfoUseRuleModelList = 29;//使用规则
    coupon.CouponUseConditionsTypestatus couponUseConditionsType = 30;//使用条件类型
}
//卡券后台操作日志
message SystemCouponModel {
    int64 cdate = 7; //操作时间
    string coperator = 8; //操作人
    CouponStatus couponStatus = 9;
    string remark = 10; //操作内容
}

//使用规则
message CouponInfoUseRuleModel {
    int32  useRuleCount= 1;//叠加张数,0代表无限
    CouponUseRuleStatus useRuleType = 2;//使用规则，（COUPON_MAXIMUM_AVAILABLE 本券最多可使用  COUPON_SAME_TYPE_OVERLAY 可与同类型叠加 COUPON_FIRST_USE 仅可首单使用）
    string couponId = 3;//优惠券id
}

//优惠券活动代码列表返回
message GetCouponActivityCodesResp {
    common.Result result = 1;
    repeated common.CodeNameModel data = 2; //code:活动编码，name:活动名称
}
