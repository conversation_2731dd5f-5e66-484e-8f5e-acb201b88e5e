syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_audit_param.proto";
import "chilat/commodity/model/goods_audit_model.proto";
import "common.proto";

// 审核管理（客户端）
service ClientAudit {
  // 审核列表
  rpc pageList (GoodsAuditPageQueryParam) returns (GoodsAuditPageResp);
  // 审核商品
  rpc goodsPageList (AuditGoodsPageQueryParam) returns (AuditGoodsPageResp);
  // 设为侵权
  rpc illegal (common.IdParam) returns (common.ApiResult);
  // 取消侵权
  rpc cancelIllegal (common.IdParam) returns (common.ApiResult);
  // 提取码
  rpc loginByCode (LoginByCodeParam) returns (common.StringResult);
  // 领用
  rpc claim (common.IdParam) returns (common.ApiResult);
  // 责任人列表
  rpc leaderList (common.EmptyParam) returns (LeaderListResp);
  // 校验完成
  rpc finish (common.IdsParam) returns (common.ApiResult);
  // 访问日志
  rpc visit (common.EmptyParam) returns (common.ApiResult);
}