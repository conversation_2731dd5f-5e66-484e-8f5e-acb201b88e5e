syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.param";

import "chilat/basis/model/coupon_usable_model.proto";
import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";
import "common.proto";

message MallMyCouponDetailParam {
  common.PageParam page = 1;//分页参数
  chilat.coupon.TicketStatus ticketStatus = 2;//优惠券状态
  chilat.coupon.CouponTypeStatus couponType = 3; //产品券：couponTypeProduct ,佣金券：couponTypeCommission
}


//可使用优惠券参数
message MallCouponUsableParam {
  string orderNo = 1;//订单号
  chilat.coupon.CouponTypeStatus couponType = 2; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
}

//检查可使用优惠券校验参数
message MallCouponCheckParam {
  string orderNo = 1;//订单号
  repeated chilat.basis.CouponUsableDetailModel selectCouponModelsList = 2;//选中优惠券集合
  repeated chilat.basis.CouponUsableDetailModel notCouponModelsList = 3;//未选中优惠券集合
  chilat.coupon.CouponTypeStatus couponType=4;//券类型
}

