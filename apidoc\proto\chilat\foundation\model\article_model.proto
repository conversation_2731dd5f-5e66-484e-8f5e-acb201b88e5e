syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "common.proto";

// 文章分页返回结果
message ArticlePageResp {
  common.Result result = 1;
  common.Page page = 2;
  ArticleModel data = 3;
}

// 文章
message ArticleModel {
  string id = 1;
  string title = 2; // 标题
  string content = 3; // 内容
  string logo = 4; // logo
  bool isDefault = 5; // 默认展示
  int32 readCount = 6; // 已读次数
  bool pcShow = 7; // PC是否展示
  int32 idx = 8; // 展示顺序
  int64 sendTime = 9; // 发表时间
  string author = 10; // 作者
  string sourceChannelName = 11; // 来源
  string introduce = 12; // 简介
  int64 createTime = 13; // 创建时间
  int64 updateTime = 14; // 更新时间
  repeated string categoryNameList = 15; // 分类路径
  repeated int32 categoryIds = 16; // 分类路径ID
}