<template>
  <div class="page-wrapper">
    <search-cate-card></search-cate-card>
    <div class="w-[1080px] mx-auto pt-[56px] pb-[128px]">
      <div class="text-[52px] leading-[52px] font-medium text-center">
        Formas de pago
      </div>
      <div class="text-center mt-[78px]">
        <div class="text-[34px] leading-[34px] font-medium">
          1. ¿Cuáles son las modalidades de pago?
        </div>
        <div class="text-[18px] leading-[22px] text-[#7F7F7F] mt-[16px]">
          Chilatshop ofrece diferentes opciones en cuanto a métodos de pago. No
          sólo admite los principales métodos de pago internacionales, sino que
          también ofrece una amplia gama de opciones de pago en moneda local.
          Esto significa que puede realizar transacciones utilizando métodos de
          pago conocidos sin gastos de conversión adicionales. Esta flexibilidad
          en los métodos de pago hace que sus transacciones transfronterizas
          sean más cómodas.
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
        <n-space
          class="flex justify-center items-center mt-[46px]"
          :style="{ gap: '0 40px' }"
        >
          <img
            loading="lazy"
            alt="Visa"
            class="h-[58px]"
            src="@/assets/icons/article/Visa.png"
          />
          <img
            loading="lazy"
            alt="BBVA"
            class="h-[54px]"
            src="@/assets/icons/article/BBVA.png"
          />
          <img
            loading="lazy"
            alt="Interbank"
            class="h-[54px]"
            src="@/assets/icons/article/Interbank.png"
          />
          <img
            loading="lazy"
            alt="Scotiabank"
            class="h-[54px]"
            src="@/assets/icons/article/Scotiabank.png"
          />
          <img
            loading="lazy"
            alt="Paypal"
            class="h-[50px]"
            src="@/assets/icons/article/Paypal.png"
          />
          <img
            loading="lazy"
            alt="Yape"
            class="h-[54px]"
            src="@/assets/icons/article/Yape.png"
          />
          <img
            loading="lazy"
            alt="Plin"
            class="h-[54px]"
            src="@/assets/icons/article/Plin.png"
          />
          <img
            loading="lazy"
            alt="Tunki"
            class="h-[54px]"
            src="@/assets/icons/article/Tunki.png"
          />
        </n-space>
        <div class="flex justify-between items-center mt-[76px]">
          <div
            class="w-[255px] h-[265px] px-[16px] py-[24px] bg-[#f5f5f5] rounded-[20px] flex flex-col items-center"
          >
            <img
              loading="lazy"
              alt="transferencia"
              class="w-[100px]"
              src="@/assets/icons/article/transfer.svg"
            />
            <div class="text-[20px] leading-[24px] font-medium mt-[34px]">
              Deposite /<br />
              Transferencia bancaria
            </div>
          </div>
          <div
            class="w-[255px] h-[265px] px-[16px] py-[24px] bg-[#f5f5f5] rounded-[20px] flex flex-col items-center text-center"
          >
            <img
              loading="lazy"
              alt="cash"
              class="w-[100px]"
              src="@/assets/icons/article/cash.svg"
            />
            <div class="text-[20px] leading-[24px] font-medium mt-[34px]">
              Pago en efectivo
            </div>
          </div>
          <div
            class="w-[255px] h-[265px] px-[24px] py-[24px] bg-[#f5f5f5] rounded-[20px] flex flex-col items-center"
          >
            <img
              loading="lazy"
              alt="ewallet"
              class="w-[100px]"
              src="@/assets/icons/article/ewallet.svg"
            />
            <div class="text-[20px] leading-[24px] font-medium mt-[34px]">
              Billetera electrónica
            </div>
            <div class="text-[16px] leading-[20px] mt-[12px]">
              Tupay (including Yape, PLlN, Tunki, etc.)
            </div>
          </div>
          <div
            class="w-[255px] h-[265px] px-[20px] py-[24px] bg-[#f5f5f5] rounded-[20px] flex flex-col items-center"
          >
            <img
              loading="lazy"
              alt="others"
              class="w-[100px]"
              src="@/assets/icons/article/others.svg"
            />
            <div class="text-[20px] leading-[24px] font-medium mt-[34px]">
              Otros métodos de pago
            </div>
            <div class="text-[16px] leading-[20px] mt-[12px]">
              Paypal, pago fuera de línea en USD
            </div>
          </div>
        </div>
      </div>
      <div class="text-center mt-[90px]">
        <div class="text-[34px] leading-[34px] font-medium">
          2. ¿Cómo se paga?
        </div>
        <div
          class="text-[18px] leading-[22px] text-[#7F7F7F] mt-[16px] px-[120px]"
        >
          Chilatshop admite el pago en línea, cuando envíe una consulta y
          confirme el pedido, se generará un enlace de pago en su centro
          personal, puede pagar directamente haciendo clic en el enlace, no es
          necesario ir al banco para transferir dinero, es muy cómodo y rápido.
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
        <img
          loading="lazy"
          alt="payOrder"
          class="w-[824px] mx-auto mt-[50px]"
          src="@/assets/icons/article/payOrder.png"
        />
      </div>
      <div class="text-center mt-[90px]">
        <div class="text-[34px] leading-[34px] font-medium">
          3. Países en los que actualmente es posible pagar en moneda local
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
        <n-grid :cols="4" :x-gap="126" :y-gap="50" class="mt-[70px]">
          <n-grid-item
            class="w-[175px] flex flex-col"
            v-for="pay in payMethods"
            :key="pay.country"
          >
            <img
              loading="lazy"
              :alt="pay.country"
              :src="pay.icon"
              class="w-[100px] mx-auto"
            />
            <div class="text-[16px] leading-[16px] text-[#7F7F7F] mt-[12px]">
              {{ pay.country }}
            </div>
            <div class="text-[18px] leading-[18px] mt-[6px]">
              {{ pay.currency }}
            </div>
          </n-grid-item>
        </n-grid>
      </div>
      <div class="text-center mt-[90px]">
        <div class="text-[34px] leading-[34px] font-medium">
          4. Pagar por tutoriales
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
        <div class="mt-[50px] rounded-[20px] overflow-hidden">
          <video-you-tube
            :width="1080"
            :height="607.5"
            youtubeId="-5ZBcr0rgJs?si=HZgwkVdHGUeahmDC"
            poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/01/02/c0ca8a81-956c-4f31-98b9-ae2787cef03b.png"
          ></video-you-tube>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");
const userInfo = computed(() => useAuthStore().getUserInfo);

import Mexico from "@/assets/icons/article/Mexico.png";
import Peru from "@/assets/icons/article/Peru.png";
import Argentina from "@/assets/icons/article/Argentina.png";
import Chile from "@/assets/icons/article/Chile.png";
import Colombia from "@/assets/icons/article/Colombia.png";
import CostaRica from "@/assets/icons/article/CostaRica.png";
import Ecuador from "@/assets/icons/article/Ecuador.png";
import Panama from "@/assets/icons/article/Panama.png";

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");
});

const payMethods = [
  {
    icon: Mexico,
    country: "México",
    currency: "Peso Mexicano",
  },
  {
    icon: Peru,
    country: "Perú",
    currency: "Nuevo Sol",
  },
  {
    icon: Argentina,
    country: "Argentina",
    currency: "Peso argentino",
  },
  {
    icon: Chile,
    country: "Chile",
    currency: "Peso chileno",
  },
  {
    icon: Colombia,
    country: "Colombia",
    currency: "Peso colombiano",
  },
  {
    icon: CostaRica,
    country: "Costa Rica",
    currency: "Colón costarricense",
  },
  {
    icon: Ecuador,
    country: "Ecuador",
    currency: "Dólar estadounidense",
  },
  {
    icon: Panama,
    country: "Panamá",
    currency: "Balboa",
  },
];

const onLoginClick = async () => {
  window?.MyStat?.addPageEvent(
    "passport_open_nav_register",
    "点击顶部导航注册，打开注册窗口"
  ); // 埋点

  loginRegister?.openLogin("", 0);
};
</script>

<style scoped lang="scss">
.page-wrapper {
  width: 1280px;
  margin: 0 auto;
  color: #222;
}
.page-header {
  width: 100%;
  height: 410px;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/inviteBg.jpg");
  background-repeat: no-repeat;
}
.page-footer {
  width: 100%;
  height: 150px;
  position: relative;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/footerBg.jpg");
  background-repeat: no-repeat;
}
</style>
