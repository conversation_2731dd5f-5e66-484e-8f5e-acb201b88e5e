<template>
  <div class="mobile-container" v-if="pageData.showErrorPage">
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>
    <div class="px-[0.24rem] py-[0.4rem]">
      <div
        class="text-[0.36rem] font-medium text-[#333] flex items-center justify-center"
      >
        <img loading="lazy"
          src="@/assets/icons/submitError.svg"
          class="mr-[0.1rem] w-[0.6rem]"
        />
        <span>{{ authStore.i18n("cm_common.linkExpired") }}</span>
      </div>
      <div class="text-center">
        <n-button
          color="#E50113"
          text-color="#fff"
          @click="resendVerification(true)"
          class="rounded-[0.16rem] w-[5.2rem] h-[0.76rem] text-[0.32rem] mt-[0.6rem] mx-auto"
        >
          <div>{{ authStore.i18n("cm_common_emailActivateAgain") }}</div>
        </n-button>
      </div>

      <div class="border-t-1 mt-[0.6rem]">
        <div class="mt-[0.4rem] mb-[0.24rem] text-[0.36rem] font-medium">
          {{ authStore.i18n("cm_common.verificationEmail") }}
        </div>
        <ul>
          <n-space vertical :style="{ gap: '0.12rem 0' }">
            <li>
              {{ authStore.i18n("cm_common.spamCheck") }}
            </li>
            <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
            <li>
              {{ authStore.i18n("cm_common.verificationDelay") }}

              <span
                class="text-[#636ded] cursor-pointer"
                @click="resendVerification"
              >
                {{ authStore.i18n("cm_common.resendVerification") }}
              </span>
            </li>
          </n-space>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  scene: null,
  verifyResult: null,
  loading: false,
  showErrorPage: false,
});

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onVerifyMail();
// 激活邮箱链接
async function onVerifyMail() {
  const res: any = await useVerifyMail({
    id: route?.query?.verifyId,
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (pageData.verifyResult !== "EXPIRED") {
      window?.MyStat?.addPageEvent(
        "passport_mail_verify_success",
        `邮箱验证成功：${pageData.verifyResult}`,
        true
      ); // 埋点
      navigateTo("/h5/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_mail_verify_error",
        `邮箱验证失败：${pageData.verifyResult}`
      ); // 埋点
      pageData.showErrorPage = true;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_mail_verify_error",
      `邮箱验证失败：${res?.result?.message}`
    ); // 埋点
    pageData.showErrorPage = true;
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification(isGoEmail?: boolean) {
  if (isGoEmail) {
    pageData.loading = true;
  }
  const res: any = await useSendVerifyMail({
    verifyMailScene: pageData.scene,
  });
  pageData.loading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/h5/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
      if (isGoEmail) {
        // 跳转邮箱
        navigateToEmail();
      }
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>
<style scoped lang="scss">
ul {
  list-style-type: disc;
  padding-left: 0.32rem;
}
</style>
