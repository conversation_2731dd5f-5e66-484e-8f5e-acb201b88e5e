/* eslint-disable */

export const protobufPackage = "mall.commodity";

export enum GoodsListFilterType {
  GOODS_LIST_FILTER_TYPE_UNSPECIFIED = 0,
  /** GOODS_LIST_FILTER_TYPE_CATEGORY - 商品分类过滤 */
  GOODS_LIST_FILTER_TYPE_CATEGORY = 10,
  /** GOODS_LIST_FILTER_TYPE_PRICE - 价格范围过滤 */
  GOODS_LIST_FILTER_TYPE_PRICE = 20,
  /** GOODS_LIST_FILTER_TYPE_MIN_BUY - 最少购买过滤 */
  GOODS_LIST_FILTER_TYPE_MIN_BUY = 30,
  UNRECOGNIZED = -1,
}

export function goodsListFilterTypeFromJSON(object: any): GoodsListFilterType {
  switch (object) {
    case 0:
    case "GOODS_LIST_FILTER_TYPE_UNSPECIFIED":
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_UNSPECIFIED;
    case 10:
    case "GOODS_LIST_FILTER_TYPE_CATEGORY":
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_CATEGORY;
    case 20:
    case "GOODS_LIST_FILTER_TYPE_PRICE":
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_PRICE;
    case 30:
    case "GOODS_LIST_FILTER_TYPE_MIN_BUY":
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_MIN_BUY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsListFilterType.UNRECOGNIZED;
  }
}

export function goodsListFilterTypeToJSON(object: GoodsListFilterType): string {
  switch (object) {
    case GoodsListFilterType.GOODS_LIST_FILTER_TYPE_UNSPECIFIED:
      return "GOODS_LIST_FILTER_TYPE_UNSPECIFIED";
    case GoodsListFilterType.GOODS_LIST_FILTER_TYPE_CATEGORY:
      return "GOODS_LIST_FILTER_TYPE_CATEGORY";
    case GoodsListFilterType.GOODS_LIST_FILTER_TYPE_PRICE:
      return "GOODS_LIST_FILTER_TYPE_PRICE";
    case GoodsListFilterType.GOODS_LIST_FILTER_TYPE_MIN_BUY:
      return "GOODS_LIST_FILTER_TYPE_MIN_BUY";
    case GoodsListFilterType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
