syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

// 商品库存列表查询结果
message GoodsStockPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsStockPageDataModel data = 3;
}

// 商品库存列表信息
message GoodsStockPageDataModel {
    string id = 10; //商品ID
    string goodsNo = 20; // 商品编号-主数据
    string goodsName = 25; // 商品名称-主数据
    string categoryName = 30; // 商品分类
    string mainImageUrl = 35; //商品主图
    GoodsOnlineState onlineState = 39; // 上架状态
    string onlineStateDesc = 40; // 上架状态描述
    int32 salesQty = 50; // 销量按商品合计（含未支付单，不含取消单）
    int32 localRealStockQty = 60; // 本地交期实有库存数量按商品合计
    int32 chinaRealStockQty = 70; // 中国交期实有库存数量按商品合计
}


// 商品SKU库存消息查询结果
message GoodsStockDetailResp {
    common.Result result = 1;
    GoodsStockDetailModel data = 2;
}

// 商品规格信息
message GoodsStockDetailModel {
    string id = 1; //商品ID
    string goodsNo = 3; // 商品编号-主数据
    string goodsName = 6; // 商品名称-主数据
    string mainImageUrl = 8; //商品主图
    repeated GoodsSkuStockModel skuList = 9; // 商品SKU信息
}

// 商品SKU库存信息
message GoodsSkuStockModel {
    string id = 10; // SKU ID（必填）
    string skuNo = 20; // SKU商品料号（必填）
    repeated common.NameValueModel specNames = 40; //SKU名称（name: 规格名称，value: 规格值）
    int32 salesQty = 50; // 销量（含未支付单，不含取消单）
    int32 realStockQty = 60; // 实有库存数量
    int32 availableStockQty = 70; // 可用库存数量
    int32 freezeStockQty = 80; // 冻结库存数量
}

// 商品SKU库存消息查询结果
message GoodsSkuStockResp {
    common.Result result = 1;
    GoodsSkuStockModel data = 2;
}
