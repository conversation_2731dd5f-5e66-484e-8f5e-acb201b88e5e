/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { ApiResult, EmptyParam, IdParam } from "../../common";
import {
  InvitedUserMailStatusResp,
  QueryVerifyMailResultResp,
  SendVerifyMailResp,
  UserAddressResp,
  UserDetailResp,
  VerifyMailResp,
} from "./model/user_model";
import {
  InvitedUserMailStatusParam,
  queryVerifyMailResultParam,
  SaveUserAddressParam,
  SendVerifyMailParam,
  UpdatePasswordParam,
} from "./param/user_param";

export const protobufPackage = "mall.passport";

/** 用户 */
export interface User {
  /** 查询用户详情 */
  detail(request: EmptyParam): Promise<UserDetailResp>;
  /** 修改密码 */
  updatePassword(request: UpdatePasswordParam): Promise<ApiResult>;
  /** 保存地址 */
  saveUserAddress(request: SaveUserAddressParam): Promise<ApiResult>;
  /** 查询所有地址 */
  listUserAddress(request: EmptyParam): Promise<UserAddressResp>;
  /** 设为默认 */
  addressToDefault(request: IdParam): Promise<ApiResult>;
  /** 删除地址 */
  deleteUserAddress(request: IdParam): Promise<ApiResult>;
  /** 发送验证邮箱的邮件 */
  sendVerifyMail(request: SendVerifyMailParam): Promise<SendVerifyMailResp>;
  /** 查询邮箱是否已验证, 以及验证后可以得到的优惠券----入参email */
  queryVerifyMailResult(request: queryVerifyMailResultParam): Promise<QueryVerifyMailResultResp>;
  /** 点击激活链接 ---- 入参为激活链接上的code参数 */
  verifyMail(request: IdParam): Promise<VerifyMailResp>;
  /** 查询被邀请好友用户的邮箱验证情况 */
  invitedUserMailStatus(request: InvitedUserMailStatusParam): Promise<InvitedUserMailStatusResp>;
}

export const UserServiceName = "mall.passport.User";
export class UserClientImpl implements User {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || UserServiceName;
    this.rpc = rpc;
    this.detail = this.detail.bind(this);
    this.updatePassword = this.updatePassword.bind(this);
    this.saveUserAddress = this.saveUserAddress.bind(this);
    this.listUserAddress = this.listUserAddress.bind(this);
    this.addressToDefault = this.addressToDefault.bind(this);
    this.deleteUserAddress = this.deleteUserAddress.bind(this);
    this.sendVerifyMail = this.sendVerifyMail.bind(this);
    this.queryVerifyMailResult = this.queryVerifyMailResult.bind(this);
    this.verifyMail = this.verifyMail.bind(this);
    this.invitedUserMailStatus = this.invitedUserMailStatus.bind(this);
  }
  detail(request: EmptyParam): Promise<UserDetailResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "detail", data);
    return promise.then((data) => UserDetailResp.decode(_m0.Reader.create(data)));
  }

  updatePassword(request: UpdatePasswordParam): Promise<ApiResult> {
    const data = UpdatePasswordParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "updatePassword", data);
    return promise.then((data) => ApiResult.decode(_m0.Reader.create(data)));
  }

  saveUserAddress(request: SaveUserAddressParam): Promise<ApiResult> {
    const data = SaveUserAddressParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "saveUserAddress", data);
    return promise.then((data) => ApiResult.decode(_m0.Reader.create(data)));
  }

  listUserAddress(request: EmptyParam): Promise<UserAddressResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "listUserAddress", data);
    return promise.then((data) => UserAddressResp.decode(_m0.Reader.create(data)));
  }

  addressToDefault(request: IdParam): Promise<ApiResult> {
    const data = IdParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "addressToDefault", data);
    return promise.then((data) => ApiResult.decode(_m0.Reader.create(data)));
  }

  deleteUserAddress(request: IdParam): Promise<ApiResult> {
    const data = IdParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "deleteUserAddress", data);
    return promise.then((data) => ApiResult.decode(_m0.Reader.create(data)));
  }

  sendVerifyMail(request: SendVerifyMailParam): Promise<SendVerifyMailResp> {
    const data = SendVerifyMailParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "sendVerifyMail", data);
    return promise.then((data) => SendVerifyMailResp.decode(_m0.Reader.create(data)));
  }

  queryVerifyMailResult(request: queryVerifyMailResultParam): Promise<QueryVerifyMailResultResp> {
    const data = queryVerifyMailResultParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "queryVerifyMailResult", data);
    return promise.then((data) => QueryVerifyMailResultResp.decode(_m0.Reader.create(data)));
  }

  verifyMail(request: IdParam): Promise<VerifyMailResp> {
    const data = IdParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "verifyMail", data);
    return promise.then((data) => VerifyMailResp.decode(_m0.Reader.create(data)));
  }

  invitedUserMailStatus(request: InvitedUserMailStatusParam): Promise<InvitedUserMailStatusResp> {
    const data = InvitedUserMailStatusParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "invitedUserMailStatus", data);
    return promise.then((data) => InvitedUserMailStatusResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
