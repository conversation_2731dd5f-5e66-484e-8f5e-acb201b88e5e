syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing";

//用户类型（10:批发，20:零售，25:网店，30:自用，40:其它，0:未定义）
enum PotentialUserType {
    POTENTIAL_USER_TYPE_UNSPECIFIED = 0; //未定义
    POTENTIAL_USER_TYPE_WHOLESALE = 10; //批发
    POTENTIAL_USER_TYPE_RETAIL = 20; //零售
    POTENTIAL_USER_TYPE_ONLINE_SHOP = 25; //网店
    POTENTIAL_USER_TYPE_SELF_USE = 30; //自用
    POTENTIAL_USER_TYPE_OTHER = 40; //其它
}

//服务类型（10:来访，20:在线选品，30:定制选品，40:其它，0:未定义）
enum PotentialServiceType {
    POTENTIAL_SERVICE_TYPE_UNSPECIFIED = 0; //未定义
    POTENTIAL_SERVICE_TYPE_VISIT_CHINA = 10; //来访
    POTENTIAL_SERVICE_TYPE_ONLINE_BUY = 20; //在线选品
    POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT = 30; //定制选品
    POTENTIAL_SERVICE_TYPE_OTHER = 40; //其它（保留）
}

//采购数量（10:散货，20:整柜，30:不确定，0:未定义）
enum PotentialPurchaseQuantity {
    POTENTIAL_PURCHASE_QUANTITY_UNSPECIFIED = 0; //未定义
    POTENTIAL_PURCHASE_QUANTITY_LCL = 10; //散货（拼箱） - Less than Container Load
    POTENTIAL_PURCHASE_QUANTITY_FCL = 20; //整柜 - Full Container Load
    POTENTIAL_PURCHASE_QUANTITY_NOT_SURE = 30; //不确定
}
