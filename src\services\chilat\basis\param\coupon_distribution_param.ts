/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import {
  TicketSceneStatus,
  ticketSceneStatusFromJSON,
  ticketSceneStatusToJSON,
} from "../../coupon/coupon_detail_common";

export const protobufPackage = "chilat.basis";

/** 优惠券发放 */
export interface CouponDistributionParam {
  /** 优惠券ID */
  couponId: string;
  /** 用户id */
  userId: string;
  /** 发放优惠券场景 */
  ticketSceneStatus: TicketSceneStatus;
}

function createBaseCouponDistributionParam(): CouponDistributionParam {
  return { couponId: "", userId: "", ticketSceneStatus: 0 };
}

export const CouponDistributionParam = {
  encode(message: CouponDistributionParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.couponId !== "") {
      writer.uint32(10).string(message.couponId);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.ticketSceneStatus !== 0) {
      writer.uint32(24).int32(message.ticketSceneStatus);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponDistributionParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponDistributionParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.couponId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.ticketSceneStatus = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponDistributionParam {
    return {
      couponId: isSet(object.couponId) ? globalThis.String(object.couponId) : "",
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      ticketSceneStatus: isSet(object.ticketSceneStatus) ? ticketSceneStatusFromJSON(object.ticketSceneStatus) : 0,
    };
  },

  toJSON(message: CouponDistributionParam): unknown {
    const obj: any = {};
    if (message.couponId !== "") {
      obj.couponId = message.couponId;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.ticketSceneStatus !== 0) {
      obj.ticketSceneStatus = ticketSceneStatusToJSON(message.ticketSceneStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponDistributionParam>, I>>(base?: I): CouponDistributionParam {
    return CouponDistributionParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponDistributionParam>, I>>(object: I): CouponDistributionParam {
    const message = createBaseCouponDistributionParam();
    message.couponId = object.couponId ?? "";
    message.userId = object.userId ?? "";
    message.ticketSceneStatus = object.ticketSceneStatus ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
