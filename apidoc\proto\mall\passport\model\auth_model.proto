syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport.model";

import "common.proto";


// 登录返回信息
message AuthLoginResp {
    common.Result result = 1;
    AuthLoginModel data = 2;
}

// 登录结果
message AuthLoginModel {
    string userId = 1; // 用户ID
    string username = 2; // 用户名
    string token = 3; // 登录令牌
}

// 登录用户信息
message LoginUserModel {
    string userId = 1; // 用户ID
    string username = 2; // 用户名
}
