syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/foundation/param/warehouse_param.proto";
import "chilat/foundation/model/warehouse_model.proto";
import "common.proto";

// 仓库管理
service Warehouse {
  // 仓库列表
  rpc list (WarehouseListParam) returns (WarehouseListResp);
  // 保存仓库
  rpc save (WarehouseSaveParam) returns (WarehouseResp);
  // 启用仓库
  rpc enable (common.EnabledParam) returns (common.ApiResult);
}