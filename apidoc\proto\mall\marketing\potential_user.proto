syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing";

import "common.proto";
import "mall/marketing/model/potential_user_model.proto";
import "mall/marketing/param/potential_user_param.proto";

service PotentialUser {
  // 保存潜客信息
  rpc saveUserInfo (PotentialUserSaveParam) returns (PotentialUserSaveResp);
  // 直接点击whatsapp
  rpc clickWhatsapp (PotentialUserClickParam) returns (PotentialUserClickResp);
}
