/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";
import Long from "long";

export const protobufPackage = "chilat.basis";

export interface QueryInquiryResp {
  result: Result | undefined;
  data: InquiryModel | undefined;
}

export interface InquiryModel {
  /** 询盘单号 */
  goodsLookingNo: string;
  /** 询盘总数量 */
  totalGoodsCount: number;
  /** 询盘总价 */
  subTotal: number;
  /** 询盘商品 */
  skus: InquirySkuModel[];
  /** 客户备注 */
  remark: string;
  /** id */
  id: string;
  /** 询盘时间 */
  submitTime: number;
  /** 国家 */
  countryName: string;
  /** whatsapp */
  whatsapp: string;
  /** 姓名 */
  submitName: string;
  /** 邮箱 */
  email: string;
  /** 邮编 */
  postcode: string;
  /** 地址 */
  address: string;
  /** 国家 */
  countryId: string;
  /** 区号 */
  areaCode: string;
}

export interface InquirySkuModel {
  /** 商品id */
  goodsId: string;
  /** 商品名称 */
  goodsName: string;
  /** sku图片 */
  skuImage: string;
  /** 购买数量 */
  buyQuantity: number;
  /** 价格单位 */
  priceUnitName: string;
  /** 销售价 */
  salePrice: number;
  /** 销售总价 */
  totalSalePrice: number;
  /** 供应商价格 */
  supplierPrice: number;
  /** 货源链接 */
  supplyLink: string;
  /** 包装含量 */
  packageInsideCount: number;
  /** 长 */
  goodsLength: number;
  /** 宽 */
  goodsWidth: number;
  /** 高 */
  goodsHeight: number;
  /** 重量 */
  goodsWeight: number;
  /** specMap */
  specMap: { [key: string]: SpecModel };
  /** 规格值 */
  specs: SkuSpecModel[];
}

export interface InquirySkuModel_SpecMapEntry {
  key: string;
  value: SpecModel | undefined;
}

export interface specGroupModel {
  groupName: string;
  specList: SpecModel[];
}

export interface SkuSpecModel {
  /** 规格ID */
  specId: string;
  /** 规格名称 */
  specName: string;
  /** 规格值ID */
  itemId: string;
  /** 规格值 */
  itemName: string;
  /** 图片 */
  imageUrl: string;
  /** 颜色 */
  color: string;
}

export interface SpecModel {
  specName: string[];
}

export interface GoodsLookingPageListResp {
  result: Result | undefined;
  page: Page | undefined;
  data: InquiryModel[];
}

function createBaseQueryInquiryResp(): QueryInquiryResp {
  return { result: undefined, data: undefined };
}

export const QueryInquiryResp = {
  encode(
    message: QueryInquiryResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      InquiryModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryInquiryResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryInquiryResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = InquiryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryInquiryResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? InquiryModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: QueryInquiryResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = InquiryModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryInquiryResp>, I>>(
    base?: I
  ): QueryInquiryResp {
    return QueryInquiryResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryInquiryResp>, I>>(
    object: I
  ): QueryInquiryResp {
    const message = createBaseQueryInquiryResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? InquiryModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseInquiryModel(): InquiryModel {
  return {
    goodsLookingNo: "",
    totalGoodsCount: 0,
    subTotal: 0,
    skus: [],
    remark: "",
    id: "",
    submitTime: 0,
    countryName: "",
    whatsapp: "",
    submitName: "",
    email: "",
    postcode: "",
    address: "",
    countryId: "",
    areaCode: "",
  };
}

export const InquiryModel = {
  encode(
    message: InquiryModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.goodsLookingNo !== "") {
      writer.uint32(10).string(message.goodsLookingNo);
    }
    if (message.totalGoodsCount !== 0) {
      writer.uint32(16).int32(message.totalGoodsCount);
    }
    if (message.subTotal !== 0) {
      writer.uint32(25).double(message.subTotal);
    }
    for (const v of message.skus) {
      InquirySkuModel.encode(v!, writer.uint32(34).fork()).ldelim();
    }
    if (message.remark !== "") {
      writer.uint32(42).string(message.remark);
    }
    if (message.id !== "") {
      writer.uint32(50).string(message.id);
    }
    if (message.submitTime !== 0) {
      writer.uint32(56).int64(message.submitTime);
    }
    if (message.countryName !== "") {
      writer.uint32(66).string(message.countryName);
    }
    if (message.whatsapp !== "") {
      writer.uint32(74).string(message.whatsapp);
    }
    if (message.submitName !== "") {
      writer.uint32(82).string(message.submitName);
    }
    if (message.email !== "") {
      writer.uint32(90).string(message.email);
    }
    if (message.postcode !== "") {
      writer.uint32(98).string(message.postcode);
    }
    if (message.address !== "") {
      writer.uint32(106).string(message.address);
    }
    if (message.countryId !== "") {
      writer.uint32(114).string(message.countryId);
    }
    if (message.areaCode !== "") {
      writer.uint32(122).string(message.areaCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): InquiryModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInquiryModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.goodsLookingNo = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.totalGoodsCount = reader.int32();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.subTotal = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.skus.push(InquirySkuModel.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.remark = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.id = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.submitTime = longToNumber(reader.int64() as Long);
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.countryName = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.whatsapp = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.submitName = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.email = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.postcode = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.address = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.countryId = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.areaCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InquiryModel {
    return {
      goodsLookingNo: isSet(object.goodsLookingNo)
        ? globalThis.String(object.goodsLookingNo)
        : "",
      totalGoodsCount: isSet(object.totalGoodsCount)
        ? globalThis.Number(object.totalGoodsCount)
        : 0,
      subTotal: isSet(object.subTotal) ? globalThis.Number(object.subTotal) : 0,
      skus: globalThis.Array.isArray(object?.skus)
        ? object.skus.map((e: any) => InquirySkuModel.fromJSON(e))
        : [],
      remark: isSet(object.remark) ? globalThis.String(object.remark) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      submitTime: isSet(object.submitTime)
        ? globalThis.Number(object.submitTime)
        : 0,
      countryName: isSet(object.countryName)
        ? globalThis.String(object.countryName)
        : "",
      whatsapp: isSet(object.whatsapp)
        ? globalThis.String(object.whatsapp)
        : "",
      submitName: isSet(object.submitName)
        ? globalThis.String(object.submitName)
        : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      postcode: isSet(object.postcode)
        ? globalThis.String(object.postcode)
        : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      countryId: isSet(object.countryId)
        ? globalThis.String(object.countryId)
        : "",
      areaCode: isSet(object.areaCode)
        ? globalThis.String(object.areaCode)
        : "",
    };
  },

  toJSON(message: InquiryModel): unknown {
    const obj: any = {};
    if (message.goodsLookingNo !== "") {
      obj.goodsLookingNo = message.goodsLookingNo;
    }
    if (message.totalGoodsCount !== 0) {
      obj.totalGoodsCount = Math.round(message.totalGoodsCount);
    }
    if (message.subTotal !== 0) {
      obj.subTotal = message.subTotal;
    }
    if (message.skus?.length) {
      obj.skus = message.skus.map((e) => InquirySkuModel.toJSON(e));
    }
    if (message.remark !== "") {
      obj.remark = message.remark;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.submitTime !== 0) {
      obj.submitTime = Math.round(message.submitTime);
    }
    if (message.countryName !== "") {
      obj.countryName = message.countryName;
    }
    if (message.whatsapp !== "") {
      obj.whatsapp = message.whatsapp;
    }
    if (message.submitName !== "") {
      obj.submitName = message.submitName;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.postcode !== "") {
      obj.postcode = message.postcode;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.countryId !== "") {
      obj.countryId = message.countryId;
    }
    if (message.areaCode !== "") {
      obj.areaCode = message.areaCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InquiryModel>, I>>(
    base?: I
  ): InquiryModel {
    return InquiryModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InquiryModel>, I>>(
    object: I
  ): InquiryModel {
    const message = createBaseInquiryModel();
    message.goodsLookingNo = object.goodsLookingNo ?? "";
    message.totalGoodsCount = object.totalGoodsCount ?? 0;
    message.subTotal = object.subTotal ?? 0;
    message.skus =
      object.skus?.map((e) => InquirySkuModel.fromPartial(e)) || [];
    message.remark = object.remark ?? "";
    message.id = object.id ?? "";
    message.submitTime = object.submitTime ?? 0;
    message.countryName = object.countryName ?? "";
    message.whatsapp = object.whatsapp ?? "";
    message.submitName = object.submitName ?? "";
    message.email = object.email ?? "";
    message.postcode = object.postcode ?? "";
    message.address = object.address ?? "";
    message.countryId = object.countryId ?? "";
    message.areaCode = object.areaCode ?? "";
    return message;
  },
};

function createBaseInquirySkuModel(): InquirySkuModel {
  return {
    goodsId: "",
    goodsName: "",
    skuImage: "",
    buyQuantity: 0,
    priceUnitName: "",
    salePrice: 0,
    totalSalePrice: 0,
    supplierPrice: 0,
    supplyLink: "",
    packageInsideCount: 0,
    goodsLength: 0,
    goodsWidth: 0,
    goodsHeight: 0,
    goodsWeight: 0,
    specMap: {},
    specs: [],
  };
}

export const InquirySkuModel = {
  encode(
    message: InquirySkuModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(10).string(message.goodsId);
    }
    if (message.goodsName !== "") {
      writer.uint32(18).string(message.goodsName);
    }
    if (message.skuImage !== "") {
      writer.uint32(26).string(message.skuImage);
    }
    if (message.buyQuantity !== 0) {
      writer.uint32(32).int32(message.buyQuantity);
    }
    if (message.priceUnitName !== "") {
      writer.uint32(42).string(message.priceUnitName);
    }
    if (message.salePrice !== 0) {
      writer.uint32(49).double(message.salePrice);
    }
    if (message.totalSalePrice !== 0) {
      writer.uint32(57).double(message.totalSalePrice);
    }
    if (message.supplierPrice !== 0) {
      writer.uint32(65).double(message.supplierPrice);
    }
    if (message.supplyLink !== "") {
      writer.uint32(74).string(message.supplyLink);
    }
    if (message.packageInsideCount !== 0) {
      writer.uint32(80).int32(message.packageInsideCount);
    }
    if (message.goodsLength !== 0) {
      writer.uint32(89).double(message.goodsLength);
    }
    if (message.goodsWidth !== 0) {
      writer.uint32(97).double(message.goodsWidth);
    }
    if (message.goodsHeight !== 0) {
      writer.uint32(105).double(message.goodsHeight);
    }
    if (message.goodsWeight !== 0) {
      writer.uint32(113).double(message.goodsWeight);
    }
    Object.entries(message.specMap).forEach(([key, value]) => {
      InquirySkuModel_SpecMapEntry.encode(
        { key: key as any, value },
        writer.uint32(122).fork()
      ).ldelim();
    });
    for (const v of message.specs) {
      SkuSpecModel.encode(v!, writer.uint32(130).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): InquirySkuModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInquirySkuModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.skuImage = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.buyQuantity = reader.int32();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.priceUnitName = reader.string();
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }

          message.salePrice = reader.double();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }

          message.totalSalePrice = reader.double();
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }

          message.supplierPrice = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.supplyLink = reader.string();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.packageInsideCount = reader.int32();
          continue;
        case 11:
          if (tag !== 89) {
            break;
          }

          message.goodsLength = reader.double();
          continue;
        case 12:
          if (tag !== 97) {
            break;
          }

          message.goodsWidth = reader.double();
          continue;
        case 13:
          if (tag !== 105) {
            break;
          }

          message.goodsHeight = reader.double();
          continue;
        case 14:
          if (tag !== 113) {
            break;
          }

          message.goodsWeight = reader.double();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          const entry15 = InquirySkuModel_SpecMapEntry.decode(
            reader,
            reader.uint32()
          );
          if (entry15.value !== undefined) {
            message.specMap[entry15.key] = entry15.value;
          }
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.specs.push(SkuSpecModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InquirySkuModel {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      goodsName: isSet(object.goodsName)
        ? globalThis.String(object.goodsName)
        : "",
      skuImage: isSet(object.skuImage)
        ? globalThis.String(object.skuImage)
        : "",
      buyQuantity: isSet(object.buyQuantity)
        ? globalThis.Number(object.buyQuantity)
        : 0,
      priceUnitName: isSet(object.priceUnitName)
        ? globalThis.String(object.priceUnitName)
        : "",
      salePrice: isSet(object.salePrice)
        ? globalThis.Number(object.salePrice)
        : 0,
      totalSalePrice: isSet(object.totalSalePrice)
        ? globalThis.Number(object.totalSalePrice)
        : 0,
      supplierPrice: isSet(object.supplierPrice)
        ? globalThis.Number(object.supplierPrice)
        : 0,
      supplyLink: isSet(object.supplyLink)
        ? globalThis.String(object.supplyLink)
        : "",
      packageInsideCount: isSet(object.packageInsideCount)
        ? globalThis.Number(object.packageInsideCount)
        : 0,
      goodsLength: isSet(object.goodsLength)
        ? globalThis.Number(object.goodsLength)
        : 0,
      goodsWidth: isSet(object.goodsWidth)
        ? globalThis.Number(object.goodsWidth)
        : 0,
      goodsHeight: isSet(object.goodsHeight)
        ? globalThis.Number(object.goodsHeight)
        : 0,
      goodsWeight: isSet(object.goodsWeight)
        ? globalThis.Number(object.goodsWeight)
        : 0,
      specMap: isObject(object.specMap)
        ? Object.entries(object.specMap).reduce<{ [key: string]: SpecModel }>(
            (acc, [key, value]) => {
              acc[key] = SpecModel.fromJSON(value);
              return acc;
            },
            {}
          )
        : {},
      specs: globalThis.Array.isArray(object?.specs)
        ? object.specs.map((e: any) => SkuSpecModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InquirySkuModel): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.skuImage !== "") {
      obj.skuImage = message.skuImage;
    }
    if (message.buyQuantity !== 0) {
      obj.buyQuantity = Math.round(message.buyQuantity);
    }
    if (message.priceUnitName !== "") {
      obj.priceUnitName = message.priceUnitName;
    }
    if (message.salePrice !== 0) {
      obj.salePrice = message.salePrice;
    }
    if (message.totalSalePrice !== 0) {
      obj.totalSalePrice = message.totalSalePrice;
    }
    if (message.supplierPrice !== 0) {
      obj.supplierPrice = message.supplierPrice;
    }
    if (message.supplyLink !== "") {
      obj.supplyLink = message.supplyLink;
    }
    if (message.packageInsideCount !== 0) {
      obj.packageInsideCount = Math.round(message.packageInsideCount);
    }
    if (message.goodsLength !== 0) {
      obj.goodsLength = message.goodsLength;
    }
    if (message.goodsWidth !== 0) {
      obj.goodsWidth = message.goodsWidth;
    }
    if (message.goodsHeight !== 0) {
      obj.goodsHeight = message.goodsHeight;
    }
    if (message.goodsWeight !== 0) {
      obj.goodsWeight = message.goodsWeight;
    }
    if (message.specMap) {
      const entries = Object.entries(message.specMap);
      if (entries.length > 0) {
        obj.specMap = {};
        entries.forEach(([k, v]) => {
          obj.specMap[k] = SpecModel.toJSON(v);
        });
      }
    }
    if (message.specs?.length) {
      obj.specs = message.specs.map((e) => SkuSpecModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InquirySkuModel>, I>>(
    base?: I
  ): InquirySkuModel {
    return InquirySkuModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InquirySkuModel>, I>>(
    object: I
  ): InquirySkuModel {
    const message = createBaseInquirySkuModel();
    message.goodsId = object.goodsId ?? "";
    message.goodsName = object.goodsName ?? "";
    message.skuImage = object.skuImage ?? "";
    message.buyQuantity = object.buyQuantity ?? 0;
    message.priceUnitName = object.priceUnitName ?? "";
    message.salePrice = object.salePrice ?? 0;
    message.totalSalePrice = object.totalSalePrice ?? 0;
    message.supplierPrice = object.supplierPrice ?? 0;
    message.supplyLink = object.supplyLink ?? "";
    message.packageInsideCount = object.packageInsideCount ?? 0;
    message.goodsLength = object.goodsLength ?? 0;
    message.goodsWidth = object.goodsWidth ?? 0;
    message.goodsHeight = object.goodsHeight ?? 0;
    message.goodsWeight = object.goodsWeight ?? 0;
    message.specMap = Object.entries(object.specMap ?? {}).reduce<{
      [key: string]: SpecModel;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SpecModel.fromPartial(value);
      }
      return acc;
    }, {});
    message.specs = object.specs?.map((e) => SkuSpecModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseInquirySkuModel_SpecMapEntry(): InquirySkuModel_SpecMapEntry {
  return { key: "", value: undefined };
}

export const InquirySkuModel_SpecMapEntry = {
  encode(
    message: InquirySkuModel_SpecMapEntry,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SpecModel.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): InquirySkuModel_SpecMapEntry {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInquirySkuModel_SpecMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = SpecModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InquirySkuModel_SpecMapEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SpecModel.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: InquirySkuModel_SpecMapEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SpecModel.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InquirySkuModel_SpecMapEntry>, I>>(
    base?: I
  ): InquirySkuModel_SpecMapEntry {
    return InquirySkuModel_SpecMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InquirySkuModel_SpecMapEntry>, I>>(
    object: I
  ): InquirySkuModel_SpecMapEntry {
    const message = createBaseInquirySkuModel_SpecMapEntry();
    message.key = object.key ?? "";
    message.value =
      object.value !== undefined && object.value !== null
        ? SpecModel.fromPartial(object.value)
        : undefined;
    return message;
  },
};

function createBasespecGroupModel(): specGroupModel {
  return { groupName: "", specList: [] };
}

export const specGroupModel = {
  encode(
    message: specGroupModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.groupName !== "") {
      writer.uint32(18).string(message.groupName);
    }
    for (const v of message.specList) {
      SpecModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): specGroupModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasespecGroupModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2:
          if (tag !== 18) {
            break;
          }

          message.groupName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.specList.push(SpecModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): specGroupModel {
    return {
      groupName: isSet(object.groupName)
        ? globalThis.String(object.groupName)
        : "",
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => SpecModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: specGroupModel): unknown {
    const obj: any = {};
    if (message.groupName !== "") {
      obj.groupName = message.groupName;
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) => SpecModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<specGroupModel>, I>>(
    base?: I
  ): specGroupModel {
    return specGroupModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<specGroupModel>, I>>(
    object: I
  ): specGroupModel {
    const message = createBasespecGroupModel();
    message.groupName = object.groupName ?? "";
    message.specList =
      object.specList?.map((e) => SpecModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSkuSpecModel(): SkuSpecModel {
  return {
    specId: "",
    specName: "",
    itemId: "",
    itemName: "",
    imageUrl: "",
    color: "",
  };
}

export const SkuSpecModel = {
  encode(
    message: SkuSpecModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.specId !== "") {
      writer.uint32(10).string(message.specId);
    }
    if (message.specName !== "") {
      writer.uint32(18).string(message.specName);
    }
    if (message.itemId !== "") {
      writer.uint32(26).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(34).string(message.itemName);
    }
    if (message.imageUrl !== "") {
      writer.uint32(42).string(message.imageUrl);
    }
    if (message.color !== "") {
      writer.uint32(50).string(message.color);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SkuSpecModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkuSpecModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.specId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.specName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.itemName = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.color = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SkuSpecModel {
    return {
      specId: isSet(object.specId) ? globalThis.String(object.specId) : "",
      specName: isSet(object.specName)
        ? globalThis.String(object.specName)
        : "",
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName)
        ? globalThis.String(object.itemName)
        : "",
      imageUrl: isSet(object.imageUrl)
        ? globalThis.String(object.imageUrl)
        : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
    };
  },

  toJSON(message: SkuSpecModel): unknown {
    const obj: any = {};
    if (message.specId !== "") {
      obj.specId = message.specId;
    }
    if (message.specName !== "") {
      obj.specName = message.specName;
    }
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SkuSpecModel>, I>>(
    base?: I
  ): SkuSpecModel {
    return SkuSpecModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuSpecModel>, I>>(
    object: I
  ): SkuSpecModel {
    const message = createBaseSkuSpecModel();
    message.specId = object.specId ?? "";
    message.specName = object.specName ?? "";
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    message.imageUrl = object.imageUrl ?? "";
    message.color = object.color ?? "";
    return message;
  },
};

function createBaseSpecModel(): SpecModel {
  return { specName: [] };
}

export const SpecModel = {
  encode(
    message: SpecModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    for (const v of message.specName) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SpecModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSpecModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2:
          if (tag !== 18) {
            break;
          }

          message.specName.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SpecModel {
    return {
      specName: globalThis.Array.isArray(object?.specName)
        ? object.specName.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SpecModel): unknown {
    const obj: any = {};
    if (message.specName?.length) {
      obj.specName = message.specName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SpecModel>, I>>(base?: I): SpecModel {
    return SpecModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpecModel>, I>>(
    object: I
  ): SpecModel {
    const message = createBaseSpecModel();
    message.specName = object.specName?.map((e) => e) || [];
    return message;
  },
};

function createBaseGoodsLookingPageListResp(): GoodsLookingPageListResp {
  return { result: undefined, page: undefined, data: [] };
}

export const GoodsLookingPageListResp = {
  encode(
    message: GoodsLookingPageListResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      InquiryModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): GoodsLookingPageListResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsLookingPageListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(InquiryModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsLookingPageListResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => InquiryModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsLookingPageListResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => InquiryModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsLookingPageListResp>, I>>(
    base?: I
  ): GoodsLookingPageListResp {
    return GoodsLookingPageListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsLookingPageListResp>, I>>(
    object: I
  ): GoodsLookingPageListResp {
    const message = createBaseGoodsLookingPageListResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.page =
      object.page !== undefined && object.page !== null
        ? Page.fromPartial(object.page)
        : undefined;
    message.data = object.data?.map((e) => InquiryModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin =
  | Date
  | Function
  | Uint8Array
  | string
  | number
  | boolean
  | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
  ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U>
  ? ReadonlyArray<DeepPartial<U>>
  : T extends {}
  ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & {
      [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
    };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
