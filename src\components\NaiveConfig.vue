<template>
  <n-config-provider
    inline-theme-disabled
    :theme="theme"
    :locale="locale"
    :date-locale="dateLocale"
    :theme-overrides="themeOverrides"
  >
    <slot />
  </n-config-provider>
</template>

<script lang="ts" setup>
import {
  useOsTheme,
  darkTheme,
  NConfigProvider,
  type GlobalThemeOverrides,
} from "naive-ui";
import { useAppStore } from "@/store/app";
import { useLangStore } from "@/store/lang";
type ColorType = "primary" | "info" | "success" | "warning" | "error";

const app = useAppStore();
const lang = useLangStore();
const osThemeRef = useOsTheme();
const theme = computed(() => (osThemeRef.value === "dark" ? darkTheme : null));
const locale = computed(() => lang.customizedLocale);
const dateLocale = computed(() => lang.customizedDateLocale);
const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: "#ff5722",
    // borderColor: "#ff5722",
  },
  Button: {
    colorPrimary: "#ff5722",
    colorHoverPrimary: "#ff5722",
    colorPressedPrimary: "#ff5722",
    colorFocusPrimary: "#ff5722",
    colorDisabledPrimary: "#ff5722",
    textColorDisabledPrimary: "#ff5722",
    textColorTextPrimary: "#ff5722",
    textColorTextHoverPrimary: "#ff5722",
    textColorTextFocusPrimary: "#ff5722",
    textColorTextDisabledPrimary: "#ff5722",
    textColorGhostHoverPrimary: "#ff5722",
    textColorGhostPressedPrimary: "#ff5722",
    borderHoverPrimary: "#ff5722",
    borderPressedPrimary: "#ff2922",
    borderFocusPrimary: "#ff5722",
    rippleColorPrimary: "#ff2922",
    textColorTextHover: "#ff2922",
  },
};
</script>
