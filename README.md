# Chilat2MallSRR

### 参考

- [TikTok 转化追踪](https://ads.tiktok.com/gateway/docs/index?identify_key=2b9b4278e47b275f36e7c39a4af4ba067d088e031d5f5fe45d381559ac89ba48&language=ENGLISH&doc_id=1701890973258754#item-link-What%20are%20event%20codes?)
- [Facebook 转化追踪](https://developers.facebook.com/docs/meta-pixel/implementation/conversion-tracking#custom-events)

### 移动端响应式布局

- 为了确保在不同屏幕尺寸的移动设备上能够有良好的显示效果，移动端采用了动态计算根元素 font-size 值，并使用 rem 单位进行适配的方案。

- 采取的方案
  1、动态插入适配的 JavaScript：
  -- 在 nuxt.config.ts 文件中，通过判断是否是移动端设备，在 head 头部动态插入适配的 JavaScript 代码。
  -- 该 JavaScript 代码会根据屏幕宽度动态计算根元素的 font-size。
  2、使用 rem 单位：
  -- 在样式中使用 rem 单位进行布局和样式设计。例如，假设某元素的 font-size 为 14px，在 375px 宽度的屏幕上，动态计算的根元素 font-size 为 50px，则计算后的 rem 值为 14/50 = 0.28。最终的代码为：font-size: 0.28rem。
  3、动态计算 px 值：
  -- 对于某些只能使用 px 单位的组件，需要获取根元素的 font-size，并根据该值动态计算并赋值。例如，可以参考 resetFontSize 函数的实现来动态获取并计算 px 值。

### vite 打包优化

- vite-plugin-imagemin 基于 imagemin 进行开发的 vite 插件，可以在 build 的时候自动获取指定路径下的指定类型的图片文件并压缩
  -- 注意：imagemin 包含 pngquant 和 mozjpeg 等知名库，需要下载二进制文件。中国国内开发者因为网络原因大概率会导致安装失败，解决方式如下。
  推荐使用 resolutions 和 bin-wrapper-china 安装

### nuxt3 pwa:

- 1、npx nuxi@latest module add @vite-pwa/nuxt
- 2、将 icon、logo 等资源放到 public 目录下
- 3、nuxt.config.ts 配置 pwa;
- 4、app.vue 配置: <NuxtPwaManifest />;
- 5、重新启动项目 pnpm run dev;
- 6、验证下打包后是否正常: pnpm run generate, 是否生成--目前无法运行，可以不执行;
- 7、根据提示我们执行下 npx serve .output/public 如果没有安装 serve 的话会提示安装
- 8、执行 pnpm run build 可以看到和上边一样生成了 sw.js 文件
- 参考：https://juejin.cn/post/7386848746914480168
- 示例: https://github.com/vite-pwa/nuxt/tree/main/playground
-

## 项目参考

- [SoybeanAdmin](https://docs.soybean.pro/zh/guide/intro.html)
- [iconify](https://icon-sets.iconify.design/)

### 首页历史版本（backup）

- 2024-09-09 UI 改版
