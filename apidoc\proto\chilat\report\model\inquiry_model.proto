syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.model";

import "common.proto";

message QueryGroupByCategoryResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated GroupByCategoryModel data = 3;
}

message QueryGroupByGoodsResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated QueryGroupByGoodsModel data = 3;
}

message QueryGroupByCountryResp {
  common.Result result = 1;
  repeated QueryGroupByCountryModel data = 2;
}

message QueryGroupByCustomerResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated QueryGroupByCustomerModel data = 3;
}

message ProcessIndicatorResp {
  common.Result result = 1;
  repeated ProcessIndicatorModel data = 3;
}

message ProcessIndicatorModel {
  int32 visitorCount = 1; // 访客数
  int32 searchVisitorCount = 2; // 搜索客户数
  double searchVisitorRate = 3; // 搜索率
  int32 registerCount = 4; // 注册客户数
  double registerRate = 5; // 注册率
  int32 cartVisitorCount = 6; // 加购客户数
  double cartVisitorRate = 7; // 加购率
  int32 inquiryVisitorCount = 8; // 询盘客户数
  int32 inquiryOldUserCount = 18; // 询盘老客户数
  double inquiryVisitorRate = 9; // 询盘率
  int32 newVisitorCount = 21; // 新访客
  int32 newSearchVisitorCount = 22; // 搜索客户数
  double newSearchVisitorRate = 23; // 搜索率
  int32 newRegisterCount = 24; // 注册客户数
  double newRegisterRate = 25; // 注册率
  int32 newCartVisitorCount = 26; // 加购客户数
  double newCartVisitorRate = 27; // 加购率
  int32 newInquiryVisitorCount = 28; // 询盘客户数
  double newInquiryVisitorRate = 29; // 询盘率
  int32 oldVisitorCount = 41; // 老访客
  int32 oldSearchVisitorCount = 42; // 搜索客户数
  double oldSearchVisitorRate = 43; // 搜索率
  int32 oldRegisterCount = 44; // 注册客户数
  double oldRegisterRate = 45; // 注册率
  int32 oldCartVisitorCount = 46; // 加购客户数
  double oldCartVisitorRate = 47; // 加购率
  int32 oldInquiryVisitorCount = 48; // 询盘客户数
  double oldInquiryVisitorRate = 49; // 询盘率
  int64 visitDate = 100; // 日期
  int32 pvCount = 110; // 总访问量
  int32 bounceUV = 120; // 跳出UV
  string bounceRate = 130; // 跳出率
  int32 inquiryNewUserCount = 140; // 询盘新客户数
}

message GroupByCategoryModel {
  string categoryId = 1;
  string cateName = 2; //分类名称
  int32 goodsCount = 3; //商品数
  int32 inquiryCount = 4;  //询盘数
  double inquiryPercent = 5; //询盘数占比
  int32 customerCount = 6; //询盘客户数
  double customerPercent = 7; //询盘客户占比
  double inquiryPrice = 8;//询盘总额
}

message QueryGroupByGoodsModel {
  string goodsId = 1;
  string goodsName = 2; //商品名称
  string cateName = 3; //分类名称
  int32 inquiryCount = 4;  //询盘数
  double inquiryPercent = 5; //询盘数占比
  int32 customerCount = 6; //询盘客户数
  double customerPercent = 7; //询盘客户占比
  double inquiryPrice = 8;//询盘总额
  string goodsNo = 9; //商品编号
}

message QueryGroupByCountryModel {
  string countryId = 1;
  string countryName = 2; //国家
  int32 goodsCount = 3; //商品数
  int32 inquiryCount = 4;  //询盘数
  double inquiryPercent = 5; //询盘数占比
  int32 customerCount = 6; //询盘客户数
  double customerPercent = 7; //询盘客户占比
  double inquiryPrice = 8;//询盘总额
}

message QueryGroupByCustomerModel {
  string userId = 1;
  string username = 2; //用户名
  string whatsapp = 3;
  string countryName = 5; //国家
  int32 goodsCount = 6; //商品数
  int32 inquiryCount = 7;  //询盘数
  double inquiryPercent = 8; //询盘数占比
  double inquiryPrice = 9;//询盘总额
}