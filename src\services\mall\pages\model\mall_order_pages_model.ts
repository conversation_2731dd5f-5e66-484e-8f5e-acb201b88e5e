/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";
import {
  MallOrderStatus,
  mallOrderStatusFromJSON,
  mallOrderStatusToJSON,
  PayMode,
  payModeFromJSON,
  payModeToJSON,
  PayStatusEnum,
  payStatusEnumFromJSON,
  payStatusEnumToJSON,
  PayType,
  payTypeFromJSON,
  payTypeToJSON,
  QuotationMode,
  quotationModeFromJSON,
  quotationModeToJSON,
} from "../../../common/business";
import { SkuModel } from "../../commodity/model/goods_info_model";
import Long from "long";

export const protobufPackage = "mall.pages";

/** 获取订单列表 */
export interface GetOrderListResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 订单列表 */
  data: GetOrderListModel | undefined;
}

export interface GetOrderListModel {
  /** 分页信息 */
  page:
    | Page
    | undefined;
  /** 订单列表 */
  orderList: OrderInfoModel[];
}

export interface OrderInfoModel {
  /** 订单号 */
  orderNo: string;
  /** 订单状态 */
  mallOrderStatus: MallOrderStatus;
  /** 订单状态对应的文案描述 */
  statusDesc: string;
  /** 商品列表, 为空表示该账单不包含产品价; */
  skuList: SkuModel[];
  /** 下单时间戳 */
  orderTime: number;
}

/** 获取订单详情 */
export interface GetOrderDetailResp {
  /** 错误码 */
  result: Result | undefined;
  data: GetOrderDetailModel | undefined;
}

export interface GetOrderDetailModel {
  /** 订单号 */
  orderNo: string;
  /** 订单状态 */
  mallOrderStatus: MallOrderStatus;
  /** 订单状态对应的文案描述 */
  statusDesc: string;
  /** 下单时间,时间戳 */
  orderTime: number;
  /** 地址信息 */
  addressInfo:
    | AddressInfoModel
    | undefined;
  /** 装箱列表 */
  boxList: BoxInfoModel[];
  /** sku总数量 */
  totalCount: number;
  /** 订单备注 */
  orderRemark: string;
  /** 产品成本 */
  productAmount:
    | ProductAmountModel
    | undefined;
  /** 运输线路与预估费用列表 */
  transportAmountList: TransportAmountModel[];
  /** 线上支付 or 线下支付, 对于部分不支持线上支付的国家或地区,返回线下支付 */
  payType: PayType;
  /** 报价模式 */
  quotationMode: QuotationMode;
  /** 支付模式 */
  payMode: PayMode;
}

export interface AddressInfoModel {
  /** 用户名 */
  userName: string;
  /** 电话号码 */
  phone: string;
  /** 地址 */
  address: string;
}

export interface ProductAmountModel {
  /** 产品成本 */
  amount: number;
  /** 产品成本费用项 */
  feeList: FeeModel[];
}

export interface FeeModel {
  /** 费用名称 */
  feeName: string;
  /** 费用金额 */
  feeAmount: number;
  /** 子费用 */
  childFeeList: FeeModel[];
}

export interface TransportAmountModel {
  /** 线路id */
  transportId: string;
  /** 线路名称 */
  name: string;
  /** 预估费用 */
  amount: number;
  /** 费用明细 */
  amountDetailList: FeeModel[];
  /** 预期交货时间 */
  expectDeliveryTime: string;
  /** 备注 */
  transportRemark: string;
}

export interface BoxInfoModel {
  /** sku件数-装箱数 */
  skuCount: number;
  /** 箱子数量 */
  boxCount: number;
  /** 箱运费 */
  boxTransportFeeList: BoxTransportFeeModel[];
  /** sku列表 */
  skuList: SkuModel[];
}

export interface BoxTransportFeeModel {
  /** 线路id */
  transportId: string;
  /** 运输费用 */
  amount: number;
}

export interface PaymentModel {
  /** 需要支付的金额 */
  amount: number;
  /** 0为线上支付, 1为线下支付 */
  payType: PayType;
  /** 账单内容描述 */
  description: string;
  /** 金额备注 */
  amountRemark: string;
}

/** 查询收银台信息，包括待支付金额、支付方式列表、是否已支付 */
export interface GetCashDeskInfoResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 数据 */
  data: GetCashDeskInfoModel | undefined;
}

export interface GetCashDeskInfoModel {
  /** 支付方式列表 */
  payMethodList: PayMethodModel[];
  /** 支付金额 */
  payAmount:
    | PaymentAmountModel
    | undefined;
  /** 支付结果 */
  payResultModel: PayResultModel | undefined;
  goodsName: string;
  picUrl: string;
  totalCount: number;
}

export interface PayMethodModel {
  /** 支付方式code */
  code: string;
  /** 名称，显示用 */
  name: string;
  /** 支付方式图标icon url */
  iconUrl: string;
}

export interface PaymentAmountModel {
  amount: number;
  feeList: FeeModel[];
}

/** 查询支付结果 */
export interface QueryPayResultResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 返回数据 */
  data: QueryPayResultModel | undefined;
}

export interface QueryPayResultModel {
  /** 支付结果 */
  payResult:
    | PayResultModel
    | undefined;
  /** 支付时所处订单状态 */
  mallOrderStatus: MallOrderStatus;
}

export interface PayResultModel {
  /** 返回支付结果 */
  payStatus: PayStatusEnum;
  /** 如果失败的话，返回失败原因 */
  errorMsg: string;
}

/** 未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息 */
export interface OpenCashDeskResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 返回数据 */
  data: OpenCashDeskModel | undefined;
}

export interface OpenCashDeskModel {
  paymentId: string;
}

/** 提交支付 */
export interface SubmitPaymentResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 返回数据 */
  data: SubmitPaymentModel | undefined;
}

export interface SubmitPaymentModel {
  /** 第三方支付的pay url，前端需要跳转过去 */
  payUrl: string;
}

function createBaseGetOrderListResp(): GetOrderListResp {
  return { result: undefined, data: undefined };
}

export const GetOrderListResp = {
  encode(message: GetOrderListResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetOrderListModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GetOrderListModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetOrderListModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetOrderListResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetOrderListModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListResp>, I>>(base?: I): GetOrderListResp {
    return GetOrderListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListResp>, I>>(object: I): GetOrderListResp {
    const message = createBaseGetOrderListResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetOrderListModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetOrderListModel(): GetOrderListModel {
  return { page: undefined, orderList: [] };
}

export const GetOrderListModel = {
  encode(message: GetOrderListModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    for (const v of message.orderList) {
      OrderInfoModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.orderList.push(OrderInfoModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListModel {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      orderList: globalThis.Array.isArray(object?.orderList)
        ? object.orderList.map((e: any) => OrderInfoModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetOrderListModel): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.orderList?.length) {
      obj.orderList = message.orderList.map((e) => OrderInfoModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListModel>, I>>(base?: I): GetOrderListModel {
    return GetOrderListModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListModel>, I>>(object: I): GetOrderListModel {
    const message = createBaseGetOrderListModel();
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.orderList = object.orderList?.map((e) => OrderInfoModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOrderInfoModel(): OrderInfoModel {
  return { orderNo: "", mallOrderStatus: 0, statusDesc: "", skuList: [], orderTime: 0 };
}

export const OrderInfoModel = {
  encode(message: OrderInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.mallOrderStatus !== 0) {
      writer.uint32(160).int32(message.mallOrderStatus);
    }
    if (message.statusDesc !== "") {
      writer.uint32(170).string(message.statusDesc);
    }
    for (const v of message.skuList) {
      SkuModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    if (message.orderTime !== 0) {
      writer.uint32(320).int64(message.orderTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): OrderInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOrderInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.mallOrderStatus = reader.int32() as any;
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.statusDesc = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.skuList.push(SkuModel.decode(reader, reader.uint32()));
          continue;
        case 40:
          if (tag !== 320) {
            break;
          }

          message.orderTime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OrderInfoModel {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      mallOrderStatus: isSet(object.mallOrderStatus) ? mallOrderStatusFromJSON(object.mallOrderStatus) : 0,
      statusDesc: isSet(object.statusDesc) ? globalThis.String(object.statusDesc) : "",
      skuList: globalThis.Array.isArray(object?.skuList) ? object.skuList.map((e: any) => SkuModel.fromJSON(e)) : [],
      orderTime: isSet(object.orderTime) ? globalThis.Number(object.orderTime) : 0,
    };
  },

  toJSON(message: OrderInfoModel): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.mallOrderStatus !== 0) {
      obj.mallOrderStatus = mallOrderStatusToJSON(message.mallOrderStatus);
    }
    if (message.statusDesc !== "") {
      obj.statusDesc = message.statusDesc;
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => SkuModel.toJSON(e));
    }
    if (message.orderTime !== 0) {
      obj.orderTime = Math.round(message.orderTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OrderInfoModel>, I>>(base?: I): OrderInfoModel {
    return OrderInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderInfoModel>, I>>(object: I): OrderInfoModel {
    const message = createBaseOrderInfoModel();
    message.orderNo = object.orderNo ?? "";
    message.mallOrderStatus = object.mallOrderStatus ?? 0;
    message.statusDesc = object.statusDesc ?? "";
    message.skuList = object.skuList?.map((e) => SkuModel.fromPartial(e)) || [];
    message.orderTime = object.orderTime ?? 0;
    return message;
  },
};

function createBaseGetOrderDetailResp(): GetOrderDetailResp {
  return { result: undefined, data: undefined };
}

export const GetOrderDetailResp = {
  encode(message: GetOrderDetailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetOrderDetailModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GetOrderDetailModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetOrderDetailModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetOrderDetailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetOrderDetailModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailResp>, I>>(base?: I): GetOrderDetailResp {
    return GetOrderDetailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailResp>, I>>(object: I): GetOrderDetailResp {
    const message = createBaseGetOrderDetailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetOrderDetailModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetOrderDetailModel(): GetOrderDetailModel {
  return {
    orderNo: "",
    mallOrderStatus: 0,
    statusDesc: "",
    orderTime: 0,
    addressInfo: undefined,
    boxList: [],
    totalCount: 0,
    orderRemark: "",
    productAmount: undefined,
    transportAmountList: [],
    payType: 0,
    quotationMode: 0,
    payMode: 0,
  };
}

export const GetOrderDetailModel = {
  encode(message: GetOrderDetailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.mallOrderStatus !== 0) {
      writer.uint32(160).int32(message.mallOrderStatus);
    }
    if (message.statusDesc !== "") {
      writer.uint32(170).string(message.statusDesc);
    }
    if (message.orderTime !== 0) {
      writer.uint32(200).int64(message.orderTime);
    }
    if (message.addressInfo !== undefined) {
      AddressInfoModel.encode(message.addressInfo, writer.uint32(242).fork()).ldelim();
    }
    for (const v of message.boxList) {
      BoxInfoModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.totalCount !== 0) {
      writer.uint32(400).int32(message.totalCount);
    }
    if (message.orderRemark !== "") {
      writer.uint32(562).string(message.orderRemark);
    }
    if (message.productAmount !== undefined) {
      ProductAmountModel.encode(message.productAmount, writer.uint32(642).fork()).ldelim();
    }
    for (const v of message.transportAmountList) {
      TransportAmountModel.encode(v!, writer.uint32(722).fork()).ldelim();
    }
    if (message.payType !== 0) {
      writer.uint32(800).int32(message.payType);
    }
    if (message.quotationMode !== 0) {
      writer.uint32(880).int32(message.quotationMode);
    }
    if (message.payMode !== 0) {
      writer.uint32(960).int32(message.payMode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.mallOrderStatus = reader.int32() as any;
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.statusDesc = reader.string();
          continue;
        case 25:
          if (tag !== 200) {
            break;
          }

          message.orderTime = longToNumber(reader.int64() as Long);
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.addressInfo = AddressInfoModel.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.boxList.push(BoxInfoModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        case 70:
          if (tag !== 562) {
            break;
          }

          message.orderRemark = reader.string();
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.productAmount = ProductAmountModel.decode(reader, reader.uint32());
          continue;
        case 90:
          if (tag !== 722) {
            break;
          }

          message.transportAmountList.push(TransportAmountModel.decode(reader, reader.uint32()));
          continue;
        case 100:
          if (tag !== 800) {
            break;
          }

          message.payType = reader.int32() as any;
          continue;
        case 110:
          if (tag !== 880) {
            break;
          }

          message.quotationMode = reader.int32() as any;
          continue;
        case 120:
          if (tag !== 960) {
            break;
          }

          message.payMode = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailModel {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      mallOrderStatus: isSet(object.mallOrderStatus) ? mallOrderStatusFromJSON(object.mallOrderStatus) : 0,
      statusDesc: isSet(object.statusDesc) ? globalThis.String(object.statusDesc) : "",
      orderTime: isSet(object.orderTime) ? globalThis.Number(object.orderTime) : 0,
      addressInfo: isSet(object.addressInfo) ? AddressInfoModel.fromJSON(object.addressInfo) : undefined,
      boxList: globalThis.Array.isArray(object?.boxList)
        ? object.boxList.map((e: any) => BoxInfoModel.fromJSON(e))
        : [],
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
      orderRemark: isSet(object.orderRemark) ? globalThis.String(object.orderRemark) : "",
      productAmount: isSet(object.productAmount) ? ProductAmountModel.fromJSON(object.productAmount) : undefined,
      transportAmountList: globalThis.Array.isArray(object?.transportAmountList)
        ? object.transportAmountList.map((e: any) => TransportAmountModel.fromJSON(e))
        : [],
      payType: isSet(object.payType) ? payTypeFromJSON(object.payType) : 0,
      quotationMode: isSet(object.quotationMode) ? quotationModeFromJSON(object.quotationMode) : 0,
      payMode: isSet(object.payMode) ? payModeFromJSON(object.payMode) : 0,
    };
  },

  toJSON(message: GetOrderDetailModel): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.mallOrderStatus !== 0) {
      obj.mallOrderStatus = mallOrderStatusToJSON(message.mallOrderStatus);
    }
    if (message.statusDesc !== "") {
      obj.statusDesc = message.statusDesc;
    }
    if (message.orderTime !== 0) {
      obj.orderTime = Math.round(message.orderTime);
    }
    if (message.addressInfo !== undefined) {
      obj.addressInfo = AddressInfoModel.toJSON(message.addressInfo);
    }
    if (message.boxList?.length) {
      obj.boxList = message.boxList.map((e) => BoxInfoModel.toJSON(e));
    }
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    if (message.orderRemark !== "") {
      obj.orderRemark = message.orderRemark;
    }
    if (message.productAmount !== undefined) {
      obj.productAmount = ProductAmountModel.toJSON(message.productAmount);
    }
    if (message.transportAmountList?.length) {
      obj.transportAmountList = message.transportAmountList.map((e) => TransportAmountModel.toJSON(e));
    }
    if (message.payType !== 0) {
      obj.payType = payTypeToJSON(message.payType);
    }
    if (message.quotationMode !== 0) {
      obj.quotationMode = quotationModeToJSON(message.quotationMode);
    }
    if (message.payMode !== 0) {
      obj.payMode = payModeToJSON(message.payMode);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailModel>, I>>(base?: I): GetOrderDetailModel {
    return GetOrderDetailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailModel>, I>>(object: I): GetOrderDetailModel {
    const message = createBaseGetOrderDetailModel();
    message.orderNo = object.orderNo ?? "";
    message.mallOrderStatus = object.mallOrderStatus ?? 0;
    message.statusDesc = object.statusDesc ?? "";
    message.orderTime = object.orderTime ?? 0;
    message.addressInfo = (object.addressInfo !== undefined && object.addressInfo !== null)
      ? AddressInfoModel.fromPartial(object.addressInfo)
      : undefined;
    message.boxList = object.boxList?.map((e) => BoxInfoModel.fromPartial(e)) || [];
    message.totalCount = object.totalCount ?? 0;
    message.orderRemark = object.orderRemark ?? "";
    message.productAmount = (object.productAmount !== undefined && object.productAmount !== null)
      ? ProductAmountModel.fromPartial(object.productAmount)
      : undefined;
    message.transportAmountList = object.transportAmountList?.map((e) => TransportAmountModel.fromPartial(e)) || [];
    message.payType = object.payType ?? 0;
    message.quotationMode = object.quotationMode ?? 0;
    message.payMode = object.payMode ?? 0;
    return message;
  },
};

function createBaseAddressInfoModel(): AddressInfoModel {
  return { userName: "", phone: "", address: "" };
}

export const AddressInfoModel = {
  encode(message: AddressInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userName !== "") {
      writer.uint32(82).string(message.userName);
    }
    if (message.phone !== "") {
      writer.uint32(162).string(message.phone);
    }
    if (message.address !== "") {
      writer.uint32(242).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AddressInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddressInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.userName = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.phone = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddressInfoModel {
    return {
      userName: isSet(object.userName) ? globalThis.String(object.userName) : "",
      phone: isSet(object.phone) ? globalThis.String(object.phone) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: AddressInfoModel): unknown {
    const obj: any = {};
    if (message.userName !== "") {
      obj.userName = message.userName;
    }
    if (message.phone !== "") {
      obj.phone = message.phone;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddressInfoModel>, I>>(base?: I): AddressInfoModel {
    return AddressInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddressInfoModel>, I>>(object: I): AddressInfoModel {
    const message = createBaseAddressInfoModel();
    message.userName = object.userName ?? "";
    message.phone = object.phone ?? "";
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseProductAmountModel(): ProductAmountModel {
  return { amount: 0, feeList: [] };
}

export const ProductAmountModel = {
  encode(message: ProductAmountModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.amount !== 0) {
      writer.uint32(81).double(message.amount);
    }
    for (const v of message.feeList) {
      FeeModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ProductAmountModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProductAmountModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 81) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.feeList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProductAmountModel {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      feeList: globalThis.Array.isArray(object?.feeList) ? object.feeList.map((e: any) => FeeModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: ProductAmountModel): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.feeList?.length) {
      obj.feeList = message.feeList.map((e) => FeeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProductAmountModel>, I>>(base?: I): ProductAmountModel {
    return ProductAmountModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductAmountModel>, I>>(object: I): ProductAmountModel {
    const message = createBaseProductAmountModel();
    message.amount = object.amount ?? 0;
    message.feeList = object.feeList?.map((e) => FeeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFeeModel(): FeeModel {
  return { feeName: "", feeAmount: 0, childFeeList: [] };
}

export const FeeModel = {
  encode(message: FeeModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.feeName !== "") {
      writer.uint32(82).string(message.feeName);
    }
    if (message.feeAmount !== 0) {
      writer.uint32(161).double(message.feeAmount);
    }
    for (const v of message.childFeeList) {
      FeeModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FeeModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.feeName = reader.string();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.feeAmount = reader.double();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.childFeeList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeeModel {
    return {
      feeName: isSet(object.feeName) ? globalThis.String(object.feeName) : "",
      feeAmount: isSet(object.feeAmount) ? globalThis.Number(object.feeAmount) : 0,
      childFeeList: globalThis.Array.isArray(object?.childFeeList)
        ? object.childFeeList.map((e: any) => FeeModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FeeModel): unknown {
    const obj: any = {};
    if (message.feeName !== "") {
      obj.feeName = message.feeName;
    }
    if (message.feeAmount !== 0) {
      obj.feeAmount = message.feeAmount;
    }
    if (message.childFeeList?.length) {
      obj.childFeeList = message.childFeeList.map((e) => FeeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeeModel>, I>>(base?: I): FeeModel {
    return FeeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeeModel>, I>>(object: I): FeeModel {
    const message = createBaseFeeModel();
    message.feeName = object.feeName ?? "";
    message.feeAmount = object.feeAmount ?? 0;
    message.childFeeList = object.childFeeList?.map((e) => FeeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTransportAmountModel(): TransportAmountModel {
  return { transportId: "", name: "", amount: 0, amountDetailList: [], expectDeliveryTime: "", transportRemark: "" };
}

export const TransportAmountModel = {
  encode(message: TransportAmountModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.transportId !== "") {
      writer.uint32(82).string(message.transportId);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    if (message.amount !== 0) {
      writer.uint32(241).double(message.amount);
    }
    for (const v of message.amountDetailList) {
      FeeModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.expectDeliveryTime !== "") {
      writer.uint32(402).string(message.expectDeliveryTime);
    }
    if (message.transportRemark !== "") {
      writer.uint32(482).string(message.transportRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): TransportAmountModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransportAmountModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.transportId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.amountDetailList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.expectDeliveryTime = reader.string();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.transportRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TransportAmountModel {
    return {
      transportId: isSet(object.transportId) ? globalThis.String(object.transportId) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      amountDetailList: globalThis.Array.isArray(object?.amountDetailList)
        ? object.amountDetailList.map((e: any) => FeeModel.fromJSON(e))
        : [],
      expectDeliveryTime: isSet(object.expectDeliveryTime) ? globalThis.String(object.expectDeliveryTime) : "",
      transportRemark: isSet(object.transportRemark) ? globalThis.String(object.transportRemark) : "",
    };
  },

  toJSON(message: TransportAmountModel): unknown {
    const obj: any = {};
    if (message.transportId !== "") {
      obj.transportId = message.transportId;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.amountDetailList?.length) {
      obj.amountDetailList = message.amountDetailList.map((e) => FeeModel.toJSON(e));
    }
    if (message.expectDeliveryTime !== "") {
      obj.expectDeliveryTime = message.expectDeliveryTime;
    }
    if (message.transportRemark !== "") {
      obj.transportRemark = message.transportRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransportAmountModel>, I>>(base?: I): TransportAmountModel {
    return TransportAmountModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransportAmountModel>, I>>(object: I): TransportAmountModel {
    const message = createBaseTransportAmountModel();
    message.transportId = object.transportId ?? "";
    message.name = object.name ?? "";
    message.amount = object.amount ?? 0;
    message.amountDetailList = object.amountDetailList?.map((e) => FeeModel.fromPartial(e)) || [];
    message.expectDeliveryTime = object.expectDeliveryTime ?? "";
    message.transportRemark = object.transportRemark ?? "";
    return message;
  },
};

function createBaseBoxInfoModel(): BoxInfoModel {
  return { skuCount: 0, boxCount: 0, boxTransportFeeList: [], skuList: [] };
}

export const BoxInfoModel = {
  encode(message: BoxInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.skuCount !== 0) {
      writer.uint32(80).int32(message.skuCount);
    }
    if (message.boxCount !== 0) {
      writer.uint32(160).int32(message.boxCount);
    }
    for (const v of message.boxTransportFeeList) {
      BoxTransportFeeModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    for (const v of message.skuList) {
      SkuModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BoxInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoxInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.skuCount = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.boxCount = reader.int32();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.boxTransportFeeList.push(BoxTransportFeeModel.decode(reader, reader.uint32()));
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.skuList.push(SkuModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BoxInfoModel {
    return {
      skuCount: isSet(object.skuCount) ? globalThis.Number(object.skuCount) : 0,
      boxCount: isSet(object.boxCount) ? globalThis.Number(object.boxCount) : 0,
      boxTransportFeeList: globalThis.Array.isArray(object?.boxTransportFeeList)
        ? object.boxTransportFeeList.map((e: any) => BoxTransportFeeModel.fromJSON(e))
        : [],
      skuList: globalThis.Array.isArray(object?.skuList) ? object.skuList.map((e: any) => SkuModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: BoxInfoModel): unknown {
    const obj: any = {};
    if (message.skuCount !== 0) {
      obj.skuCount = Math.round(message.skuCount);
    }
    if (message.boxCount !== 0) {
      obj.boxCount = Math.round(message.boxCount);
    }
    if (message.boxTransportFeeList?.length) {
      obj.boxTransportFeeList = message.boxTransportFeeList.map((e) => BoxTransportFeeModel.toJSON(e));
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => SkuModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BoxInfoModel>, I>>(base?: I): BoxInfoModel {
    return BoxInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BoxInfoModel>, I>>(object: I): BoxInfoModel {
    const message = createBaseBoxInfoModel();
    message.skuCount = object.skuCount ?? 0;
    message.boxCount = object.boxCount ?? 0;
    message.boxTransportFeeList = object.boxTransportFeeList?.map((e) => BoxTransportFeeModel.fromPartial(e)) || [];
    message.skuList = object.skuList?.map((e) => SkuModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBoxTransportFeeModel(): BoxTransportFeeModel {
  return { transportId: "", amount: 0 };
}

export const BoxTransportFeeModel = {
  encode(message: BoxTransportFeeModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.transportId !== "") {
      writer.uint32(82).string(message.transportId);
    }
    if (message.amount !== 0) {
      writer.uint32(161).double(message.amount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BoxTransportFeeModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoxTransportFeeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.transportId = reader.string();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.amount = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BoxTransportFeeModel {
    return {
      transportId: isSet(object.transportId) ? globalThis.String(object.transportId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
    };
  },

  toJSON(message: BoxTransportFeeModel): unknown {
    const obj: any = {};
    if (message.transportId !== "") {
      obj.transportId = message.transportId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BoxTransportFeeModel>, I>>(base?: I): BoxTransportFeeModel {
    return BoxTransportFeeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BoxTransportFeeModel>, I>>(object: I): BoxTransportFeeModel {
    const message = createBaseBoxTransportFeeModel();
    message.transportId = object.transportId ?? "";
    message.amount = object.amount ?? 0;
    return message;
  },
};

function createBasePaymentModel(): PaymentModel {
  return { amount: 0, payType: 0, description: "", amountRemark: "" };
}

export const PaymentModel = {
  encode(message: PaymentModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.amount !== 0) {
      writer.uint32(161).double(message.amount);
    }
    if (message.payType !== 0) {
      writer.uint32(240).int32(message.payType);
    }
    if (message.description !== "") {
      writer.uint32(322).string(message.description);
    }
    if (message.amountRemark !== "") {
      writer.uint32(402).string(message.amountRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PaymentModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaymentModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 20:
          if (tag !== 161) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.payType = reader.int32() as any;
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.description = reader.string();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.amountRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaymentModel {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      payType: isSet(object.payType) ? payTypeFromJSON(object.payType) : 0,
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      amountRemark: isSet(object.amountRemark) ? globalThis.String(object.amountRemark) : "",
    };
  },

  toJSON(message: PaymentModel): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.payType !== 0) {
      obj.payType = payTypeToJSON(message.payType);
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.amountRemark !== "") {
      obj.amountRemark = message.amountRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaymentModel>, I>>(base?: I): PaymentModel {
    return PaymentModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentModel>, I>>(object: I): PaymentModel {
    const message = createBasePaymentModel();
    message.amount = object.amount ?? 0;
    message.payType = object.payType ?? 0;
    message.description = object.description ?? "";
    message.amountRemark = object.amountRemark ?? "";
    return message;
  },
};

function createBaseGetCashDeskInfoResp(): GetCashDeskInfoResp {
  return { result: undefined, data: undefined };
}

export const GetCashDeskInfoResp = {
  encode(message: GetCashDeskInfoResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetCashDeskInfoModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCashDeskInfoResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashDeskInfoResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = GetCashDeskInfoModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashDeskInfoResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetCashDeskInfoModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetCashDeskInfoResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetCashDeskInfoModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashDeskInfoResp>, I>>(base?: I): GetCashDeskInfoResp {
    return GetCashDeskInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashDeskInfoResp>, I>>(object: I): GetCashDeskInfoResp {
    const message = createBaseGetCashDeskInfoResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetCashDeskInfoModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetCashDeskInfoModel(): GetCashDeskInfoModel {
  return {
    payMethodList: [],
    payAmount: undefined,
    payResultModel: undefined,
    goodsName: "",
    picUrl: "",
    totalCount: 0,
  };
}

export const GetCashDeskInfoModel = {
  encode(message: GetCashDeskInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.payMethodList) {
      PayMethodModel.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    if (message.payAmount !== undefined) {
      PaymentAmountModel.encode(message.payAmount, writer.uint32(162).fork()).ldelim();
    }
    if (message.payResultModel !== undefined) {
      PayResultModel.encode(message.payResultModel, writer.uint32(242).fork()).ldelim();
    }
    if (message.goodsName !== "") {
      writer.uint32(322).string(message.goodsName);
    }
    if (message.picUrl !== "") {
      writer.uint32(402).string(message.picUrl);
    }
    if (message.totalCount !== 0) {
      writer.uint32(480).int32(message.totalCount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCashDeskInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashDeskInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.payMethodList.push(PayMethodModel.decode(reader, reader.uint32()));
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.payAmount = PaymentAmountModel.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.payResultModel = PayResultModel.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.picUrl = reader.string();
          continue;
        case 60:
          if (tag !== 480) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashDeskInfoModel {
    return {
      payMethodList: globalThis.Array.isArray(object?.payMethodList)
        ? object.payMethodList.map((e: any) => PayMethodModel.fromJSON(e))
        : [],
      payAmount: isSet(object.payAmount) ? PaymentAmountModel.fromJSON(object.payAmount) : undefined,
      payResultModel: isSet(object.payResultModel) ? PayResultModel.fromJSON(object.payResultModel) : undefined,
      goodsName: isSet(object.goodsName) ? globalThis.String(object.goodsName) : "",
      picUrl: isSet(object.picUrl) ? globalThis.String(object.picUrl) : "",
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
    };
  },

  toJSON(message: GetCashDeskInfoModel): unknown {
    const obj: any = {};
    if (message.payMethodList?.length) {
      obj.payMethodList = message.payMethodList.map((e) => PayMethodModel.toJSON(e));
    }
    if (message.payAmount !== undefined) {
      obj.payAmount = PaymentAmountModel.toJSON(message.payAmount);
    }
    if (message.payResultModel !== undefined) {
      obj.payResultModel = PayResultModel.toJSON(message.payResultModel);
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.picUrl !== "") {
      obj.picUrl = message.picUrl;
    }
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashDeskInfoModel>, I>>(base?: I): GetCashDeskInfoModel {
    return GetCashDeskInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashDeskInfoModel>, I>>(object: I): GetCashDeskInfoModel {
    const message = createBaseGetCashDeskInfoModel();
    message.payMethodList = object.payMethodList?.map((e) => PayMethodModel.fromPartial(e)) || [];
    message.payAmount = (object.payAmount !== undefined && object.payAmount !== null)
      ? PaymentAmountModel.fromPartial(object.payAmount)
      : undefined;
    message.payResultModel = (object.payResultModel !== undefined && object.payResultModel !== null)
      ? PayResultModel.fromPartial(object.payResultModel)
      : undefined;
    message.goodsName = object.goodsName ?? "";
    message.picUrl = object.picUrl ?? "";
    message.totalCount = object.totalCount ?? 0;
    return message;
  },
};

function createBasePayMethodModel(): PayMethodModel {
  return { code: "", name: "", iconUrl: "" };
}

export const PayMethodModel = {
  encode(message: PayMethodModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== "") {
      writer.uint32(82).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(242).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PayMethodModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePayMethodModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.code = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PayMethodModel {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: PayMethodModel): unknown {
    const obj: any = {};
    if (message.code !== "") {
      obj.code = message.code;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PayMethodModel>, I>>(base?: I): PayMethodModel {
    return PayMethodModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayMethodModel>, I>>(object: I): PayMethodModel {
    const message = createBasePayMethodModel();
    message.code = object.code ?? "";
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBasePaymentAmountModel(): PaymentAmountModel {
  return { amount: 0, feeList: [] };
}

export const PaymentAmountModel = {
  encode(message: PaymentAmountModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.amount !== 0) {
      writer.uint32(81).double(message.amount);
    }
    for (const v of message.feeList) {
      FeeModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PaymentAmountModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaymentAmountModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 81) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.feeList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaymentAmountModel {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      feeList: globalThis.Array.isArray(object?.feeList) ? object.feeList.map((e: any) => FeeModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: PaymentAmountModel): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.feeList?.length) {
      obj.feeList = message.feeList.map((e) => FeeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaymentAmountModel>, I>>(base?: I): PaymentAmountModel {
    return PaymentAmountModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentAmountModel>, I>>(object: I): PaymentAmountModel {
    const message = createBasePaymentAmountModel();
    message.amount = object.amount ?? 0;
    message.feeList = object.feeList?.map((e) => FeeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseQueryPayResultResp(): QueryPayResultResp {
  return { result: undefined, data: undefined };
}

export const QueryPayResultResp = {
  encode(message: QueryPayResultResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      QueryPayResultModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPayResultResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPayResultResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = QueryPayResultModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPayResultResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? QueryPayResultModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: QueryPayResultResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = QueryPayResultModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPayResultResp>, I>>(base?: I): QueryPayResultResp {
    return QueryPayResultResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPayResultResp>, I>>(object: I): QueryPayResultResp {
    const message = createBaseQueryPayResultResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? QueryPayResultModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseQueryPayResultModel(): QueryPayResultModel {
  return { payResult: undefined, mallOrderStatus: 0 };
}

export const QueryPayResultModel = {
  encode(message: QueryPayResultModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.payResult !== undefined) {
      PayResultModel.encode(message.payResult, writer.uint32(82).fork()).ldelim();
    }
    if (message.mallOrderStatus !== 0) {
      writer.uint32(160).int32(message.mallOrderStatus);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPayResultModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPayResultModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.payResult = PayResultModel.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.mallOrderStatus = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPayResultModel {
    return {
      payResult: isSet(object.payResult) ? PayResultModel.fromJSON(object.payResult) : undefined,
      mallOrderStatus: isSet(object.mallOrderStatus) ? mallOrderStatusFromJSON(object.mallOrderStatus) : 0,
    };
  },

  toJSON(message: QueryPayResultModel): unknown {
    const obj: any = {};
    if (message.payResult !== undefined) {
      obj.payResult = PayResultModel.toJSON(message.payResult);
    }
    if (message.mallOrderStatus !== 0) {
      obj.mallOrderStatus = mallOrderStatusToJSON(message.mallOrderStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPayResultModel>, I>>(base?: I): QueryPayResultModel {
    return QueryPayResultModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPayResultModel>, I>>(object: I): QueryPayResultModel {
    const message = createBaseQueryPayResultModel();
    message.payResult = (object.payResult !== undefined && object.payResult !== null)
      ? PayResultModel.fromPartial(object.payResult)
      : undefined;
    message.mallOrderStatus = object.mallOrderStatus ?? 0;
    return message;
  },
};

function createBasePayResultModel(): PayResultModel {
  return { payStatus: 0, errorMsg: "" };
}

export const PayResultModel = {
  encode(message: PayResultModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.payStatus !== 0) {
      writer.uint32(80).int32(message.payStatus);
    }
    if (message.errorMsg !== "") {
      writer.uint32(162).string(message.errorMsg);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PayResultModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePayResultModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.payStatus = reader.int32() as any;
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.errorMsg = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PayResultModel {
    return {
      payStatus: isSet(object.payStatus) ? payStatusEnumFromJSON(object.payStatus) : 0,
      errorMsg: isSet(object.errorMsg) ? globalThis.String(object.errorMsg) : "",
    };
  },

  toJSON(message: PayResultModel): unknown {
    const obj: any = {};
    if (message.payStatus !== 0) {
      obj.payStatus = payStatusEnumToJSON(message.payStatus);
    }
    if (message.errorMsg !== "") {
      obj.errorMsg = message.errorMsg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PayResultModel>, I>>(base?: I): PayResultModel {
    return PayResultModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayResultModel>, I>>(object: I): PayResultModel {
    const message = createBasePayResultModel();
    message.payStatus = object.payStatus ?? 0;
    message.errorMsg = object.errorMsg ?? "";
    return message;
  },
};

function createBaseOpenCashDeskResp(): OpenCashDeskResp {
  return { result: undefined, data: undefined };
}

export const OpenCashDeskResp = {
  encode(message: OpenCashDeskResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      OpenCashDeskModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): OpenCashDeskResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOpenCashDeskResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = OpenCashDeskModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OpenCashDeskResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? OpenCashDeskModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: OpenCashDeskResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = OpenCashDeskModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OpenCashDeskResp>, I>>(base?: I): OpenCashDeskResp {
    return OpenCashDeskResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OpenCashDeskResp>, I>>(object: I): OpenCashDeskResp {
    const message = createBaseOpenCashDeskResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? OpenCashDeskModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseOpenCashDeskModel(): OpenCashDeskModel {
  return { paymentId: "" };
}

export const OpenCashDeskModel = {
  encode(message: OpenCashDeskModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.paymentId !== "") {
      writer.uint32(82).string(message.paymentId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): OpenCashDeskModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOpenCashDeskModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.paymentId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OpenCashDeskModel {
    return { paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "" };
  },

  toJSON(message: OpenCashDeskModel): unknown {
    const obj: any = {};
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OpenCashDeskModel>, I>>(base?: I): OpenCashDeskModel {
    return OpenCashDeskModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OpenCashDeskModel>, I>>(object: I): OpenCashDeskModel {
    const message = createBaseOpenCashDeskModel();
    message.paymentId = object.paymentId ?? "";
    return message;
  },
};

function createBaseSubmitPaymentResp(): SubmitPaymentResp {
  return { result: undefined, data: undefined };
}

export const SubmitPaymentResp = {
  encode(message: SubmitPaymentResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      SubmitPaymentModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SubmitPaymentResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitPaymentResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = SubmitPaymentModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitPaymentResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? SubmitPaymentModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: SubmitPaymentResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = SubmitPaymentModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitPaymentResp>, I>>(base?: I): SubmitPaymentResp {
    return SubmitPaymentResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitPaymentResp>, I>>(object: I): SubmitPaymentResp {
    const message = createBaseSubmitPaymentResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? SubmitPaymentModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseSubmitPaymentModel(): SubmitPaymentModel {
  return { payUrl: "" };
}

export const SubmitPaymentModel = {
  encode(message: SubmitPaymentModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.payUrl !== "") {
      writer.uint32(82).string(message.payUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SubmitPaymentModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitPaymentModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.payUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitPaymentModel {
    return { payUrl: isSet(object.payUrl) ? globalThis.String(object.payUrl) : "" };
  },

  toJSON(message: SubmitPaymentModel): unknown {
    const obj: any = {};
    if (message.payUrl !== "") {
      obj.payUrl = message.payUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitPaymentModel>, I>>(base?: I): SubmitPaymentModel {
    return SubmitPaymentModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitPaymentModel>, I>>(object: I): SubmitPaymentModel {
    const message = createBaseSubmitPaymentModel();
    message.payUrl = object.payUrl ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
