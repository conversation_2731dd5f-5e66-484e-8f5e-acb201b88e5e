/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../common";
import {
  GetCashDeskInfoResp,
  GetOrderDetailResp,
  GetOrderListResp,
  OpenCashDeskResp,
  QueryPayResultResp,
  SubmitPaymentResp,
} from "./model/mall_order_pages_model";
import {
  CancelOrderParam,
  GetCashDeskInfoParam,
  GetOrderDetailParam,
  GetOrderListRequestParam,
  OpenCashDeskParam,
  QueryPayResultParam,
  SubmitPaymentParam,
} from "./param/mall_order_pages_param";

export const protobufPackage = "mall.pages";

/** 订单支付相关页面 */
export interface MallOrderPage {
  /** 分页查询订单列表 */
  getOrderList(request: GetOrderListRequestParam): Promise<GetOrderListResp>;
  /** 取消订单 */
  cancelOrder(request: CancelOrderParam): Promise<Result>;
  /** 查询订单详情 */
  getOrderDetail(request: GetOrderDetailParam): Promise<GetOrderDetailResp>;
  /** 未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息 */
  openCashDesk(request: OpenCashDeskParam): Promise<OpenCashDeskResp>;
  /** 查询收银台信息，包括待支付金额、支付方式列表、是否已支付 */
  getCashDeskInfo(request: GetCashDeskInfoParam): Promise<GetCashDeskInfoResp>;
  /** 收银台提交支付 */
  submitPayment(request: SubmitPaymentParam): Promise<SubmitPaymentResp>;
  /** 查询支付结果 */
  queryPayResult(request: QueryPayResultParam): Promise<QueryPayResultResp>;
}

export const MallOrderPageServiceName = "mall.pages.MallOrderPage";
export class MallOrderPageClientImpl implements MallOrderPage {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || MallOrderPageServiceName;
    this.rpc = rpc;
    this.getOrderList = this.getOrderList.bind(this);
    this.cancelOrder = this.cancelOrder.bind(this);
    this.getOrderDetail = this.getOrderDetail.bind(this);
    this.openCashDesk = this.openCashDesk.bind(this);
    this.getCashDeskInfo = this.getCashDeskInfo.bind(this);
    this.submitPayment = this.submitPayment.bind(this);
    this.queryPayResult = this.queryPayResult.bind(this);
  }
  getOrderList(request: GetOrderListRequestParam): Promise<GetOrderListResp> {
    const data = GetOrderListRequestParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getOrderList", data);
    return promise.then((data) => GetOrderListResp.decode(_m0.Reader.create(data)));
  }

  cancelOrder(request: CancelOrderParam): Promise<Result> {
    const data = CancelOrderParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "cancelOrder", data);
    return promise.then((data) => Result.decode(_m0.Reader.create(data)));
  }

  getOrderDetail(request: GetOrderDetailParam): Promise<GetOrderDetailResp> {
    const data = GetOrderDetailParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getOrderDetail", data);
    return promise.then((data) => GetOrderDetailResp.decode(_m0.Reader.create(data)));
  }

  openCashDesk(request: OpenCashDeskParam): Promise<OpenCashDeskResp> {
    const data = OpenCashDeskParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "openCashDesk", data);
    return promise.then((data) => OpenCashDeskResp.decode(_m0.Reader.create(data)));
  }

  getCashDeskInfo(request: GetCashDeskInfoParam): Promise<GetCashDeskInfoResp> {
    const data = GetCashDeskInfoParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getCashDeskInfo", data);
    return promise.then((data) => GetCashDeskInfoResp.decode(_m0.Reader.create(data)));
  }

  submitPayment(request: SubmitPaymentParam): Promise<SubmitPaymentResp> {
    const data = SubmitPaymentParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "submitPayment", data);
    return promise.then((data) => SubmitPaymentResp.decode(_m0.Reader.create(data)));
  }

  queryPayResult(request: QueryPayResultParam): Promise<QueryPayResultResp> {
    const data = QueryPayResultParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "queryPayResult", data);
    return promise.then((data) => QueryPayResultResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
