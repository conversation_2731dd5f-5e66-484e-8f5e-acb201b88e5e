syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/param/cart_param.proto";
import "common.proto";

//商城配置批量查询
message MallConfigMultiQueryParam {
  common.EmptyParam globalConfigParam = 10;
  common.IdParam categoryRelatedBrandsParams = 20;
//  MidCartQueryParam cartStatParam = 30;
//  MidCartQueryParam cartInfoParam = 35;
  MidGoodsCartInfoParam goodsCartInfoParam = 30;
  common.EmptyParam categoryTreeParam = 40;
  common.IdParam categoryPathByCategoryIdParam = 51;
  common.IdParam categoryPathByGoodsIdParam = 52;
}

