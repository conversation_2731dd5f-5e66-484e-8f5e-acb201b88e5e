/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { IdResult, StringParam } from "../../common";
import { GoodsListDataResp, GoodsListPageResp } from "./model/goods_list_page_model";
import { GoodsListQueryParam } from "./param/goods_list_page_param";

export const protobufPackage = "mall.pages";

/** 商品列表页 */
export interface GoodsListPage {
  /** 获取页面数据（服务器端渲染） */
  getPageData(request: GoodsListQueryParam): Promise<GoodsListPageResp>;
  /** 搜索商品（浏览器AJAX请求） */
  searchGoods(request: GoodsListQueryParam): Promise<GoodsListDataResp>;
  /** 获取商品 */
  getGoods(request: StringParam): Promise<IdResult>;
}

export const GoodsListPageServiceName = "mall.pages.GoodsListPage";
export class GoodsListPageClientImpl implements GoodsListPage {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || GoodsListPageServiceName;
    this.rpc = rpc;
    this.getPageData = this.getPageData.bind(this);
    this.searchGoods = this.searchGoods.bind(this);
    this.getGoods = this.getGoods.bind(this);
  }
  getPageData(request: GoodsListQueryParam): Promise<GoodsListPageResp> {
    const data = GoodsListQueryParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getPageData", data);
    return promise.then((data) => GoodsListPageResp.decode(_m0.Reader.create(data)));
  }

  searchGoods(request: GoodsListQueryParam): Promise<GoodsListDataResp> {
    const data = GoodsListQueryParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "searchGoods", data);
    return promise.then((data) => GoodsListDataResp.decode(_m0.Reader.create(data)));
  }

  getGoods(request: StringParam): Promise<IdResult> {
    const data = StringParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getGoods", data);
    return promise.then((data) => IdResult.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
