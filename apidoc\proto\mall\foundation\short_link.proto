syntax = "proto3";
package mall.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/basis/model/country_model.proto";
import "chilat/basis/param/country_param.proto";
import "common.proto";
import "mall/foundation/model/short_link_model.proto";
import "mall/foundation/param/short_link_param.proto";

// 短链接服务
service ShortLink {

  // 获取短链接（传参完整URL，返回转换后的URL）
  rpc getShortLink(GetShortLinkParam) returns (GetShortLinkResp);

  // 获取完整链接（传参链接代码，返回源URL）
  rpc getFullLink(GetFullLinkParam) returns (GetFullLinkResp);

}