syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.model";

import "common.proto";
import "mall/marketing/marketing_common.proto";

//潜客信息表单提交成功返回
message PotentialUserSaveResp {
    common.Result result = 1;
    PotentialUserSaveModel data = 2;
}

//潜客信息表单提交成功
message PotentialUserSaveModel {
    int32 seqNo = 10; // 顺序号
    PotentialServiceType serviceType = 20; // 服务类型
    string whatsAppUrl = 30; //Whatsapp聊天窗口URL
}

//潜客直接点击whatsapp提交成功返回
message PotentialUserClickResp {
    common.Result result = 1;
    PotentialUserClickModel data = 2;
}

//潜客直接点击whatsapp提交成功
message PotentialUserClickModel {
    int32 seqNo = 10; // 顺序号
    string whatsAppUrl = 30; //Whatsapp聊天窗口URL
}
