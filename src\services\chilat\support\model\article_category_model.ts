/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";
import { PageType, pageTypeFromJSON, pageTypeToJSON } from "../support_common";
import Long from "long";

export const protobufPackage = "chilat.support";

export interface ArticleCategoryTreeResp {
  result: Result | undefined;
  data: ArticleCategoryTreeModel[];
}

export interface ArticleCategoryTreeModel {
  id: string;
  /** 类目名称 */
  name: string;
  /** 类目图标 */
  icon: string;
  /** 排序 */
  idx: number;
  /** 上级类目 */
  parentId: string;
  /** 是否默认显示 */
  isDefault: boolean;
  children: ArticleCategoryTreeModel[];
}

export interface ArticlePageListResp {
  result: Result | undefined;
  page: Page | undefined;
  data: ArticleModel[];
}

export interface ArticleModel {
  id: string;
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** logo */
  logo: string;
  /** 作者 */
  author: string;
  /** 简介 */
  introduce: string;
  /** 是否默认显示 */
  isDefault: boolean;
  /** 排序 */
  idx: number;
  /** 文章分类 */
  articleCategories: ArticleCategoryTreeModel[];
  /** 显示在pc上还是h5上 */
  pageType: PageType;
  pageTypeDesc: string;
  udate: number;
  /** 文章代码 */
  articleCode: string;
}

function createBaseArticleCategoryTreeResp(): ArticleCategoryTreeResp {
  return { result: undefined, data: [] };
}

export const ArticleCategoryTreeResp = {
  encode(message: ArticleCategoryTreeResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      ArticleCategoryTreeModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticleCategoryTreeResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticleCategoryTreeResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(ArticleCategoryTreeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticleCategoryTreeResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => ArticleCategoryTreeModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ArticleCategoryTreeResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => ArticleCategoryTreeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticleCategoryTreeResp>, I>>(base?: I): ArticleCategoryTreeResp {
    return ArticleCategoryTreeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticleCategoryTreeResp>, I>>(object: I): ArticleCategoryTreeResp {
    const message = createBaseArticleCategoryTreeResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => ArticleCategoryTreeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseArticleCategoryTreeModel(): ArticleCategoryTreeModel {
  return { id: "", name: "", icon: "", idx: 0, parentId: "", isDefault: false, children: [] };
}

export const ArticleCategoryTreeModel = {
  encode(message: ArticleCategoryTreeModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.icon !== "") {
      writer.uint32(26).string(message.icon);
    }
    if (message.idx !== 0) {
      writer.uint32(32).int32(message.idx);
    }
    if (message.parentId !== "") {
      writer.uint32(42).string(message.parentId);
    }
    if (message.isDefault !== false) {
      writer.uint32(48).bool(message.isDefault);
    }
    for (const v of message.children) {
      ArticleCategoryTreeModel.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticleCategoryTreeModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticleCategoryTreeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.icon = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.idx = reader.int32();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.parentId = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.isDefault = reader.bool();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.children.push(ArticleCategoryTreeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticleCategoryTreeModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      icon: isSet(object.icon) ? globalThis.String(object.icon) : "",
      idx: isSet(object.idx) ? globalThis.Number(object.idx) : 0,
      parentId: isSet(object.parentId) ? globalThis.String(object.parentId) : "",
      isDefault: isSet(object.isDefault) ? globalThis.Boolean(object.isDefault) : false,
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) => ArticleCategoryTreeModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ArticleCategoryTreeModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.icon !== "") {
      obj.icon = message.icon;
    }
    if (message.idx !== 0) {
      obj.idx = Math.round(message.idx);
    }
    if (message.parentId !== "") {
      obj.parentId = message.parentId;
    }
    if (message.isDefault !== false) {
      obj.isDefault = message.isDefault;
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) => ArticleCategoryTreeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticleCategoryTreeModel>, I>>(base?: I): ArticleCategoryTreeModel {
    return ArticleCategoryTreeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticleCategoryTreeModel>, I>>(object: I): ArticleCategoryTreeModel {
    const message = createBaseArticleCategoryTreeModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.icon = object.icon ?? "";
    message.idx = object.idx ?? 0;
    message.parentId = object.parentId ?? "";
    message.isDefault = object.isDefault ?? false;
    message.children = object.children?.map((e) => ArticleCategoryTreeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseArticlePageListResp(): ArticlePageListResp {
  return { result: undefined, page: undefined, data: [] };
}

export const ArticlePageListResp = {
  encode(message: ArticlePageListResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      ArticleModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticlePageListResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticlePageListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(ArticleModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticlePageListResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => ArticleModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: ArticlePageListResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => ArticleModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticlePageListResp>, I>>(base?: I): ArticlePageListResp {
    return ArticlePageListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticlePageListResp>, I>>(object: I): ArticlePageListResp {
    const message = createBaseArticlePageListResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => ArticleModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseArticleModel(): ArticleModel {
  return {
    id: "",
    title: "",
    content: "",
    logo: "",
    author: "",
    introduce: "",
    isDefault: false,
    idx: 0,
    articleCategories: [],
    pageType: 0,
    pageTypeDesc: "",
    udate: 0,
    articleCode: "",
  };
}

export const ArticleModel = {
  encode(message: ArticleModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    if (message.logo !== "") {
      writer.uint32(34).string(message.logo);
    }
    if (message.author !== "") {
      writer.uint32(42).string(message.author);
    }
    if (message.introduce !== "") {
      writer.uint32(50).string(message.introduce);
    }
    if (message.isDefault !== false) {
      writer.uint32(56).bool(message.isDefault);
    }
    if (message.idx !== 0) {
      writer.uint32(64).int32(message.idx);
    }
    for (const v of message.articleCategories) {
      ArticleCategoryTreeModel.encode(v!, writer.uint32(74).fork()).ldelim();
    }
    if (message.pageType !== 0) {
      writer.uint32(80).int32(message.pageType);
    }
    if (message.pageTypeDesc !== "") {
      writer.uint32(90).string(message.pageTypeDesc);
    }
    if (message.udate !== 0) {
      writer.uint32(96).int64(message.udate);
    }
    if (message.articleCode !== "") {
      writer.uint32(106).string(message.articleCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticleModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticleModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.logo = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.author = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.introduce = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.isDefault = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.idx = reader.int32();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.articleCategories.push(ArticleCategoryTreeModel.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.pageType = reader.int32() as any;
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.pageTypeDesc = reader.string();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.udate = longToNumber(reader.int64() as Long);
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.articleCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticleModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      logo: isSet(object.logo) ? globalThis.String(object.logo) : "",
      author: isSet(object.author) ? globalThis.String(object.author) : "",
      introduce: isSet(object.introduce) ? globalThis.String(object.introduce) : "",
      isDefault: isSet(object.isDefault) ? globalThis.Boolean(object.isDefault) : false,
      idx: isSet(object.idx) ? globalThis.Number(object.idx) : 0,
      articleCategories: globalThis.Array.isArray(object?.articleCategories)
        ? object.articleCategories.map((e: any) => ArticleCategoryTreeModel.fromJSON(e))
        : [],
      pageType: isSet(object.pageType) ? pageTypeFromJSON(object.pageType) : 0,
      pageTypeDesc: isSet(object.pageTypeDesc) ? globalThis.String(object.pageTypeDesc) : "",
      udate: isSet(object.udate) ? globalThis.Number(object.udate) : 0,
      articleCode: isSet(object.articleCode) ? globalThis.String(object.articleCode) : "",
    };
  },

  toJSON(message: ArticleModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.logo !== "") {
      obj.logo = message.logo;
    }
    if (message.author !== "") {
      obj.author = message.author;
    }
    if (message.introduce !== "") {
      obj.introduce = message.introduce;
    }
    if (message.isDefault !== false) {
      obj.isDefault = message.isDefault;
    }
    if (message.idx !== 0) {
      obj.idx = Math.round(message.idx);
    }
    if (message.articleCategories?.length) {
      obj.articleCategories = message.articleCategories.map((e) => ArticleCategoryTreeModel.toJSON(e));
    }
    if (message.pageType !== 0) {
      obj.pageType = pageTypeToJSON(message.pageType);
    }
    if (message.pageTypeDesc !== "") {
      obj.pageTypeDesc = message.pageTypeDesc;
    }
    if (message.udate !== 0) {
      obj.udate = Math.round(message.udate);
    }
    if (message.articleCode !== "") {
      obj.articleCode = message.articleCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticleModel>, I>>(base?: I): ArticleModel {
    return ArticleModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticleModel>, I>>(object: I): ArticleModel {
    const message = createBaseArticleModel();
    message.id = object.id ?? "";
    message.title = object.title ?? "";
    message.content = object.content ?? "";
    message.logo = object.logo ?? "";
    message.author = object.author ?? "";
    message.introduce = object.introduce ?? "";
    message.isDefault = object.isDefault ?? false;
    message.idx = object.idx ?? 0;
    message.articleCategories = object.articleCategories?.map((e) => ArticleCategoryTreeModel.fromPartial(e)) || [];
    message.pageType = object.pageType ?? 0;
    message.pageTypeDesc = object.pageTypeDesc ?? "";
    message.udate = object.udate ?? 0;
    message.articleCode = object.articleCode ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
