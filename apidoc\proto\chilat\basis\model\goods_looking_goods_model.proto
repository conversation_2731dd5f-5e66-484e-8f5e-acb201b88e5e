syntax = "proto3";
package chilat.basis;

import "common.proto";

option java_package = "com.chilat.rpc.basis.model";

message GoodsLookingGoodsModelResp {
  common.Result result = 1;
  repeated GoodsLookingGoodsModel data =2;
}

message GoodsLookingGoodsModel {
  string goodsLookingId = 1;
  string skuId = 2;
  string skuNo = 3;
  string skuImage = 4;
  string specsInfo = 5;
  double salePrice = 6;
  string goodsId = 7;
  string goodsName = 8;
  string goodsNo = 9;
  string goodsImage = 10;
  int32 buyQuantity = 11;
  string id = 12;
}