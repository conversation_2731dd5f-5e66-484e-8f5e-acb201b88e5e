<template>
  <div class="page-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div class="pb-[1rem]">
      <div
        class="px-[0.24rem] py-[0.3rem] text-center relative border-t-1 border-b-1 border-[#E6E6E6]"
      >
        <img loading="lazy"
          alt="back"
          src="@/assets/icons/arrowLeft.svg"
          class="w-[0.24rem] absolute"
          @click="onBackClick"
        />
        <div class="text-[0.36rem] leading-[0.36rem] font-medium">
          Preguntas frecuentes
        </div>
      </div>
      <n-collapse
        :show-arrow="false"
        :on-update:expanded-names="onUpdateExpandedNames"
        :default-expanded-names="pageData.defaultExpandedNames"
        class="px-[0.32rem]"
      >
        <n-collapse-item
          v-for="(column, index) in pageData.columnList"
          :key="column.id"
          :name="column.id"
          class="pt-[0.36rem]"
          :class="
            pageData.expandedColumnIds.includes(column.id)
              ? 'pb-0 text-[#e50113]'
              : 'pb-[0.36rem]'
          "
        >
          <template #header>
            <div
              class="text-[0.32rem] leading-[0.32rem]"
              :class="{
                'text-[#e50113]': pageData.expandedColumnIds.includes(
                  column.id
                ),
              }"
            >
              {{ index + 1 }}. {{ column.name }}
            </div>
          </template>
          <template #header-extra="{ collapsed }">
            <img loading="lazy"
              v-if="collapsed"
              alt="expand"
              class="w-[0.3rem] ml-[0.32rem]"
              src="@/assets/icons/article/downArrow.svg"
            />
            <img loading="lazy"
              v-else
              alt="collapse"
              class="w-[0.3rem] ml-[0.32rem]"
              src="@/assets/icons/article/topArrow.svg"
            />
          </template>
          <template #arrow><span class="hidden"></span></template>
          <div
            class="border-t-1 border-[#F2F2F2]"
            data-spm-box="faq-article-list"
          >
            <a
              :data-spm-index="articleIndex + 1"
              :href="`/h5/article/faq?id=${encodeURIComponent(
                article.id
              )}&title=${encodeURIComponent(
                article.title
              )}&columnId=${encodeURIComponent(
                column.id
              )}&columnTitle=${encodeURIComponent(column.name)}`"
              class="text-[0.28rem] leading-[0.42rem] text-[#4D4D4D] ml-[0.6rem] block py-[0.24rem]"
              :class="{
                'border-b-1 border-[#F2F2F2]':
                  articleIndex !== column.articleList.length - 1,
              }"
              v-for="(article, articleIndex) in column.articleList"
              :key="article.id"
              @click="onUpdateUrl(column.id)"
            >
              {{ article.title }}
            </a>
          </div>
        </n-collapse-item>
      </n-collapse>
    </div>

    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const pageData = reactive(<any>{
  columnId: route?.query?.columnId || "",
  columnList: <any>[],
  selectedColumn: <any>{},
  expandedColumnIds: <any>[],
  defaultExpandedNames: <any>[],
});

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");
});

await onGetColumnList();
async function onGetColumnList() {
  const res: any = await useListArticleCategory({
    names: ["Preguntas Frecuentes"],
  });
  if (res?.result?.code === 200) {
    pageData.columnList = res?.data[0]?.children;
    nextTick(async () => {
      if (pageData.columnId) {
        const index = pageData.columnList.findIndex(
          (col: any) => col.id === pageData.columnId
        );
        const column = pageData.columnList[index];
        const articleList = await onGetArticleList(pageData.columnId);
        if (articleList) {
          column.articleList = articleList; // 将文章列表保存到栏目对象中
        }
        pageData.defaultExpandedNames.push(pageData.columnId);
        pageData.expandedColumnIds.push(pageData.columnId);
        window?.MyStat?.addPageEvent(
          "faq_view_categories",
          `查看FAQ分类：${index + 1}. ${column.name}`
        ); // 埋点
      }
    });
  } else {
    showToast(res.result?.message);
  }
}

async function onGetArticleList(columnId: string) {
  const res: any = await useListArticleByCategoryId({
    deviceType: "VISIT_DEVICE_TYPE_PC",
    articleCategoryId: columnId,
  });
  if (res?.result?.code === 200) {
    return res?.data; // 返回文章列表
  } else {
    showToast(res.result?.message);
    return null; // 返回 null 表示失败
  }
}

async function onUpdateExpandedNames(ids: any[]) {
  for (const columnId of ids) {
    // 通过 columnId 查找栏目，并判断是否已加载文章列表
    const columnIndex = pageData.columnList.findIndex(
      (col: any) => col.id === columnId
    );

    if (columnIndex !== -1) {
      const column = pageData.columnList[columnIndex];

      if (column && !column.articleList) {
        window?.MyStat?.addPageEvent(
          "faq_view_categories",
          `查看FAQ分类：${columnIndex + 1}. ${column.name}`
        );

        const articleList = await onGetArticleList(columnId);
        if (articleList) {
          column.articleList = articleList; // 将文章列表保存到栏目对象中
        }
      }
    }
  }

  // 更新展开的栏目 ID
  pageData.expandedColumnIds = ids;
}

/** 返回上一页 */
function onBackClick() {
  router.go(-1);
}

function onUpdateUrl(columnId: string) {
  const url = new URL(window.location.href);
  url.searchParams.set("columnId", columnId);
  window.history.replaceState(null, "", url.toString());
}
</script>

<style scoped lang="scss">
.page-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
  font-size: 0.28rem;
}

:deep(
    .n-collapse
      .n-collapse-item
      .n-collapse-item__content-wrapper
      .n-collapse-item__content-inner
  ) {
  padding-top: 0.36rem;
}
:deep(.n-collapse .n-collapse-item) {
  margin: 0;
}
:deep(.n-collapse .n-collapse-item .n-collapse-item__header) {
  padding: 0;
}
:deep(.n-collapse .n-collapse-item:last-child) {
  border-bottom: 0.02rem solid rgb(239, 239, 245);
}
</style>
