/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import {
  HomePageCategoryResp,
  HomePageGoodsResp,
  MidRecommendGoodsResp,
  MidRecommendGoodsV2Resp,
  PcHomePageGoodsResp,
} from "../../chilat/basis/model/goods_model";
import { RecommendGoodsParam } from "../../chilat/basis/param/goods_param";
import { EmptyParam } from "../../common";
import { HomePageResp } from "./model/home_page_model";

export const protobufPackage = "mall.pages";

/** 首页 */
export interface HomePage {
  /** 获取页面数据 */
  getPageData(request: EmptyParam): Promise<HomePageResp>;
  /** 推荐商品 */
  recommendGoods(request: EmptyParam): Promise<MidRecommendGoodsResp>;
  /** 首页商品 */
  homePageGoods(request: EmptyParam): Promise<HomePageGoodsResp>;
  /** pc首页 */
  pcHomePageGoods(request: EmptyParam): Promise<PcHomePageGoodsResp>;
  /** pc首页分类 */
  homePageCategory(request: EmptyParam): Promise<HomePageCategoryResp>;
  /** 推荐商品v2,包含义乌热销和美客多热销 */
  recommendGoodsV2(request: RecommendGoodsParam): Promise<MidRecommendGoodsV2Resp>;
}

export const HomePageServiceName = "mall.pages.HomePage";
export class HomePageClientImpl implements HomePage {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || HomePageServiceName;
    this.rpc = rpc;
    this.getPageData = this.getPageData.bind(this);
    this.recommendGoods = this.recommendGoods.bind(this);
    this.homePageGoods = this.homePageGoods.bind(this);
    this.pcHomePageGoods = this.pcHomePageGoods.bind(this);
    this.homePageCategory = this.homePageCategory.bind(this);
    this.recommendGoodsV2 = this.recommendGoodsV2.bind(this);
  }
  getPageData(request: EmptyParam): Promise<HomePageResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getPageData", data);
    return promise.then((data) => HomePageResp.decode(_m0.Reader.create(data)));
  }

  recommendGoods(request: EmptyParam): Promise<MidRecommendGoodsResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "recommendGoods", data);
    return promise.then((data) => MidRecommendGoodsResp.decode(_m0.Reader.create(data)));
  }

  homePageGoods(request: EmptyParam): Promise<HomePageGoodsResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "homePageGoods", data);
    return promise.then((data) => HomePageGoodsResp.decode(_m0.Reader.create(data)));
  }

  pcHomePageGoods(request: EmptyParam): Promise<PcHomePageGoodsResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "pcHomePageGoods", data);
    return promise.then((data) => PcHomePageGoodsResp.decode(_m0.Reader.create(data)));
  }

  homePageCategory(request: EmptyParam): Promise<HomePageCategoryResp> {
    const data = EmptyParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "homePageCategory", data);
    return promise.then((data) => HomePageCategoryResp.decode(_m0.Reader.create(data)));
  }

  recommendGoodsV2(request: RecommendGoodsParam): Promise<MidRecommendGoodsV2Resp> {
    const data = RecommendGoodsParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "recommendGoodsV2", data);
    return promise.then((data) => MidRecommendGoodsV2Resp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
