syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_looking_param.proto";
import "chilat/commodity/model/goods_looking_model.proto";
import "chilat/user/param/user_param.proto";
import "common.proto";

// 询盘管理
service GoodsLooking {
  // 询盘列表
  rpc pageList (GoodsLookingPageQueryParam) returns (GoodsLookingPageResp);
  // 开始回复
  rpc start (GoodsLookingRemarkParam) returns (common.ApiResult);
  // 结束回复
  rpc finish (GoodsLookingRemarkParam) returns (common.ApiResult);
  // 备注
  rpc remark (GoodsLookingRemarkParam) returns (common.ApiResult);
  // 日志
  rpc log (common.IdParam) returns (common.LogResult);
  // 分配
  rpc assign (GoodsLookingAssignParam) returns (common.ApiResult);
  // 变更负责人
  rpc transfer (GoodsLookingTransferParam) returns (common.ApiResult);
  // 流转
  rpc flowStatus(FlowStatusParam) returns (common.ApiResult);
  // 导出询盘
  rpc exportGoodsLooking (GoodsLookingQueryParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
  // 询盘模板下载
  rpc downloadTemplate (GoodsLookingTemplateTypeParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
  // 更新询盘
  rpc importGoodsLooking (common.EmptyParam) returns (common.ImportResult) {
    option (common.webapi).upload = true;
  };
  // 更新包装
  rpc importGoodsPackage (common.EmptyParam) returns (common.ImportResult) {
    option (common.webapi).upload = true;
  };
  // 查询历史询盘数
  rpc queryHistoryGoodsLooking(PageHistoryGoodsLookingParam) returns (GoodsLookingPageResp);
  // 详情
  rpc detail(common.IdParam) returns (GoodsLookingDetailResp);
  // 同步询盘云归属业务员
  rpc saveOriginFinder(common.IdsParam) returns (common.ApiResult);
  // 同步询盘云归属业务员
  rpc saveFinder(common.IdsParam) returns (common.ApiResult);

  // 添加采购员
  rpc savePurchaser(user.PurchaserSaveParam) returns (common.ApiResult);

  // 推送询盘单到询盘云（参数为询盘单号）
  rpc pushXunPanYun(common.CodeParam) returns (common.ApiResult);
}