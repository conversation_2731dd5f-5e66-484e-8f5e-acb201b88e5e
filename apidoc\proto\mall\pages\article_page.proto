syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "chilat/support/model/article_category_model.proto";
import "mall/pages/param/article_page_param.proto";
import "mall/pages/model/article_page_model.proto";

service ArticlePage {
  // 查询栏目
  rpc listArticleCategory(ListArticleCategoryParam) returns (chilat.support.ArticleCategoryTreeResp);
  // 根据栏目查询文章
  rpc listArticleByCategoryId(ListArticleParam) returns (ListArticleResp);
  // 获取BLOG列表
  rpc getBlogList(GetBlogListParam) returns (GetBlogListResp);
  // 查询文章详情
  rpc articleDetail(ArticleDetailParam) returns (ArticleResp);
  //  根据筛选条件查询文章列表
  rpc searchArticleList(SearchArticleListParam) returns (SearchArticleListResp);
}