syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "common.proto";

message GetCancelOrderReasonResp {
  common.Result result = 1; //错误码信息
  CancelOrderReasonListModel data = 2; //返回数据
}

message CancelOrderReasonListModel {
  repeated ReasonModel reasonList = 10; //取消订单理由列表
}

message ReasonModel {
  string id = 10; //取消订单理由id
  string reason = 20; //取消订单理由描述
}