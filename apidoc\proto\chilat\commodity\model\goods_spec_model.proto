syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";

message GoodsSpecPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsSpecModel data = 3;
}

message GoodsSpecDetailResp {
    common.Result result = 1;
    GoodsSpecModel data = 2;
}

// 商品规格信息
message GoodsSpecModel {
    string id = 1; //规格ID
    int32 idx = 2; //顺序
    string specName = 3; //规格名称
    string remark = 4; //规格备注
    bool enabled = 5; //是否启用
    repeated string categoryIds = 6; //类目id列表
    repeated string categoryPaths = 7; //多选类目树列表
    int32 relatedGoodsCount = 8; //关联的商品数量（详情接口返回）
}
