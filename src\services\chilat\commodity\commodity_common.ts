/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, PageParam, Result } from "../../common";
import Long from "long";

export const protobufPackage = "chilat.commodity";

export enum GoodsLookingState {
  GOODS_LOOKING_STATE_UNKNOWN = 0,
  /** GOODS_LOOKING_STATE_ASSIGN - 待分配 */
  GOODS_LOOKING_STATE_ASSIGN = 1,
  /** GOODS_LOOKING_STATE_WAIT - 待回复 */
  GOODS_LOOKING_STATE_WAIT = 2,
  /** GOODS_LOOKING_STATE_FINDING - 回复中 */
  GOODS_LOOKING_STATE_FINDING = 3,
  /** GOODS_LOOKING_STATE_FINISH - 已回复 */
  GOODS_LOOKING_STATE_FINISH = 4,
  UNRECOGNIZED = -1,
}

export function goodsLookingStateFromJSON(object: any): GoodsLookingState {
  switch (object) {
    case 0:
    case "GOODS_LOOKING_STATE_UNKNOWN":
      return GoodsLookingState.GOODS_LOOKING_STATE_UNKNOWN;
    case 1:
    case "GOODS_LOOKING_STATE_ASSIGN":
      return GoodsLookingState.GOODS_LOOKING_STATE_ASSIGN;
    case 2:
    case "GOODS_LOOKING_STATE_WAIT":
      return GoodsLookingState.GOODS_LOOKING_STATE_WAIT;
    case 3:
    case "GOODS_LOOKING_STATE_FINDING":
      return GoodsLookingState.GOODS_LOOKING_STATE_FINDING;
    case 4:
    case "GOODS_LOOKING_STATE_FINISH":
      return GoodsLookingState.GOODS_LOOKING_STATE_FINISH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsLookingState.UNRECOGNIZED;
  }
}

export function goodsLookingStateToJSON(object: GoodsLookingState): string {
  switch (object) {
    case GoodsLookingState.GOODS_LOOKING_STATE_UNKNOWN:
      return "GOODS_LOOKING_STATE_UNKNOWN";
    case GoodsLookingState.GOODS_LOOKING_STATE_ASSIGN:
      return "GOODS_LOOKING_STATE_ASSIGN";
    case GoodsLookingState.GOODS_LOOKING_STATE_WAIT:
      return "GOODS_LOOKING_STATE_WAIT";
    case GoodsLookingState.GOODS_LOOKING_STATE_FINDING:
      return "GOODS_LOOKING_STATE_FINDING";
    case GoodsLookingState.GOODS_LOOKING_STATE_FINISH:
      return "GOODS_LOOKING_STATE_FINISH";
    case GoodsLookingState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GoodsOnlineState {
  /** GOODS_ONLINE_STATE_UNKNOWN - 查询条件，表示不限 */
  GOODS_ONLINE_STATE_UNKNOWN = 0,
  /** GOODS_ONLINE_STATE_ONLINE - 上架状态 */
  GOODS_ONLINE_STATE_ONLINE = 1,
  /** GOODS_ONLINE_STATE_OFFLINE - 下架状态 */
  GOODS_ONLINE_STATE_OFFLINE = 2,
  /** GOODS_ONLINE_STATE_DRAFT - 未发布状态 */
  GOODS_ONLINE_STATE_DRAFT = 3,
  /** GOODS_ONLINE_STATE_RECYCLE - 已回收 */
  GOODS_ONLINE_STATE_RECYCLE = 4,
  UNRECOGNIZED = -1,
}

export function goodsOnlineStateFromJSON(object: any): GoodsOnlineState {
  switch (object) {
    case 0:
    case "GOODS_ONLINE_STATE_UNKNOWN":
      return GoodsOnlineState.GOODS_ONLINE_STATE_UNKNOWN;
    case 1:
    case "GOODS_ONLINE_STATE_ONLINE":
      return GoodsOnlineState.GOODS_ONLINE_STATE_ONLINE;
    case 2:
    case "GOODS_ONLINE_STATE_OFFLINE":
      return GoodsOnlineState.GOODS_ONLINE_STATE_OFFLINE;
    case 3:
    case "GOODS_ONLINE_STATE_DRAFT":
      return GoodsOnlineState.GOODS_ONLINE_STATE_DRAFT;
    case 4:
    case "GOODS_ONLINE_STATE_RECYCLE":
      return GoodsOnlineState.GOODS_ONLINE_STATE_RECYCLE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsOnlineState.UNRECOGNIZED;
  }
}

export function goodsOnlineStateToJSON(object: GoodsOnlineState): string {
  switch (object) {
    case GoodsOnlineState.GOODS_ONLINE_STATE_UNKNOWN:
      return "GOODS_ONLINE_STATE_UNKNOWN";
    case GoodsOnlineState.GOODS_ONLINE_STATE_ONLINE:
      return "GOODS_ONLINE_STATE_ONLINE";
    case GoodsOnlineState.GOODS_ONLINE_STATE_OFFLINE:
      return "GOODS_ONLINE_STATE_OFFLINE";
    case GoodsOnlineState.GOODS_ONLINE_STATE_DRAFT:
      return "GOODS_ONLINE_STATE_DRAFT";
    case GoodsOnlineState.GOODS_ONLINE_STATE_RECYCLE:
      return "GOODS_ONLINE_STATE_RECYCLE";
    case GoodsOnlineState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GoodsAttributeItemType {
  GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN = 0,
  /** GOODS_ATTRIBUTE_ITEM_TYPE_INPUT - 输入框 */
  GOODS_ATTRIBUTE_ITEM_TYPE_INPUT = 10,
  /** GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION - 多选项 */
  GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION = 20,
  /** GOODS_ATTRIBUTE_ITEM_TYPE_SELECT - 下拉框 */
  GOODS_ATTRIBUTE_ITEM_TYPE_SELECT = 30,
  UNRECOGNIZED = -1,
}

export function goodsAttributeItemTypeFromJSON(object: any): GoodsAttributeItemType {
  switch (object) {
    case 0:
    case "GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN":
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN;
    case 10:
    case "GOODS_ATTRIBUTE_ITEM_TYPE_INPUT":
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_INPUT;
    case 20:
    case "GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION":
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION;
    case 30:
    case "GOODS_ATTRIBUTE_ITEM_TYPE_SELECT":
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_SELECT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsAttributeItemType.UNRECOGNIZED;
  }
}

export function goodsAttributeItemTypeToJSON(object: GoodsAttributeItemType): string {
  switch (object) {
    case GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN:
      return "GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN";
    case GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_INPUT:
      return "GOODS_ATTRIBUTE_ITEM_TYPE_INPUT";
    case GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION:
      return "GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION";
    case GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_SELECT:
      return "GOODS_ATTRIBUTE_ITEM_TYPE_SELECT";
    case GoodsAttributeItemType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 库存操作类型 */
export enum GoodsStockOperateType {
  GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED = 0,
  /** GOODS_STOCK_OPERATE_TYPE_INCREASE - 增加库存 */
  GOODS_STOCK_OPERATE_TYPE_INCREASE = 1,
  /** GOODS_STOCK_OPERATE_TYPE_REDUCE - 减少库存 */
  GOODS_STOCK_OPERATE_TYPE_REDUCE = 2,
  UNRECOGNIZED = -1,
}

export function goodsStockOperateTypeFromJSON(object: any): GoodsStockOperateType {
  switch (object) {
    case 0:
    case "GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED":
      return GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED;
    case 1:
    case "GOODS_STOCK_OPERATE_TYPE_INCREASE":
      return GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_INCREASE;
    case 2:
    case "GOODS_STOCK_OPERATE_TYPE_REDUCE":
      return GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_REDUCE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsStockOperateType.UNRECOGNIZED;
  }
}

export function goodsStockOperateTypeToJSON(object: GoodsStockOperateType): string {
  switch (object) {
    case GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED:
      return "GOODS_STOCK_OPERATE_TYPE_UNSPECIFIED";
    case GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_INCREASE:
      return "GOODS_STOCK_OPERATE_TYPE_INCREASE";
    case GoodsStockOperateType.GOODS_STOCK_OPERATE_TYPE_REDUCE:
      return "GOODS_STOCK_OPERATE_TYPE_REDUCE";
    case GoodsStockOperateType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 商品来源类型 */
export enum GoodsSourceType {
  GOODS_SOURCE_TYPE_UNSPECIFIED = 0,
  /** GOODS_SOURCE_TYPE_CRAWL - 人工抓取 */
  GOODS_SOURCE_TYPE_CRAWL = 1,
  /** GOODS_SOURCE_TYPE_1688API - 1688同步 */
  GOODS_SOURCE_TYPE_1688API = 2,
  /** GOODS_SOURCE_TYPE_PUBLISH - 人工创建 */
  GOODS_SOURCE_TYPE_PUBLISH = 3,
  /** GOODS_SOURCE_TYPE_IMAGE - 以图搜图 */
  GOODS_SOURCE_TYPE_IMAGE = 4,
  UNRECOGNIZED = -1,
}

export function goodsSourceTypeFromJSON(object: any): GoodsSourceType {
  switch (object) {
    case 0:
    case "GOODS_SOURCE_TYPE_UNSPECIFIED":
      return GoodsSourceType.GOODS_SOURCE_TYPE_UNSPECIFIED;
    case 1:
    case "GOODS_SOURCE_TYPE_CRAWL":
      return GoodsSourceType.GOODS_SOURCE_TYPE_CRAWL;
    case 2:
    case "GOODS_SOURCE_TYPE_1688API":
      return GoodsSourceType.GOODS_SOURCE_TYPE_1688API;
    case 3:
    case "GOODS_SOURCE_TYPE_PUBLISH":
      return GoodsSourceType.GOODS_SOURCE_TYPE_PUBLISH;
    case 4:
    case "GOODS_SOURCE_TYPE_IMAGE":
      return GoodsSourceType.GOODS_SOURCE_TYPE_IMAGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsSourceType.UNRECOGNIZED;
  }
}

export function goodsSourceTypeToJSON(object: GoodsSourceType): string {
  switch (object) {
    case GoodsSourceType.GOODS_SOURCE_TYPE_UNSPECIFIED:
      return "GOODS_SOURCE_TYPE_UNSPECIFIED";
    case GoodsSourceType.GOODS_SOURCE_TYPE_CRAWL:
      return "GOODS_SOURCE_TYPE_CRAWL";
    case GoodsSourceType.GOODS_SOURCE_TYPE_1688API:
      return "GOODS_SOURCE_TYPE_1688API";
    case GoodsSourceType.GOODS_SOURCE_TYPE_PUBLISH:
      return "GOODS_SOURCE_TYPE_PUBLISH";
    case GoodsSourceType.GOODS_SOURCE_TYPE_IMAGE:
      return "GOODS_SOURCE_TYPE_IMAGE";
    case GoodsSourceType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 源商品状态 */
export enum SourceGoodsState {
  SOURCE_GOODS_STATE_UNSPECIFIED = 0,
  /** SOURCE_GOODS_STATE_ACTIVE - 已生效 */
  SOURCE_GOODS_STATE_ACTIVE = 1,
  /** SOURCE_GOODS_STATE_INACTIVE - 已失效 */
  SOURCE_GOODS_STATE_INACTIVE = 2,
  UNRECOGNIZED = -1,
}

export function sourceGoodsStateFromJSON(object: any): SourceGoodsState {
  switch (object) {
    case 0:
    case "SOURCE_GOODS_STATE_UNSPECIFIED":
      return SourceGoodsState.SOURCE_GOODS_STATE_UNSPECIFIED;
    case 1:
    case "SOURCE_GOODS_STATE_ACTIVE":
      return SourceGoodsState.SOURCE_GOODS_STATE_ACTIVE;
    case 2:
    case "SOURCE_GOODS_STATE_INACTIVE":
      return SourceGoodsState.SOURCE_GOODS_STATE_INACTIVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SourceGoodsState.UNRECOGNIZED;
  }
}

export function sourceGoodsStateToJSON(object: SourceGoodsState): string {
  switch (object) {
    case SourceGoodsState.SOURCE_GOODS_STATE_UNSPECIFIED:
      return "SOURCE_GOODS_STATE_UNSPECIFIED";
    case SourceGoodsState.SOURCE_GOODS_STATE_ACTIVE:
      return "SOURCE_GOODS_STATE_ACTIVE";
    case SourceGoodsState.SOURCE_GOODS_STATE_INACTIVE:
      return "SOURCE_GOODS_STATE_INACTIVE";
    case SourceGoodsState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 商品更新类型 */
export enum GoodsUpdateType {
  GOODS_UPDATE_TYPE_UNSPECIFIED = 0,
  /** GOODS_UPDATE_TYPE_AUTO - 程序自动更新（1688同步更新，1688同步创建商品时默认选中） */
  GOODS_UPDATE_TYPE_AUTO = 1,
  /** GOODS_UPDATE_TYPE_MANUAL - 手工更新 */
  GOODS_UPDATE_TYPE_MANUAL = 2,
  UNRECOGNIZED = -1,
}

export function goodsUpdateTypeFromJSON(object: any): GoodsUpdateType {
  switch (object) {
    case 0:
    case "GOODS_UPDATE_TYPE_UNSPECIFIED":
      return GoodsUpdateType.GOODS_UPDATE_TYPE_UNSPECIFIED;
    case 1:
    case "GOODS_UPDATE_TYPE_AUTO":
      return GoodsUpdateType.GOODS_UPDATE_TYPE_AUTO;
    case 2:
    case "GOODS_UPDATE_TYPE_MANUAL":
      return GoodsUpdateType.GOODS_UPDATE_TYPE_MANUAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsUpdateType.UNRECOGNIZED;
  }
}

export function goodsUpdateTypeToJSON(object: GoodsUpdateType): string {
  switch (object) {
    case GoodsUpdateType.GOODS_UPDATE_TYPE_UNSPECIFIED:
      return "GOODS_UPDATE_TYPE_UNSPECIFIED";
    case GoodsUpdateType.GOODS_UPDATE_TYPE_AUTO:
      return "GOODS_UPDATE_TYPE_AUTO";
    case GoodsUpdateType.GOODS_UPDATE_TYPE_MANUAL:
      return "GOODS_UPDATE_TYPE_MANUAL";
    case GoodsUpdateType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 商品审核状态 */
export enum GoodsAuditState {
  GOODS_AUDIT_STATE_UNSPECIFIED = 0,
  /** GOODS_AUDIT_STATE_TO_ASSIGN - 待分配 */
  GOODS_AUDIT_STATE_TO_ASSIGN = 1,
  /** GOODS_AUDIT_STATE_TO_AUDIT - 待校验 */
  GOODS_AUDIT_STATE_TO_AUDIT = 2,
  /** GOODS_AUDIT_STATE_AUDITING - 校验中 */
  GOODS_AUDIT_STATE_AUDITING = 3,
  /** GOODS_AUDIT_STATE_FINISH - 校验完成 */
  GOODS_AUDIT_STATE_FINISH = 4,
  UNRECOGNIZED = -1,
}

export function goodsAuditStateFromJSON(object: any): GoodsAuditState {
  switch (object) {
    case 0:
    case "GOODS_AUDIT_STATE_UNSPECIFIED":
      return GoodsAuditState.GOODS_AUDIT_STATE_UNSPECIFIED;
    case 1:
    case "GOODS_AUDIT_STATE_TO_ASSIGN":
      return GoodsAuditState.GOODS_AUDIT_STATE_TO_ASSIGN;
    case 2:
    case "GOODS_AUDIT_STATE_TO_AUDIT":
      return GoodsAuditState.GOODS_AUDIT_STATE_TO_AUDIT;
    case 3:
    case "GOODS_AUDIT_STATE_AUDITING":
      return GoodsAuditState.GOODS_AUDIT_STATE_AUDITING;
    case 4:
    case "GOODS_AUDIT_STATE_FINISH":
      return GoodsAuditState.GOODS_AUDIT_STATE_FINISH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsAuditState.UNRECOGNIZED;
  }
}

export function goodsAuditStateToJSON(object: GoodsAuditState): string {
  switch (object) {
    case GoodsAuditState.GOODS_AUDIT_STATE_UNSPECIFIED:
      return "GOODS_AUDIT_STATE_UNSPECIFIED";
    case GoodsAuditState.GOODS_AUDIT_STATE_TO_ASSIGN:
      return "GOODS_AUDIT_STATE_TO_ASSIGN";
    case GoodsAuditState.GOODS_AUDIT_STATE_TO_AUDIT:
      return "GOODS_AUDIT_STATE_TO_AUDIT";
    case GoodsAuditState.GOODS_AUDIT_STATE_AUDITING:
      return "GOODS_AUDIT_STATE_AUDITING";
    case GoodsAuditState.GOODS_AUDIT_STATE_FINISH:
      return "GOODS_AUDIT_STATE_FINISH";
    case GoodsAuditState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 商品属性 */
export interface GoodsAttrModel {
  /** 属性ID */
  attrId: string;
  /** 属性名称 */
  attrName: string;
  /** 属性值（多个属性值用空格分隔） */
  attrValue: string;
  /** 属性类型 */
  attributeItemType: GoodsAttributeItemType;
  /** 属性值（数组类型，支持多个） */
  values: string[];
}

/** 商品信息选项 */
export interface GoodsExtendItem {
  /** 字典ID */
  groupId: string;
  /** 字典名称（例：商品规格标题；商品属性名称） */
  groupName: string;
  /** 字典名称中文 */
  groupNameCn: string;
  /** item id */
  itemId: string;
  /** item名称（例：商品规格名称；商品属性值） */
  itemName: string;
  /** item名称中文 */
  itemNameCn: string;
  /** item别名（例：商品规格别名） */
  itemAlias: string;
  /** 图片链接（例：SKU图片） */
  imageUrl: string;
  /** 颜色值（格式：# + 6位16进制数，例：#CCCCCC） */
  color: string;
  /** 是否启动，默认启用（例：商品属性的显示开关） */
  isActive: boolean;
}

/** SKU信息 */
export interface SkuItem {
  /** SKU ID（商品发布时，空值表示新增） */
  id: string;
  /** SKU货号（由后端生成） */
  skuNo: string;
  /** 规格信息（商品发布时，必填：groupName规格标题，itemName规格标题） */
  specList: GoodsExtendItem[];
  /** double salePrice = 4; // 销售价（必填） */
  stepPrices: SkuStepPriceAdmin[];
  /** 上架状态（由后端赋值） */
  skuState: GoodsOnlineState;
  /** 上架状态描述（由后端赋值） */
  skuStateDesc: string;
  /** 源商品ID（1688同步时，附带） */
  sourceSkuId: string;
  /** 库存 */
  amountOnSale: number;
  /** 图片链接 */
  imageUrl: string;
  /** 1688SKU规格标识 */
  sourceSpecId: string;
}

/** 商品操作日志查询参数 */
export interface GoodsOperationLogQueryParam {
  /** 分页查询参数 */
  page:
    | PageParam
    | undefined;
  /** 数据ID（例：商品ID） */
  id: string;
  /** 在商品操作日志中，切换到“SKU日志”TAB时传值true（id参数仍为商品ID） */
  querySkuLogFlag: boolean;
}

/** 商品操作日志 */
export interface GoodsOperationLogResp {
  result: Result | undefined;
  page: Page | undefined;
  data: GoodsOperationLogModel[];
}

export interface GoodsOperationLogModel {
  /** ID */
  id: string;
  /** 操作类型（新增、修改、删除或更详细的操作说明） */
  operation: string;
  /** 操作内容（html格式） */
  content: string;
  /** 操作人 */
  operator: string;
  /** 操作时间 */
  operateTime: number;
}

/** SKU阶梯价格（admin专用） */
export interface SkuStepPriceAdmin {
  /** 起始件数（第一个阶梯的start，总是从1开始） */
  start: number;
  /** 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1） */
  end: number;
  /** 阶梯价（最小售卖单位的价格，即商品价格单位对应的价格；第一个阶梯的salePrice总是与sku中的salePrice相同） */
  price: number;
  /** 内含单件商品的价格（与包装含量相关；chilat2.0中的包装含量总是1，因此insideOnePrice总是与stepPrice相等） */
  insideOnePrice: number;
  /** 供应商价格（采购价；与阶梯价内含数量单位相同） */
  supplierPrice: number;
}

function createBaseGoodsAttrModel(): GoodsAttrModel {
  return { attrId: "", attrName: "", attrValue: "", attributeItemType: 0, values: [] };
}

export const GoodsAttrModel = {
  encode(message: GoodsAttrModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.attrId !== "") {
      writer.uint32(82).string(message.attrId);
    }
    if (message.attrName !== "") {
      writer.uint32(162).string(message.attrName);
    }
    if (message.attrValue !== "") {
      writer.uint32(202).string(message.attrValue);
    }
    if (message.attributeItemType !== 0) {
      writer.uint32(240).int32(message.attributeItemType);
    }
    for (const v of message.values) {
      writer.uint32(322).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsAttrModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsAttrModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.attrId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.attrName = reader.string();
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.attrValue = reader.string();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.attributeItemType = reader.int32() as any;
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsAttrModel {
    return {
      attrId: isSet(object.attrId) ? globalThis.String(object.attrId) : "",
      attrName: isSet(object.attrName) ? globalThis.String(object.attrName) : "",
      attrValue: isSet(object.attrValue) ? globalThis.String(object.attrValue) : "",
      attributeItemType: isSet(object.attributeItemType) ? goodsAttributeItemTypeFromJSON(object.attributeItemType) : 0,
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: GoodsAttrModel): unknown {
    const obj: any = {};
    if (message.attrId !== "") {
      obj.attrId = message.attrId;
    }
    if (message.attrName !== "") {
      obj.attrName = message.attrName;
    }
    if (message.attrValue !== "") {
      obj.attrValue = message.attrValue;
    }
    if (message.attributeItemType !== 0) {
      obj.attributeItemType = goodsAttributeItemTypeToJSON(message.attributeItemType);
    }
    if (message.values?.length) {
      obj.values = message.values;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsAttrModel>, I>>(base?: I): GoodsAttrModel {
    return GoodsAttrModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsAttrModel>, I>>(object: I): GoodsAttrModel {
    const message = createBaseGoodsAttrModel();
    message.attrId = object.attrId ?? "";
    message.attrName = object.attrName ?? "";
    message.attrValue = object.attrValue ?? "";
    message.attributeItemType = object.attributeItemType ?? 0;
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseGoodsExtendItem(): GoodsExtendItem {
  return {
    groupId: "",
    groupName: "",
    groupNameCn: "",
    itemId: "",
    itemName: "",
    itemNameCn: "",
    itemAlias: "",
    imageUrl: "",
    color: "",
    isActive: false,
  };
}

export const GoodsExtendItem = {
  encode(message: GoodsExtendItem, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.groupId !== "") {
      writer.uint32(10).string(message.groupId);
    }
    if (message.groupName !== "") {
      writer.uint32(18).string(message.groupName);
    }
    if (message.groupNameCn !== "") {
      writer.uint32(26).string(message.groupNameCn);
    }
    if (message.itemId !== "") {
      writer.uint32(34).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(42).string(message.itemName);
    }
    if (message.itemNameCn !== "") {
      writer.uint32(50).string(message.itemNameCn);
    }
    if (message.itemAlias !== "") {
      writer.uint32(58).string(message.itemAlias);
    }
    if (message.imageUrl !== "") {
      writer.uint32(66).string(message.imageUrl);
    }
    if (message.color !== "") {
      writer.uint32(74).string(message.color);
    }
    if (message.isActive !== false) {
      writer.uint32(80).bool(message.isActive);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsExtendItem {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsExtendItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.groupId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.groupName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.groupNameCn = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.itemName = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.itemNameCn = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.itemAlias = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.color = reader.string();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.isActive = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsExtendItem {
    return {
      groupId: isSet(object.groupId) ? globalThis.String(object.groupId) : "",
      groupName: isSet(object.groupName) ? globalThis.String(object.groupName) : "",
      groupNameCn: isSet(object.groupNameCn) ? globalThis.String(object.groupNameCn) : "",
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName) ? globalThis.String(object.itemName) : "",
      itemNameCn: isSet(object.itemNameCn) ? globalThis.String(object.itemNameCn) : "",
      itemAlias: isSet(object.itemAlias) ? globalThis.String(object.itemAlias) : "",
      imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: GoodsExtendItem): unknown {
    const obj: any = {};
    if (message.groupId !== "") {
      obj.groupId = message.groupId;
    }
    if (message.groupName !== "") {
      obj.groupName = message.groupName;
    }
    if (message.groupNameCn !== "") {
      obj.groupNameCn = message.groupNameCn;
    }
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    if (message.itemNameCn !== "") {
      obj.itemNameCn = message.itemNameCn;
    }
    if (message.itemAlias !== "") {
      obj.itemAlias = message.itemAlias;
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsExtendItem>, I>>(base?: I): GoodsExtendItem {
    return GoodsExtendItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsExtendItem>, I>>(object: I): GoodsExtendItem {
    const message = createBaseGoodsExtendItem();
    message.groupId = object.groupId ?? "";
    message.groupName = object.groupName ?? "";
    message.groupNameCn = object.groupNameCn ?? "";
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    message.itemNameCn = object.itemNameCn ?? "";
    message.itemAlias = object.itemAlias ?? "";
    message.imageUrl = object.imageUrl ?? "";
    message.color = object.color ?? "";
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseSkuItem(): SkuItem {
  return {
    id: "",
    skuNo: "",
    specList: [],
    stepPrices: [],
    skuState: 0,
    skuStateDesc: "",
    sourceSkuId: "",
    amountOnSale: 0,
    imageUrl: "",
    sourceSpecId: "",
  };
}

export const SkuItem = {
  encode(message: SkuItem, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.skuNo !== "") {
      writer.uint32(18).string(message.skuNo);
    }
    for (const v of message.specList) {
      GoodsExtendItem.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    for (const v of message.stepPrices) {
      SkuStepPriceAdmin.encode(v!, writer.uint32(34).fork()).ldelim();
    }
    if (message.skuState !== 0) {
      writer.uint32(56).int32(message.skuState);
    }
    if (message.skuStateDesc !== "") {
      writer.uint32(66).string(message.skuStateDesc);
    }
    if (message.sourceSkuId !== "") {
      writer.uint32(74).string(message.sourceSkuId);
    }
    if (message.amountOnSale !== 0) {
      writer.uint32(80).int32(message.amountOnSale);
    }
    if (message.imageUrl !== "") {
      writer.uint32(90).string(message.imageUrl);
    }
    if (message.sourceSpecId !== "") {
      writer.uint32(98).string(message.sourceSpecId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SkuItem {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkuItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.specList.push(GoodsExtendItem.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.stepPrices.push(SkuStepPriceAdmin.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.skuState = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.skuStateDesc = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.sourceSkuId = reader.string();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.amountOnSale = reader.int32();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.sourceSpecId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SkuItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => GoodsExtendItem.fromJSON(e))
        : [],
      stepPrices: globalThis.Array.isArray(object?.stepPrices)
        ? object.stepPrices.map((e: any) => SkuStepPriceAdmin.fromJSON(e))
        : [],
      skuState: isSet(object.skuState) ? goodsOnlineStateFromJSON(object.skuState) : 0,
      skuStateDesc: isSet(object.skuStateDesc) ? globalThis.String(object.skuStateDesc) : "",
      sourceSkuId: isSet(object.sourceSkuId) ? globalThis.String(object.sourceSkuId) : "",
      amountOnSale: isSet(object.amountOnSale) ? globalThis.Number(object.amountOnSale) : 0,
      imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "",
      sourceSpecId: isSet(object.sourceSpecId) ? globalThis.String(object.sourceSpecId) : "",
    };
  },

  toJSON(message: SkuItem): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) => GoodsExtendItem.toJSON(e));
    }
    if (message.stepPrices?.length) {
      obj.stepPrices = message.stepPrices.map((e) => SkuStepPriceAdmin.toJSON(e));
    }
    if (message.skuState !== 0) {
      obj.skuState = goodsOnlineStateToJSON(message.skuState);
    }
    if (message.skuStateDesc !== "") {
      obj.skuStateDesc = message.skuStateDesc;
    }
    if (message.sourceSkuId !== "") {
      obj.sourceSkuId = message.sourceSkuId;
    }
    if (message.amountOnSale !== 0) {
      obj.amountOnSale = Math.round(message.amountOnSale);
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.sourceSpecId !== "") {
      obj.sourceSpecId = message.sourceSpecId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SkuItem>, I>>(base?: I): SkuItem {
    return SkuItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuItem>, I>>(object: I): SkuItem {
    const message = createBaseSkuItem();
    message.id = object.id ?? "";
    message.skuNo = object.skuNo ?? "";
    message.specList = object.specList?.map((e) => GoodsExtendItem.fromPartial(e)) || [];
    message.stepPrices = object.stepPrices?.map((e) => SkuStepPriceAdmin.fromPartial(e)) || [];
    message.skuState = object.skuState ?? 0;
    message.skuStateDesc = object.skuStateDesc ?? "";
    message.sourceSkuId = object.sourceSkuId ?? "";
    message.amountOnSale = object.amountOnSale ?? 0;
    message.imageUrl = object.imageUrl ?? "";
    message.sourceSpecId = object.sourceSpecId ?? "";
    return message;
  },
};

function createBaseGoodsOperationLogQueryParam(): GoodsOperationLogQueryParam {
  return { page: undefined, id: "", querySkuLogFlag: false };
}

export const GoodsOperationLogQueryParam = {
  encode(message: GoodsOperationLogQueryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(10).fork()).ldelim();
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.querySkuLogFlag !== false) {
      writer.uint32(24).bool(message.querySkuLogFlag);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsOperationLogQueryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsOperationLogQueryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.querySkuLogFlag = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsOperationLogQueryParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      querySkuLogFlag: isSet(object.querySkuLogFlag) ? globalThis.Boolean(object.querySkuLogFlag) : false,
    };
  },

  toJSON(message: GoodsOperationLogQueryParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.querySkuLogFlag !== false) {
      obj.querySkuLogFlag = message.querySkuLogFlag;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsOperationLogQueryParam>, I>>(base?: I): GoodsOperationLogQueryParam {
    return GoodsOperationLogQueryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsOperationLogQueryParam>, I>>(object: I): GoodsOperationLogQueryParam {
    const message = createBaseGoodsOperationLogQueryParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.id = object.id ?? "";
    message.querySkuLogFlag = object.querySkuLogFlag ?? false;
    return message;
  },
};

function createBaseGoodsOperationLogResp(): GoodsOperationLogResp {
  return { result: undefined, page: undefined, data: [] };
}

export const GoodsOperationLogResp = {
  encode(message: GoodsOperationLogResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      GoodsOperationLogModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsOperationLogResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsOperationLogResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(GoodsOperationLogModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsOperationLogResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => GoodsOperationLogModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsOperationLogResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => GoodsOperationLogModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsOperationLogResp>, I>>(base?: I): GoodsOperationLogResp {
    return GoodsOperationLogResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsOperationLogResp>, I>>(object: I): GoodsOperationLogResp {
    const message = createBaseGoodsOperationLogResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => GoodsOperationLogModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoodsOperationLogModel(): GoodsOperationLogModel {
  return { id: "", operation: "", content: "", operator: "", operateTime: 0 };
}

export const GoodsOperationLogModel = {
  encode(message: GoodsOperationLogModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.operation !== "") {
      writer.uint32(18).string(message.operation);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    if (message.operator !== "") {
      writer.uint32(34).string(message.operator);
    }
    if (message.operateTime !== 0) {
      writer.uint32(40).int64(message.operateTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsOperationLogModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsOperationLogModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.operation = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.operator = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.operateTime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsOperationLogModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      operation: isSet(object.operation) ? globalThis.String(object.operation) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      operator: isSet(object.operator) ? globalThis.String(object.operator) : "",
      operateTime: isSet(object.operateTime) ? globalThis.Number(object.operateTime) : 0,
    };
  },

  toJSON(message: GoodsOperationLogModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.operation !== "") {
      obj.operation = message.operation;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.operator !== "") {
      obj.operator = message.operator;
    }
    if (message.operateTime !== 0) {
      obj.operateTime = Math.round(message.operateTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsOperationLogModel>, I>>(base?: I): GoodsOperationLogModel {
    return GoodsOperationLogModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsOperationLogModel>, I>>(object: I): GoodsOperationLogModel {
    const message = createBaseGoodsOperationLogModel();
    message.id = object.id ?? "";
    message.operation = object.operation ?? "";
    message.content = object.content ?? "";
    message.operator = object.operator ?? "";
    message.operateTime = object.operateTime ?? 0;
    return message;
  },
};

function createBaseSkuStepPriceAdmin(): SkuStepPriceAdmin {
  return { start: 0, end: 0, price: 0, insideOnePrice: 0, supplierPrice: 0 };
}

export const SkuStepPriceAdmin = {
  encode(message: SkuStepPriceAdmin, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.start !== 0) {
      writer.uint32(8).int32(message.start);
    }
    if (message.end !== 0) {
      writer.uint32(16).int32(message.end);
    }
    if (message.price !== 0) {
      writer.uint32(25).double(message.price);
    }
    if (message.insideOnePrice !== 0) {
      writer.uint32(33).double(message.insideOnePrice);
    }
    if (message.supplierPrice !== 0) {
      writer.uint32(41).double(message.supplierPrice);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SkuStepPriceAdmin {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkuStepPriceAdmin();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.start = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.end = reader.int32();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.price = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.insideOnePrice = reader.double();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }

          message.supplierPrice = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SkuStepPriceAdmin {
    return {
      start: isSet(object.start) ? globalThis.Number(object.start) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      insideOnePrice: isSet(object.insideOnePrice) ? globalThis.Number(object.insideOnePrice) : 0,
      supplierPrice: isSet(object.supplierPrice) ? globalThis.Number(object.supplierPrice) : 0,
    };
  },

  toJSON(message: SkuStepPriceAdmin): unknown {
    const obj: any = {};
    if (message.start !== 0) {
      obj.start = Math.round(message.start);
    }
    if (message.end !== 0) {
      obj.end = Math.round(message.end);
    }
    if (message.price !== 0) {
      obj.price = message.price;
    }
    if (message.insideOnePrice !== 0) {
      obj.insideOnePrice = message.insideOnePrice;
    }
    if (message.supplierPrice !== 0) {
      obj.supplierPrice = message.supplierPrice;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SkuStepPriceAdmin>, I>>(base?: I): SkuStepPriceAdmin {
    return SkuStepPriceAdmin.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuStepPriceAdmin>, I>>(object: I): SkuStepPriceAdmin {
    const message = createBaseSkuStepPriceAdmin();
    message.start = object.start ?? 0;
    message.end = object.end ?? 0;
    message.price = object.price ?? 0;
    message.insideOnePrice = object.insideOnePrice ?? 0;
    message.supplierPrice = object.supplierPrice ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
