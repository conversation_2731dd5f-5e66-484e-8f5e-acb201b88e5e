syntax = "proto3";

package chilat.coupon;

import "chilat/coupon/coupon_common.proto";
import "common.proto";

option java_package = "com.chilat.rpc.coupon.param";

//后台优惠券保存字段
message SaveCouponInfoParam {
    string id = 1;
    string couponName = 2;//优惠券名称
    string couponAlias = 3; //优惠券别名
    string userInstructions = 4; //用户使用说明
    string backendRemark = 5; //后台备注
    CouponTypeStatus couponType = 6; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    CouponWayStatus couponWay = 7; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
    double useConditionsAmount = 8; //使用条件(0不限制，达到多少金额可以使用，每满xxx可用)
    double preferentialAmount = 9; //优惠额度
    double discount = 10; //折扣（券类型为折扣券使用）
    int32 maxNumQuantity = 11; //最大发放数量
    CouponDistributionStatus couponDistributionMethod = 13; //优惠券发放方式: couponDistributionActive、主动领取 couponDistributionAutomatic、自动发放 couponDistributionExchange、优惠码兑换
    CouponStatus couponStatus = 14; //主体卡券的状态: couponDraft 草稿 couponNotActive 失效 couponActive 生效 couponLock 锁定 couponUnLock 解锁 couponExpire 过期 couponCancel 作废
    int64 couponIssueStartDate = 15; //优惠券发放开始日期
    int64 couponIssueEndDate = 16; //优惠券发放结束日期
    string activeId = 17;//活动编码
    //基本规则
    CouponEffectiveTypeStatus couponEffectiveType = 18;//有效期类型 （distributionDateEffectiveDays 发放日期+有效天数  distributionDateEffectiveHours 发放日期+有效小时 fixedTime 固定时间）
    int32 effectiveNum = 19; //有效类型为 distributionDateEffectiveDays 有效天数,有效类型为 distributionDateEffectiveHours 有效小时 ,fixedTime 固定时间
    int64 couponStartExpirationDate = 20;//卡券有效期开始日期(固定时间)
    int64 couponEndExpirationDate = 21;//卡券有效期结束日期(固定时间)
    //使用规则
    repeated CouponInfoUseRuleParam couponInfoUseRuleParamList = 22;
    CouponUseConditionsTypestatus couponUseConditionsType = 23;//使用条件类型

}


message CouponInfoPageQueryParam {
    common.PageParam page = 1;
    CouponInfoQueryParam couponInfoQueryParam = 2;
}

// 优惠券查询参数
message CouponInfoQueryParam {
    string couponName = 1;//优惠券名称
    string couponAlias = 2; //优惠券别名
    string activeId = 3;//活动编码
    CouponStatus couponStatus = 4; //主体卡券的状态: couponDraft 草稿 couponNotActive 失效 couponActive 生效 couponLock 锁定 couponUnLock 解锁 couponExpire 过期 couponCancel 删除
}



//使用规则
message CouponInfoUseRuleParam {
    int32 useRuleCount = 1;
    CouponUseRuleStatus useRuleType = 2;
}

message SendCouponToUserParam {
    string couponId = 10; //优惠券ID
    string username = 20; //用户名
}