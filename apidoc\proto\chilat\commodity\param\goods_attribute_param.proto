syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

// 属性列表参数
message GoodsAttributePageQueryParam {
  common.PageParam page = 1;
  GoodsAttributeQueryParam query = 2;
}

// 属性列表查询参数
message GoodsAttributeQueryParam {
  string id = 1; // 属性ID
  string attributeName = 2; // 属性名称
  string categoryId = 3; //分类ID
}

// 属性保存信息
message GoodsAttributeSaveParam {
  string id = 1; //属性ID
  int32 idx = 2; //顺序
  string attributeName = 3; //属性名称
  GoodsAttributeItemType attributeItemType = 4; // 属性选项类型（10输入框、20多选项、30下拉框）
  repeated string attributeItems = 5; // 属性选项列表
  string remark = 6; //属性备注
  bool enabled = 7; //是否启用
  repeated string categoryIds = 8; //类目id列表
}
