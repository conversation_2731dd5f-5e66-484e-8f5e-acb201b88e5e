syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "mall/pages/model/goods_list_page_model.proto";
import "mall/pages/param/goods_list_page_param.proto";
import "common.proto";

// 商品列表页
service GoodsListPage {
    // 获取页面数据（服务器端渲染）
    rpc getPageData (GoodsListQueryParam) returns (GoodsListPageResp);
    // 搜索商品（浏览器AJAX请求）
    rpc searchGoods (GoodsListQueryParam) returns (GoodsListDataResp);
    // 获取商品
    rpc getGoods (common.StringParam) returns (common.IdResult);
}
