/* eslint-disable */
import * as _m0 from "protobufjs/minimal";

export const protobufPackage = "mall.pages";

/** 分页查询订单列表 */
export interface GetOrderListRequestParam {
  /** 指定订单状态进行过滤,如果没有填则为全部订单 */
  orderStatus: number;
  /** 请求第几页, 不填默认为第一页 */
  pageNum: number;
  /** 每页订单数, 不填默认10个 */
  pageSize: number;
}

/** 取消订单 */
export interface CancelOrderParam {
  /** 订单id */
  orderId: string;
  /** 取消理由id */
  cancelReasonId: number;
  /** 取消理由内容 */
  cancelReason: string;
  /** 取消订单备注 */
  cancelRemark: string;
}

/** 查询订单详情 */
export interface GetOrderDetailParam {
  /** 订单id */
  orderId: string;
}

/** 查询支付方式列表 */
export interface GetPayMethodListParam {
  /** 订单id */
  orderId: string;
}

/** 查询支付金额 */
export interface GetPaymentAmountParam {
  /** 订单id */
  orderId: string;
  /** 支付单id */
  paymentId: string;
}

export interface SubmitPaymentParam {
  orderId: string;
  paymentId: string;
  amount: number;
  payMethod: string;
}

/** 查询支付结果 */
export interface QueryPaymentStatusParam {
  /** 订单id */
  orderId: string;
  /** 支付单id */
  paymentId: string;
}

/** 确认收获 */
export interface ConfirmReceiptParam {
  /** 订单id */
  orderId: string;
}

function createBaseGetOrderListRequestParam(): GetOrderListRequestParam {
  return { orderStatus: 0, pageNum: 0, pageSize: 0 };
}

export const GetOrderListRequestParam = {
  encode(message: GetOrderListRequestParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderStatus !== 0) {
      writer.uint32(80).int32(message.orderStatus);
    }
    if (message.pageNum !== 0) {
      writer.uint32(160).int32(message.pageNum);
    }
    if (message.pageSize !== 0) {
      writer.uint32(240).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListRequestParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListRequestParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.orderStatus = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.pageNum = reader.int32();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListRequestParam {
    return {
      orderStatus: isSet(object.orderStatus) ? globalThis.Number(object.orderStatus) : 0,
      pageNum: isSet(object.pageNum) ? globalThis.Number(object.pageNum) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetOrderListRequestParam): unknown {
    const obj: any = {};
    if (message.orderStatus !== 0) {
      obj.orderStatus = Math.round(message.orderStatus);
    }
    if (message.pageNum !== 0) {
      obj.pageNum = Math.round(message.pageNum);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListRequestParam>, I>>(base?: I): GetOrderListRequestParam {
    return GetOrderListRequestParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListRequestParam>, I>>(object: I): GetOrderListRequestParam {
    const message = createBaseGetOrderListRequestParam();
    message.orderStatus = object.orderStatus ?? 0;
    message.pageNum = object.pageNum ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseCancelOrderParam(): CancelOrderParam {
  return { orderId: "", cancelReasonId: 0, cancelReason: "", cancelRemark: "" };
}

export const CancelOrderParam = {
  encode(message: CancelOrderParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.cancelReasonId !== 0) {
      writer.uint32(160).int32(message.cancelReasonId);
    }
    if (message.cancelReason !== "") {
      writer.uint32(242).string(message.cancelReason);
    }
    if (message.cancelRemark !== "") {
      writer.uint32(322).string(message.cancelRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CancelOrderParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCancelOrderParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.cancelReasonId = reader.int32();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.cancelReason = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.cancelRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CancelOrderParam {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      cancelReasonId: isSet(object.cancelReasonId) ? globalThis.Number(object.cancelReasonId) : 0,
      cancelReason: isSet(object.cancelReason) ? globalThis.String(object.cancelReason) : "",
      cancelRemark: isSet(object.cancelRemark) ? globalThis.String(object.cancelRemark) : "",
    };
  },

  toJSON(message: CancelOrderParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.cancelReasonId !== 0) {
      obj.cancelReasonId = Math.round(message.cancelReasonId);
    }
    if (message.cancelReason !== "") {
      obj.cancelReason = message.cancelReason;
    }
    if (message.cancelRemark !== "") {
      obj.cancelRemark = message.cancelRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CancelOrderParam>, I>>(base?: I): CancelOrderParam {
    return CancelOrderParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelOrderParam>, I>>(object: I): CancelOrderParam {
    const message = createBaseCancelOrderParam();
    message.orderId = object.orderId ?? "";
    message.cancelReasonId = object.cancelReasonId ?? 0;
    message.cancelReason = object.cancelReason ?? "";
    message.cancelRemark = object.cancelRemark ?? "";
    return message;
  },
};

function createBaseGetOrderDetailParam(): GetOrderDetailParam {
  return { orderId: "" };
}

export const GetOrderDetailParam = {
  encode(message: GetOrderDetailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailParam {
    return { orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "" };
  },

  toJSON(message: GetOrderDetailParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailParam>, I>>(base?: I): GetOrderDetailParam {
    return GetOrderDetailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailParam>, I>>(object: I): GetOrderDetailParam {
    const message = createBaseGetOrderDetailParam();
    message.orderId = object.orderId ?? "";
    return message;
  },
};

function createBaseGetPayMethodListParam(): GetPayMethodListParam {
  return { orderId: "" };
}

export const GetPayMethodListParam = {
  encode(message: GetPayMethodListParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetPayMethodListParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPayMethodListParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPayMethodListParam {
    return { orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "" };
  },

  toJSON(message: GetPayMethodListParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPayMethodListParam>, I>>(base?: I): GetPayMethodListParam {
    return GetPayMethodListParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPayMethodListParam>, I>>(object: I): GetPayMethodListParam {
    const message = createBaseGetPayMethodListParam();
    message.orderId = object.orderId ?? "";
    return message;
  },
};

function createBaseGetPaymentAmountParam(): GetPaymentAmountParam {
  return { orderId: "", paymentId: "" };
}

export const GetPaymentAmountParam = {
  encode(message: GetPaymentAmountParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetPaymentAmountParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPaymentAmountParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPaymentAmountParam {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
    };
  },

  toJSON(message: GetPaymentAmountParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPaymentAmountParam>, I>>(base?: I): GetPaymentAmountParam {
    return GetPaymentAmountParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPaymentAmountParam>, I>>(object: I): GetPaymentAmountParam {
    const message = createBaseGetPaymentAmountParam();
    message.orderId = object.orderId ?? "";
    message.paymentId = object.paymentId ?? "";
    return message;
  },
};

function createBaseSubmitPaymentParam(): SubmitPaymentParam {
  return { orderId: "", paymentId: "", amount: 0, payMethod: "" };
}

export const SubmitPaymentParam = {
  encode(message: SubmitPaymentParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    if (message.amount !== 0) {
      writer.uint32(241).double(message.amount);
    }
    if (message.payMethod !== "") {
      writer.uint32(322).string(message.payMethod);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SubmitPaymentParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitPaymentParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.payMethod = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitPaymentParam {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      payMethod: isSet(object.payMethod) ? globalThis.String(object.payMethod) : "",
    };
  },

  toJSON(message: SubmitPaymentParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.payMethod !== "") {
      obj.payMethod = message.payMethod;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitPaymentParam>, I>>(base?: I): SubmitPaymentParam {
    return SubmitPaymentParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitPaymentParam>, I>>(object: I): SubmitPaymentParam {
    const message = createBaseSubmitPaymentParam();
    message.orderId = object.orderId ?? "";
    message.paymentId = object.paymentId ?? "";
    message.amount = object.amount ?? 0;
    message.payMethod = object.payMethod ?? "";
    return message;
  },
};

function createBaseQueryPaymentStatusParam(): QueryPaymentStatusParam {
  return { orderId: "", paymentId: "" };
}

export const QueryPaymentStatusParam = {
  encode(message: QueryPaymentStatusParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPaymentStatusParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPaymentStatusParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPaymentStatusParam {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
    };
  },

  toJSON(message: QueryPaymentStatusParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPaymentStatusParam>, I>>(base?: I): QueryPaymentStatusParam {
    return QueryPaymentStatusParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPaymentStatusParam>, I>>(object: I): QueryPaymentStatusParam {
    const message = createBaseQueryPaymentStatusParam();
    message.orderId = object.orderId ?? "";
    message.paymentId = object.paymentId ?? "";
    return message;
  },
};

function createBaseConfirmReceiptParam(): ConfirmReceiptParam {
  return { orderId: "" };
}

export const ConfirmReceiptParam = {
  encode(message: ConfirmReceiptParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfirmReceiptParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfirmReceiptParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfirmReceiptParam {
    return { orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "" };
  },

  toJSON(message: ConfirmReceiptParam): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfirmReceiptParam>, I>>(base?: I): ConfirmReceiptParam {
    return ConfirmReceiptParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfirmReceiptParam>, I>>(object: I): ConfirmReceiptParam {
    const message = createBaseConfirmReceiptParam();
    message.orderId = object.orderId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
