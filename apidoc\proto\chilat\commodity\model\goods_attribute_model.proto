syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsAttributePageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsAttributeModel data = 3;
}

message GoodsAttributeDetailResp {
    common.Result result = 1;
    GoodsAttributeModel data = 2;
}

// 商品规格信息
message GoodsAttributeModel {
    string id = 1; //属性ID
    int32 idx = 2; //顺序
    string attributeName = 3; //属性名称
    GoodsAttributeItemType attributeItemType = 4; // 属性选项类型（10输入框、20多选项、30下拉框）
    string attributeItemTypeDesc = 5; // 属性选项类型描述
    repeated string attributeItems = 6; // 属性选项列表
    string remark = 7; //属性备注
    bool enabled = 8; //是否启用
    repeated string categoryIds = 9; //类目id列表
    repeated string categoryPaths = 10; //多选类目树列表
    int32 relatedGoodsCount = 11; //关联的商品数量（详情接口返回）
}
