<template>
  <div class="article-wrapper">
    <div>
      <div class="w-full flex justify-center items-center">
        <a class="mx-auto" href="/h5" data-spm-box="navigation-logo-icon">
          <n-image
            lazy
            preview-disabled
            object-fit="fill"
            :src="pageTheme.logo"
            class="w-[2.76rem] pt-[0.16rem] pb-[0.24rem]"
          />
        </a>
      </div>
      <div
        class="px-[0.24rem] py-[0.3rem] text-center relative border-t-1 border-b-1 border-[#E6E6E6]"
        @click="onBackClick"
      >
        <img loading="lazy"
          alt="back"
          src="@/assets/icons/arrowLeft.svg"
          class="w-[0.24rem] absolute"
        />
        <div class="text-[0.36rem] leading-[0.36rem] font-medium">
          {{ pageData.columnTitle }}
        </div>
      </div>
    </div>
    <div class="px-[0.32rem] pt-[0.44rem] pb-[1rem] min-h-[60vh]">
      <div class="text-[0.4rem] leading-[0.5rem] font-medium mb-[0.32rem]">
        {{ pageData.articleDetail?.title }}
      </div>

      <div
        data-spm-box="article-inner-link"
        v-if="pageData.articleDetail?.content"
        v-html="pageData.articleDetail?.content"
        class="whitespace-pre-wrap text-[0.32rem] leading-[0.5rem] text-[#4D4D4D]"
      ></div>
    </div>

    <mobile-page-footer></mobile-page-footer>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";

const pageTheme = computed(() => useConfigStore().getPageTheme);
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const pageData = reactive<any>({
  articleDetail: "",
  columnTitle: route.query.columnTitle || "",
});

await onGetArticle();
async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.query.code,
  });
  if (res?.result?.code === 200) {
    pageData.articleDetail = res?.data;
  }
}

/** 返回上一页 */
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
.article-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
</style>
