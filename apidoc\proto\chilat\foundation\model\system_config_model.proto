syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";
import "common.proto";

message SystemConfigResp {
  common.Result result = 1;
  SystemConfigModel data = 2;
}

message GoodsTagListResp {
  common.Result result = 1;
  repeated common.IdNameModel data = 2;
}

message SystemConfigModel {
  double fixedFactor = 1; //固定系数
}

message SystemConfigLogListResp {
  common.Result result = 1;
  repeated SystemConfigLogModel data = 2;
}

message SystemConfigLogModel {
  string id = 1;
  string coperator = 2; //创建人
  int64 cdate = 3; //创建时间
  string remark = 4; //操作内容
}