<template>
  <div class="mobile-container">
    <div class="w-full relative">
      <div class="w-full pt-[1.98rem]">
        <img loading="lazy"
          alt="chilat"
          class="w-[2.6rem] absolute left-[0.2rem] top-[0.76rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/18/ef9d46aa-1b6c-45c9-841c-e41089c3ee6d.png"
        />
        <div
          class="text-[0.42rem] leading-[0.52rem] font-semibold text-center text-[#fff] relative z-10 px-[1rem]"
        >
          {{ authStore.i18n("cm_nota.oneOnOneService") }}
        </div>
        <img loading="lazy"
          alt="chilat"
          class="w-[5.2rem] absolute right-0 top-[0.8rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/15/8edbcaa4-7b80-434a-9fdd-384940a4a00e.png"
        />
      </div>
      <div
        class="mt-[0.48rem] mx-[0.16rem] relative z-10 pt-[1.4rem] bg-white min-h-[9.52rem] rounded-[0.08rem] flex flex-col items-center"
      >
        <img loading="lazy"
          alt="paySuccess"
          src="@/assets/icons/order/paySuccess.svg"
          class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
        />
        <span
          class="font-semibold text-[0.48rem] leading-[0.48rem] text-[#000] mb-[0.24rem]"
          >{{ authStore.i18n("cm_nota.submitSuccess") }}
        </span>
        <div
          class="w-full text-[#939393] text-[0.28rem] leading-[0.36rem] text-center"
        >
          <div
            class="px-[0.8rem]"
            v-if="pageData.serviceType === 'POTENTIAL_SERVICE_TYPE_VISIT_CHINA'"
          >
            {{ authStore.i18n("cm_nota.hello") }} {{ pageData.contactName }},
            {{ authStore.i18n("cm_nota.visitChinaTip1") }}
            ({{ pageData.whatsapp }})
            {{ authStore.i18n("cm_nota.visitChinaTip2") }}
          </div>
          <div
            class="px-[0.8rem]"
            v-if="
              pageData.serviceType ===
              'POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT'
            "
          >
            {{ authStore.i18n("cm_nota.hello") }} {{ pageData.contactName }},
            {{ authStore.i18n("cm_nota.customizeProductTip1") }}
            ({{ pageData.whatsapp }})
            {{ authStore.i18n("cm_nota.customizeProductTip2") }}
          </div>
          <div
            class="px-[0.4rem]"
            v-if="pageData.serviceType === 'POTENTIAL_SERVICE_TYPE_ONLINE_BUY'"
          >
            <span>
              {{ pageData.contactName }}
              {{ authStore.i18n("cm_nota.hello") }}
              ({{ pageData.whatsapp }}),
              <span
                v-html="authStore.i18n('cm_nota.onlineSelectionHtml')"
              ></span>
            </span>
            <div
              class="w-full border-t-1 border-[#BBB] mt-[0.52rem] py-[0.56rem]"
            >
              <div v-if="pageData.timeLeft">
                <span
                  class="text-[0.44rem] mr-[0.12rem] text-[#e50113] w-[0.28rem] inline-block"
                  >{{ pageData.timeLeft }}</span
                >
                <span class="text-[0.32rem] text-[#333]">{{
                  authStore.i18n("cm_goods.timeLeftTip")
                }}</span>
              </div>
              <a
                href="/h5"
                ref="homeButtonRef"
                data-spm-box="potential_user_note_success"
              >
                <n-button
                  color="#E50113"
                  class="w-full py-[0.4rem] rounded-[4rem] text-[0.32rem] leading-[0.32rem] font-semibold mt-[0.2rem]"
                >
                  {{ authStore.i18n("cm_nota.goShopping") }}
                </n-button>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const route = useRoute();
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const homeButtonRef = ref(<any>{});
const pageData = reactive<any>({
  serviceType: route?.query?.serviceType || "",
  contactName: route?.query?.contactName || "",
  whatsapp: route?.query?.whatsapp || "",
  timeLeft: 0,
});

onMounted(() => {
  if (pageData.serviceType === "POTENTIAL_SERVICE_TYPE_ONLINE_BUY") {
    pageData.timeLeft = 3;
    const countdown = setInterval(() => {
      if (pageData.timeLeft === 1) {
        homeButtonRef.value?.click();
        clearInterval(countdown);
      } else {
        pageData.timeLeft--;
      }
    }, 1000);
  }
});
</script>
<style scoped lang="scss">
.mobile-container {
  height: auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #db1121 0%, #f87e7e 100%), #f2f2f2;
  background-size: 100% 4.52rem, 100% 100%;
  background-repeat: no-repeat;
  color: #333;
}
</style>
