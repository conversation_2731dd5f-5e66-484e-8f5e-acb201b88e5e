module.exports = {
  apps: [
    {
      name: "chilat2-mall-ssr",
      script: "./.output/server/index.mjs",
      watch: true,
      ignore_watch: ["node_modules", "logs"],
      exec_mode: "cluster",
      instances: 10,
      cron_restart: "0 2 * * *",
      max_memory_restart: "1G",
      error_file: "./logs/mall-ssr-err.log",
      out_file: "./logs/mall-ssr-out.log",
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      min_uptime: "60s",
      max_restarts: 10,
      autorestart: true,
      restart_delay: 60,
      port: "3338",
    },
  ],
};
