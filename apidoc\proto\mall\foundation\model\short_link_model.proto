syntax = "proto3";
package mall.foundation;

option java_package = "com.chilat.rpc.foundation.model";
import "chilat/support/model/article_category_model.proto";
import "common.proto";


//获取短链接请求返回
message GetShortLinkResp {
  common.Result result = 1;
  GetShortLinkModel data = 2;
}

//获取短链接请求结果
message GetShortLinkModel {
  string shortUrl = 10; //转换短链接后的URL
  string linkCode = 20; //短链代码（可根据短链代码，获取原链接）
}

//获取完整链接请求返回
message GetFullLinkResp {
  common.Result result = 1;
  GetFullLinkModel data = 2;
}

//获取短链接请求结果
message GetFullLinkModel {
  string fullUrl = 10; //转换完整URL（源链接）
  string linkCode = 20; //短链代码（可根据短链代码，获取源链接）
}

