syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/param/file_manager_param.proto";
import "chilat/basis/model/file_manager_model.proto";
import "common.proto";

// 文件上传
service FileManager {
  // 上传流文件
  rpc uploadStream (UploadStreamParam) returns (UploadFileResp) {
    option (common.webapi).upload = true;
  };
  // 根据地址上传文件
  rpc uploadFileByUrl (UploadFileByUrlParam) returns (UploadFileByUrlResp);
}