syntax = "proto3";

package chilat.basis;

import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";


option java_package = "com.chilat.rpc.basis.param.coupon";

//可使用优惠券参数
message CouponUseParam {
    string orderNo = 1;//订单号
    repeated string Ids = 3;//优惠券主键Id
    string userId = 4;//用户id
    coupon.CouponTypeStatus couponType = 5; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    coupon.TicketStatus ticketStatus = 6;//优惠券类型
}



