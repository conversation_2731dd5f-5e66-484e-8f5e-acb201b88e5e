syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/param/milvus_param.proto";
import "chilat/basis/model/milvus_model.proto";
service MilvusRpc {

  rpc milvusCreateCollection(MilvusCreateCollectionParam) returns (common.ApiResult);

  rpc milvusCreateGoodsCollection(MilvusCreateCollectionParam) returns (common.ApiResult);

  rpc milvusDropCollection(MilvusDropCollectionParam) returns (common.ApiResult);

  rpc milvusShowCollections(MilvusShowCollectionsParam) returns (MilvusShowCollectionsResult);

  rpc milvusGetAllIds(MilvusGetAllParam) returns (MilvusGetAllIdsResult);

  rpc milvusDeleteByIds(MilvusDeleteByIdsParam) returns (common.ApiResult);

  rpc milvusGetByIds(MilvusGetByIdsParam) returns (MilvusGetResult);
}
