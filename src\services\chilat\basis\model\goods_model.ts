/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";
import {
  CurrencyType,
  currencyTypeFromJSON,
  currencyTypeToJSON,
  SkuStepPrice,
  SkuStepRange,
} from "../../../common/business";
import { GoodsAttrModel } from "../../commodity/commodity_common";

export const protobufPackage = "chilat.basis";

export interface MidGoodsInfoResp {
  result: Result | undefined;
  data: MidGoodsInfoModel | undefined;
}

export interface MidGoodsInfoModel {
  /** 商品ID */
  id: string;
  /** 商品编号-主数据 */
  goodsNo: string;
  /** 品牌名称 */
  brandName: string;
  /** 商品分类路径 */
  categoryPath: MidCategoryPathItemModel[];
  /** 商品名称-主数据 */
  goodsName: string;
  /** 商品标题-主数据 */
  goodsTitle: string;
  /** 是否已上架（商品预览功能可包含未上架商品） */
  isOnline: boolean;
  /** 价格对应的货币 */
  currency: CurrencyType;
  /** 商品计价单位名称 */
  goodsPriceUnitName: string;
  /** 包装内含货品的数量 */
  packageInsideCount: number;
  /** 包装内含货品的数量单位 */
  packageInsideUnit: string;
  /** 最小购买数量（起订量） */
  minBuyQuantity: number;
  /** 最小加购数量（一次加购数量） */
  minIncreaseQuantity: number;
  /** 视频封面图片（若非空，则在商详页优先显示视频） */
  coverImage: string;
  /** 视频 */
  videoUrl: string;
  /** 商品图片组 */
  goodsImageList: string[];
  /** 商品长（单位：cm） */
  goodsLength: number;
  /** 商品宽（单位：cm） */
  goodsWidth: number;
  /** 商品高（单位：cm） */
  goodsHeight: number;
  /** 商品重量 */
  goodsWeight: number;
  /** 运费提示 */
  freightTips: string;
  /** 商家条形码 */
  goodsBarcode: string;
  /** PC商品描述 */
  goodsPcDesc: string;
  /** H5商品描述 */
  goodsH5Desc: string;
  /** SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同） */
  skuStepRanges: SkuStepRange[];
  /** 属性参数（属性名称，属性值） */
  attrList: GoodsAttrModel[];
  /** 商品规格列表（仅包含有对应SKU的规格） */
  specList: MidSkuSpecModel[];
  /** 商品SKU列表 */
  skuList: MidSkuInfoModel[];
  /** 是否校验 */
  isAudit: boolean;
  /** 是否侵权 */
  isIllegal: boolean;
}

/** 商品SKU规格 */
export interface MidSkuSpecModel {
  /** 规格ID（必填） */
  id: string;
  /** 规格名称（必填） */
  name: string;
  /** 规格明细（必填） */
  items: MidSkuSpecItemModel[];
}

export interface MidSkuSpecItemModel {
  /** 规格明细ID（必填） */
  itemId: string;
  /** 规格明细名称（如颜色规格中的“红色”，必填） */
  itemName: string;
  /** 图片链接（例：SKU图片） */
  imageUrl: string;
  /** 颜色值（格式：# + 6位16进制数，例：#CCCCCC） */
  color: string;
}

/** 商品SKU信息 */
export interface MidSkuInfoModel {
  /** SKU ID（必填） */
  id: string;
  /** SKU商品料号（必填） */
  skuNo: string;
  /** 销售价 */
  salePrice: number;
  /** SKU规格ID组：规格ID，规格明细ID */
  specItems: MidSkuSpecIdPairModel[];
  /** sku阶梯价格（件数从少到多，价格从高到低） */
  stepPrices: SkuStepPrice[];
  /** int32 stockQty = 70; //库存数量（必填） */
  stockInfo: MidSkuStockModel | undefined;
}

/** 商品库存信息 */
export interface MidSkuStockModel {
  /** 库存ID */
  id: string;
  /** 销售数量（含未付订单，不含取消单） */
  salesQty: number;
  /** 实有库存数（可用库存数） */
  realQty: number;
  /** 冻结库存数 */
  freezeQty: number;
  /** 可用库存数 */
  availableQty: number;
}

/** 商品SKU中的规格ID与规格明细ID组合（通过检索SKU与反查有SKU规格） */
export interface MidSkuSpecIdPairModel {
  /** 规格ID */
  specId: string;
  /** 规格名称 */
  specName: string;
  /** 规格明细ID（当前商品内唯一） */
  itemId: string;
  /** 规格明细名称 */
  itemName: string;
}

export interface MidCategoryPathItemModel {
  /** 商品分类ID */
  id: string;
  /** 商品分类名称 */
  name: string;
}

/** ----------------------      商品分类数据     ---------------------- */
export interface MidCategoryTreeResp {
  result: Result | undefined;
  data: MidCategoryModel | undefined;
}

export interface MidCategoryModel {
  /** 商品分类ID */
  id: string;
  /** 商品分类名称 */
  name: string;
  /** 商品分类logo */
  cateLogo: string;
  /** 商品分类icon */
  cateIcon: string;
  /** 下级商品分类 */
  children: MidCategoryModel[];
}

export interface MidGoodsPageResp {
  result: Result | undefined;
  page: Page | undefined;
  data: MidGoodsModel[];
}

export interface MidGoodsModel {
  /** id */
  id: string;
  /** 商品主图 */
  goodsImageShow: string;
  /** 商品名称 */
  goodsName: string;
  /** 商品编码 */
  goodsNo: string;
  /** 商品最低价格 */
  minPrice: number;
  /** 商品最低原始价格 */
  minOldPrice: number;
  /** 品牌id */
  brandId: string;
  /** 品牌名称 */
  brandName: string;
  /** 分类id */
  categoryId: string;
  /** 分类名称 */
  categoryName: string;
  /** 二级类目id */
  categoryId2th: string;
  /** 二级类目名称 */
  categoryName2th: string;
  /** 一级类目id */
  categoryId1th: string;
  /** 一级类目名称 */
  categoryName1th: string;
  /** 商品图片组 */
  goodsImage: string;
  /** 商品重量 */
  goodsWeight: number;
  /** 起订量（最小购买数量） */
  minBuyQuantity: number;
  /** 库存数量 */
  stockQuantity: number;
  goodsTitle: string;
  goodsPriceUnitId: string;
  goodsPriceUnitName: string;
  taxRate: number;
  /** SKU信息 */
  skuList: MidSkuModel[];
  /** 最低销售价 */
  minSalesPrice: number;
  /** 最低原始销售价 */
  minOldSalesPrice: number;
  /** 默认Sku */
  defaultSku: MidSkuModel | undefined;
  /** 商品销量 */
  sales: number;
}

export interface MidSkuModel {
  /** SKU ID */
  skuId: string;
  /** SKU号 */
  skuNo: string;
  /** sku名称 */
  skuName: string;
  /** 最优价格 阶梯价格/最小包裹数量 */
  minPrice: number;
  /** 最小销售包装的内含数量 */
  packageCount: number;
  /** 最小销售包装单位 */
  packageUnit: string;
  /** 交期 */
  deliveryDate: string;
  /** 商品图片 */
  image: string;
  /** 商品会员价格 */
  priceObj: MidSkuPriceModel | undefined;
  /** 规格 */
  specList: MidSkuSpecIdPairModel[];
  /** 库存数量 */
  skuStockQuantity: number;
  /** sku阶梯价格（件数从少到多，价格从高到低） */
  goodsPrice: SkuStepPrice[];
}

export interface MidSkuPriceModel {
  /** 商品售价 */
  salePrice: number;
  /** 商品原价 */
  oldPrice: number;
  /** 是否显示原价1：显示，0：不显示 */
  oldPriceShow: number;
  /** 商品成本价 */
  costPrice: number;
  /** 商品阶梯价 */
  stepPrice: string;
  /** 平台与供应商结算价格 */
  memPriceShow: number;
  /** 平台-供应商结算单价 */
  supplierSettlePrice: number;
}

/** 按商品汇总库存返回结果 */
export interface MidGoodsStockSummaryResp {
  result: Result | undefined;
  /** KEY 为 Goods ID */
  data: { [key: string]: MidStockSummaryModel };
}

export interface MidGoodsStockSummaryResp_DataEntry {
  key: string;
  value: MidStockSummaryModel | undefined;
}

/** 按商品汇总库存信息 */
export interface MidStockSummaryModel {
  /** 销量（不含取消单） */
  salesQty: number;
  /** 实有库存数 */
  realStockQty: number;
  /** 可用库存数 */
  availableStockQty: number;
}

export interface MidSearchResultResp {
  result: Result | undefined;
  data: MidSearchResultModel | undefined;
}

export interface MidSearchResultModel {
  /** 商品列表的分页信息 */
  page: Page | undefined;
  /** 商品列表 */
  goodsList: MidSearchGoodsModel[];
  /** 商城前台商品分类过滤选项 */
  mallCategoryFilters: MidSearchCategoryFilterModel[];
  /** 搜索关键字的分词价格（用空格分隔） */
  tokenizeWords: string;
}

export interface MidSearchGoodsModel {
  /** 商品ID */
  goodsId: string;
  /** 商品编码 */
  goodsNo: string;
  /** 商品名称 */
  goodsName: string;
  /** 商品标题 */
  goodsTitle: string;
  /** 价格单位 */
  goodsPriceUnitName: string;
  /** 价格对应的货币 */
  currency: CurrencyType;
  /** 最低价 */
  minPrice: number;
  /** 最高价（若最低价与最高价相同，则在列表显示一个价格） */
  maxPrice: number;
  /** 最小购买数量（起订量） */
  minBuyQuantity: number;
  /** 最小加购数量（一次加购数量） */
  minIncreaseQuantity: number;
  /** 商品主图 */
  mainImageUrl: string;
  /** admin后台商品分类名称 */
  backendCategoryName: string;
  /** 商品权重 */
  goodsRank: number;
  /** 搜索相关度得分 */
  hitScore: number;
}

/** 商品列表中商品分类过滤选项 */
export interface MidSearchCategoryFilterModel {
  /** 商城前台商品分类ID */
  id: string;
  /** 商城前台商品分类名称 */
  name: string;
  /** 下级分类过滤项（null表示不存在） */
  children: MidSearchCategoryFilterModel[];
}

/** 商城前台商品分类树 */
export interface MallCategoryTreeModel {
  /** 商品分类ID */
  id: string;
  /** 商品分类名称 */
  cateName: string;
  /** 商品分类logo */
  cateLogo: string;
  /** string cateIcon = 4; // 商品分类icon */
  goodsCount: number;
  /** 下级商品分类 */
  children: MallCategoryTreeModel[];
}

/** 商城前台商品分类路径明细 */
export interface MallCategoryPathItemModel {
  /** 商品分类ID */
  id: string;
  /** 商品分类名称 */
  name: string;
}

/** ES商品索引重建 */
export interface RecreateESIndexResp {
  result: Result | undefined;
  data: RecreateESIndexModel | undefined;
}

export interface RecreateESIndexModel {
  updateCount: number;
  remark: string;
}

/** 更新全部商品的ES索引 */
export interface UpdateAllGoodsESIndexResp {
  result: Result | undefined;
  data: UpdateAllGoodsESIndexModel | undefined;
}

export interface UpdateAllGoodsESIndexModel {
  updateCount: number;
  remark: string;
}

export interface HomePageGoodsResp {
  result: Result | undefined;
  /** 首页商品 */
  data: HomePageGoodsModel[];
}

export interface HomePageGoodsModel {
  /** 类目ID */
  categoryId: string;
  /** 类目名称 */
  categoryName: string;
  /** 商品 */
  goodsList: MidSearchGoodsModel[];
}

export interface MidRecommendGoodsResp {
  result: Result | undefined;
  /** 商品 */
  data: MidSearchGoodsModel[];
}

function createBaseMidGoodsInfoResp(): MidGoodsInfoResp {
  return { result: undefined, data: undefined };
}

export const MidGoodsInfoResp = {
  encode(
    message: MidGoodsInfoResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MidGoodsInfoModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsInfoResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsInfoResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MidGoodsInfoModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsInfoResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data)
        ? MidGoodsInfoModel.fromJSON(object.data)
        : undefined,
    };
  },

  toJSON(message: MidGoodsInfoResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MidGoodsInfoModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsInfoResp>, I>>(
    base?: I
  ): MidGoodsInfoResp {
    return MidGoodsInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsInfoResp>, I>>(
    object: I
  ): MidGoodsInfoResp {
    const message = createBaseMidGoodsInfoResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? MidGoodsInfoModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseMidGoodsInfoModel(): MidGoodsInfoModel {
  return {
    id: "",
    goodsNo: "",
    brandName: "",
    categoryPath: [],
    goodsName: "",
    goodsTitle: "",
    isOnline: false,
    currency: 0,
    goodsPriceUnitName: "",
    packageInsideCount: 0,
    packageInsideUnit: "",
    minBuyQuantity: 0,
    minIncreaseQuantity: 0,
    coverImage: "",
    videoUrl: "",
    goodsImageList: [],
    goodsLength: 0,
    goodsWidth: 0,
    goodsHeight: 0,
    goodsWeight: 0,
    freightTips: "",
    goodsBarcode: "",
    goodsPcDesc: "",
    goodsH5Desc: "",
    skuStepRanges: [],
    attrList: [],
    specList: [],
    skuList: [],
    isAudit: false,
    isIllegal: false,
  };
}

export const MidGoodsInfoModel = {
  encode(
    message: MidGoodsInfoModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.goodsNo !== "") {
      writer.uint32(162).string(message.goodsNo);
    }
    if (message.brandName !== "") {
      writer.uint32(242).string(message.brandName);
    }
    for (const v of message.categoryPath) {
      MidCategoryPathItemModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.goodsName !== "") {
      writer.uint32(402).string(message.goodsName);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(482).string(message.goodsTitle);
    }
    if (message.isOnline !== false) {
      writer.uint32(560).bool(message.isOnline);
    }
    if (message.currency !== 0) {
      writer.uint32(600).int32(message.currency);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(642).string(message.goodsPriceUnitName);
    }
    if (message.packageInsideCount !== 0) {
      writer.uint32(720).int32(message.packageInsideCount);
    }
    if (message.packageInsideUnit !== "") {
      writer.uint32(802).string(message.packageInsideUnit);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(840).int32(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      writer.uint32(848).int32(message.minIncreaseQuantity);
    }
    if (message.coverImage !== "") {
      writer.uint32(882).string(message.coverImage);
    }
    if (message.videoUrl !== "") {
      writer.uint32(962).string(message.videoUrl);
    }
    for (const v of message.goodsImageList) {
      writer.uint32(1042).string(v!);
    }
    if (message.goodsLength !== 0) {
      writer.uint32(1121).double(message.goodsLength);
    }
    if (message.goodsWidth !== 0) {
      writer.uint32(1201).double(message.goodsWidth);
    }
    if (message.goodsHeight !== 0) {
      writer.uint32(1281).double(message.goodsHeight);
    }
    if (message.goodsWeight !== 0) {
      writer.uint32(1361).double(message.goodsWeight);
    }
    if (message.freightTips !== "") {
      writer.uint32(1442).string(message.freightTips);
    }
    if (message.goodsBarcode !== "") {
      writer.uint32(1522).string(message.goodsBarcode);
    }
    if (message.goodsPcDesc !== "") {
      writer.uint32(1602).string(message.goodsPcDesc);
    }
    if (message.goodsH5Desc !== "") {
      writer.uint32(1682).string(message.goodsH5Desc);
    }
    for (const v of message.skuStepRanges) {
      SkuStepRange.encode(v!, writer.uint32(1842).fork()).ldelim();
    }
    for (const v of message.attrList) {
      GoodsAttrModel.encode(v!, writer.uint32(1922).fork()).ldelim();
    }
    for (const v of message.specList) {
      MidSkuSpecModel.encode(v!, writer.uint32(2002).fork()).ldelim();
    }
    for (const v of message.skuList) {
      MidSkuInfoModel.encode(v!, writer.uint32(2082).fork()).ldelim();
    }
    if (message.isAudit !== false) {
      writer.uint32(2160).bool(message.isAudit);
    }
    if (message.isIllegal !== false) {
      writer.uint32(2240).bool(message.isIllegal);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsInfoModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.brandName = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.categoryPath.push(
            MidCategoryPathItemModel.decode(reader, reader.uint32())
          );
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.isOnline = reader.bool();
          continue;
        case 75:
          if (tag !== 600) {
            break;
          }

          message.currency = reader.int32() as any;
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 90:
          if (tag !== 720) {
            break;
          }

          message.packageInsideCount = reader.int32();
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.packageInsideUnit = reader.string();
          continue;
        case 105:
          if (tag !== 840) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 106:
          if (tag !== 848) {
            break;
          }

          message.minIncreaseQuantity = reader.int32();
          continue;
        case 110:
          if (tag !== 882) {
            break;
          }

          message.coverImage = reader.string();
          continue;
        case 120:
          if (tag !== 962) {
            break;
          }

          message.videoUrl = reader.string();
          continue;
        case 130:
          if (tag !== 1042) {
            break;
          }

          message.goodsImageList.push(reader.string());
          continue;
        case 140:
          if (tag !== 1121) {
            break;
          }

          message.goodsLength = reader.double();
          continue;
        case 150:
          if (tag !== 1201) {
            break;
          }

          message.goodsWidth = reader.double();
          continue;
        case 160:
          if (tag !== 1281) {
            break;
          }

          message.goodsHeight = reader.double();
          continue;
        case 170:
          if (tag !== 1361) {
            break;
          }

          message.goodsWeight = reader.double();
          continue;
        case 180:
          if (tag !== 1442) {
            break;
          }

          message.freightTips = reader.string();
          continue;
        case 190:
          if (tag !== 1522) {
            break;
          }

          message.goodsBarcode = reader.string();
          continue;
        case 200:
          if (tag !== 1602) {
            break;
          }

          message.goodsPcDesc = reader.string();
          continue;
        case 210:
          if (tag !== 1682) {
            break;
          }

          message.goodsH5Desc = reader.string();
          continue;
        case 230:
          if (tag !== 1842) {
            break;
          }

          message.skuStepRanges.push(
            SkuStepRange.decode(reader, reader.uint32())
          );
          continue;
        case 240:
          if (tag !== 1922) {
            break;
          }

          message.attrList.push(GoodsAttrModel.decode(reader, reader.uint32()));
          continue;
        case 250:
          if (tag !== 2002) {
            break;
          }

          message.specList.push(
            MidSkuSpecModel.decode(reader, reader.uint32())
          );
          continue;
        case 260:
          if (tag !== 2082) {
            break;
          }

          message.skuList.push(MidSkuInfoModel.decode(reader, reader.uint32()));
          continue;
        case 270:
          if (tag !== 2160) {
            break;
          }

          message.isAudit = reader.bool();
          continue;
        case 280:
          if (tag !== 2240) {
            break;
          }

          message.isIllegal = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsInfoModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      brandName: isSet(object.brandName)
        ? globalThis.String(object.brandName)
        : "",
      categoryPath: globalThis.Array.isArray(object?.categoryPath)
        ? object.categoryPath.map((e: any) =>
            MidCategoryPathItemModel.fromJSON(e)
          )
        : [],
      goodsName: isSet(object.goodsName)
        ? globalThis.String(object.goodsName)
        : "",
      goodsTitle: isSet(object.goodsTitle)
        ? globalThis.String(object.goodsTitle)
        : "",
      isOnline: isSet(object.isOnline)
        ? globalThis.Boolean(object.isOnline)
        : false,
      currency: isSet(object.currency)
        ? currencyTypeFromJSON(object.currency)
        : 0,
      goodsPriceUnitName: isSet(object.goodsPriceUnitName)
        ? globalThis.String(object.goodsPriceUnitName)
        : "",
      packageInsideCount: isSet(object.packageInsideCount)
        ? globalThis.Number(object.packageInsideCount)
        : 0,
      packageInsideUnit: isSet(object.packageInsideUnit)
        ? globalThis.String(object.packageInsideUnit)
        : "",
      minBuyQuantity: isSet(object.minBuyQuantity)
        ? globalThis.Number(object.minBuyQuantity)
        : 0,
      minIncreaseQuantity: isSet(object.minIncreaseQuantity)
        ? globalThis.Number(object.minIncreaseQuantity)
        : 0,
      coverImage: isSet(object.coverImage)
        ? globalThis.String(object.coverImage)
        : "",
      videoUrl: isSet(object.videoUrl)
        ? globalThis.String(object.videoUrl)
        : "",
      goodsImageList: globalThis.Array.isArray(object?.goodsImageList)
        ? object.goodsImageList.map((e: any) => globalThis.String(e))
        : [],
      goodsLength: isSet(object.goodsLength)
        ? globalThis.Number(object.goodsLength)
        : 0,
      goodsWidth: isSet(object.goodsWidth)
        ? globalThis.Number(object.goodsWidth)
        : 0,
      goodsHeight: isSet(object.goodsHeight)
        ? globalThis.Number(object.goodsHeight)
        : 0,
      goodsWeight: isSet(object.goodsWeight)
        ? globalThis.Number(object.goodsWeight)
        : 0,
      freightTips: isSet(object.freightTips)
        ? globalThis.String(object.freightTips)
        : "",
      goodsBarcode: isSet(object.goodsBarcode)
        ? globalThis.String(object.goodsBarcode)
        : "",
      goodsPcDesc: isSet(object.goodsPcDesc)
        ? globalThis.String(object.goodsPcDesc)
        : "",
      goodsH5Desc: isSet(object.goodsH5Desc)
        ? globalThis.String(object.goodsH5Desc)
        : "",
      skuStepRanges: globalThis.Array.isArray(object?.skuStepRanges)
        ? object.skuStepRanges.map((e: any) => SkuStepRange.fromJSON(e))
        : [],
      attrList: globalThis.Array.isArray(object?.attrList)
        ? object.attrList.map((e: any) => GoodsAttrModel.fromJSON(e))
        : [],
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => MidSkuSpecModel.fromJSON(e))
        : [],
      skuList: globalThis.Array.isArray(object?.skuList)
        ? object.skuList.map((e: any) => MidSkuInfoModel.fromJSON(e))
        : [],
      isAudit: isSet(object.isAudit)
        ? globalThis.Boolean(object.isAudit)
        : false,
      isIllegal: isSet(object.isIllegal)
        ? globalThis.Boolean(object.isIllegal)
        : false,
    };
  },

  toJSON(message: MidGoodsInfoModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.brandName !== "") {
      obj.brandName = message.brandName;
    }
    if (message.categoryPath?.length) {
      obj.categoryPath = message.categoryPath.map((e) =>
        MidCategoryPathItemModel.toJSON(e)
      );
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.isOnline !== false) {
      obj.isOnline = message.isOnline;
    }
    if (message.currency !== 0) {
      obj.currency = currencyTypeToJSON(message.currency);
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.packageInsideCount !== 0) {
      obj.packageInsideCount = Math.round(message.packageInsideCount);
    }
    if (message.packageInsideUnit !== "") {
      obj.packageInsideUnit = message.packageInsideUnit;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      obj.minIncreaseQuantity = Math.round(message.minIncreaseQuantity);
    }
    if (message.coverImage !== "") {
      obj.coverImage = message.coverImage;
    }
    if (message.videoUrl !== "") {
      obj.videoUrl = message.videoUrl;
    }
    if (message.goodsImageList?.length) {
      obj.goodsImageList = message.goodsImageList;
    }
    if (message.goodsLength !== 0) {
      obj.goodsLength = message.goodsLength;
    }
    if (message.goodsWidth !== 0) {
      obj.goodsWidth = message.goodsWidth;
    }
    if (message.goodsHeight !== 0) {
      obj.goodsHeight = message.goodsHeight;
    }
    if (message.goodsWeight !== 0) {
      obj.goodsWeight = message.goodsWeight;
    }
    if (message.freightTips !== "") {
      obj.freightTips = message.freightTips;
    }
    if (message.goodsBarcode !== "") {
      obj.goodsBarcode = message.goodsBarcode;
    }
    if (message.goodsPcDesc !== "") {
      obj.goodsPcDesc = message.goodsPcDesc;
    }
    if (message.goodsH5Desc !== "") {
      obj.goodsH5Desc = message.goodsH5Desc;
    }
    if (message.skuStepRanges?.length) {
      obj.skuStepRanges = message.skuStepRanges.map((e) =>
        SkuStepRange.toJSON(e)
      );
    }
    if (message.attrList?.length) {
      obj.attrList = message.attrList.map((e) => GoodsAttrModel.toJSON(e));
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) => MidSkuSpecModel.toJSON(e));
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => MidSkuInfoModel.toJSON(e));
    }
    if (message.isAudit !== false) {
      obj.isAudit = message.isAudit;
    }
    if (message.isIllegal !== false) {
      obj.isIllegal = message.isIllegal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsInfoModel>, I>>(
    base?: I
  ): MidGoodsInfoModel {
    return MidGoodsInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsInfoModel>, I>>(
    object: I
  ): MidGoodsInfoModel {
    const message = createBaseMidGoodsInfoModel();
    message.id = object.id ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.brandName = object.brandName ?? "";
    message.categoryPath =
      object.categoryPath?.map((e) =>
        MidCategoryPathItemModel.fromPartial(e)
      ) || [];
    message.goodsName = object.goodsName ?? "";
    message.goodsTitle = object.goodsTitle ?? "";
    message.isOnline = object.isOnline ?? false;
    message.currency = object.currency ?? 0;
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.packageInsideCount = object.packageInsideCount ?? 0;
    message.packageInsideUnit = object.packageInsideUnit ?? "";
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.minIncreaseQuantity = object.minIncreaseQuantity ?? 0;
    message.coverImage = object.coverImage ?? "";
    message.videoUrl = object.videoUrl ?? "";
    message.goodsImageList = object.goodsImageList?.map((e) => e) || [];
    message.goodsLength = object.goodsLength ?? 0;
    message.goodsWidth = object.goodsWidth ?? 0;
    message.goodsHeight = object.goodsHeight ?? 0;
    message.goodsWeight = object.goodsWeight ?? 0;
    message.freightTips = object.freightTips ?? "";
    message.goodsBarcode = object.goodsBarcode ?? "";
    message.goodsPcDesc = object.goodsPcDesc ?? "";
    message.goodsH5Desc = object.goodsH5Desc ?? "";
    message.skuStepRanges =
      object.skuStepRanges?.map((e) => SkuStepRange.fromPartial(e)) || [];
    message.attrList =
      object.attrList?.map((e) => GoodsAttrModel.fromPartial(e)) || [];
    message.specList =
      object.specList?.map((e) => MidSkuSpecModel.fromPartial(e)) || [];
    message.skuList =
      object.skuList?.map((e) => MidSkuInfoModel.fromPartial(e)) || [];
    message.isAudit = object.isAudit ?? false;
    message.isIllegal = object.isIllegal ?? false;
    return message;
  },
};

function createBaseMidSkuSpecModel(): MidSkuSpecModel {
  return { id: "", name: "", items: [] };
}

export const MidSkuSpecModel = {
  encode(
    message: MidSkuSpecModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    for (const v of message.items) {
      MidSkuSpecItemModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuSpecModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuSpecModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.items.push(
            MidSkuSpecItemModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuSpecModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => MidSkuSpecItemModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidSkuSpecModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.items?.length) {
      obj.items = message.items.map((e) => MidSkuSpecItemModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuSpecModel>, I>>(
    base?: I
  ): MidSkuSpecModel {
    return MidSkuSpecModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuSpecModel>, I>>(
    object: I
  ): MidSkuSpecModel {
    const message = createBaseMidSkuSpecModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.items =
      object.items?.map((e) => MidSkuSpecItemModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidSkuSpecItemModel(): MidSkuSpecItemModel {
  return { itemId: "", itemName: "", imageUrl: "", color: "" };
}

export const MidSkuSpecItemModel = {
  encode(
    message: MidSkuSpecItemModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.itemId !== "") {
      writer.uint32(82).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(162).string(message.itemName);
    }
    if (message.imageUrl !== "") {
      writer.uint32(242).string(message.imageUrl);
    }
    if (message.color !== "") {
      writer.uint32(322).string(message.color);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuSpecItemModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuSpecItemModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.itemName = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.color = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuSpecItemModel {
    return {
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName)
        ? globalThis.String(object.itemName)
        : "",
      imageUrl: isSet(object.imageUrl)
        ? globalThis.String(object.imageUrl)
        : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
    };
  },

  toJSON(message: MidSkuSpecItemModel): unknown {
    const obj: any = {};
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuSpecItemModel>, I>>(
    base?: I
  ): MidSkuSpecItemModel {
    return MidSkuSpecItemModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuSpecItemModel>, I>>(
    object: I
  ): MidSkuSpecItemModel {
    const message = createBaseMidSkuSpecItemModel();
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    message.imageUrl = object.imageUrl ?? "";
    message.color = object.color ?? "";
    return message;
  },
};

function createBaseMidSkuInfoModel(): MidSkuInfoModel {
  return {
    id: "",
    skuNo: "",
    salePrice: 0,
    specItems: [],
    stepPrices: [],
    stockInfo: undefined,
  };
}

export const MidSkuInfoModel = {
  encode(
    message: MidSkuInfoModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.skuNo !== "") {
      writer.uint32(162).string(message.skuNo);
    }
    if (message.salePrice !== 0) {
      writer.uint32(241).double(message.salePrice);
    }
    for (const v of message.specItems) {
      MidSkuSpecIdPairModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    for (const v of message.stepPrices) {
      SkuStepPrice.encode(v!, writer.uint32(482).fork()).ldelim();
    }
    if (message.stockInfo !== undefined) {
      MidSkuStockModel.encode(
        message.stockInfo,
        writer.uint32(562).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuInfoModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.salePrice = reader.double();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.specItems.push(
            MidSkuSpecIdPairModel.decode(reader, reader.uint32())
          );
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.stepPrices.push(SkuStepPrice.decode(reader, reader.uint32()));
          continue;
        case 70:
          if (tag !== 562) {
            break;
          }

          message.stockInfo = MidSkuStockModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuInfoModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      salePrice: isSet(object.salePrice)
        ? globalThis.Number(object.salePrice)
        : 0,
      specItems: globalThis.Array.isArray(object?.specItems)
        ? object.specItems.map((e: any) => MidSkuSpecIdPairModel.fromJSON(e))
        : [],
      stepPrices: globalThis.Array.isArray(object?.stepPrices)
        ? object.stepPrices.map((e: any) => SkuStepPrice.fromJSON(e))
        : [],
      stockInfo: isSet(object.stockInfo)
        ? MidSkuStockModel.fromJSON(object.stockInfo)
        : undefined,
    };
  },

  toJSON(message: MidSkuInfoModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.salePrice !== 0) {
      obj.salePrice = message.salePrice;
    }
    if (message.specItems?.length) {
      obj.specItems = message.specItems.map((e) =>
        MidSkuSpecIdPairModel.toJSON(e)
      );
    }
    if (message.stepPrices?.length) {
      obj.stepPrices = message.stepPrices.map((e) => SkuStepPrice.toJSON(e));
    }
    if (message.stockInfo !== undefined) {
      obj.stockInfo = MidSkuStockModel.toJSON(message.stockInfo);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuInfoModel>, I>>(
    base?: I
  ): MidSkuInfoModel {
    return MidSkuInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuInfoModel>, I>>(
    object: I
  ): MidSkuInfoModel {
    const message = createBaseMidSkuInfoModel();
    message.id = object.id ?? "";
    message.skuNo = object.skuNo ?? "";
    message.salePrice = object.salePrice ?? 0;
    message.specItems =
      object.specItems?.map((e) => MidSkuSpecIdPairModel.fromPartial(e)) || [];
    message.stepPrices =
      object.stepPrices?.map((e) => SkuStepPrice.fromPartial(e)) || [];
    message.stockInfo =
      object.stockInfo !== undefined && object.stockInfo !== null
        ? MidSkuStockModel.fromPartial(object.stockInfo)
        : undefined;
    return message;
  },
};

function createBaseMidSkuStockModel(): MidSkuStockModel {
  return { id: "", salesQty: 0, realQty: 0, freezeQty: 0, availableQty: 0 };
}

export const MidSkuStockModel = {
  encode(
    message: MidSkuStockModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.salesQty !== 0) {
      writer.uint32(160).int32(message.salesQty);
    }
    if (message.realQty !== 0) {
      writer.uint32(240).int32(message.realQty);
    }
    if (message.freezeQty !== 0) {
      writer.uint32(320).int32(message.freezeQty);
    }
    if (message.availableQty !== 0) {
      writer.uint32(400).int32(message.availableQty);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuStockModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuStockModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.salesQty = reader.int32();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.realQty = reader.int32();
          continue;
        case 40:
          if (tag !== 320) {
            break;
          }

          message.freezeQty = reader.int32();
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.availableQty = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuStockModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      salesQty: isSet(object.salesQty) ? globalThis.Number(object.salesQty) : 0,
      realQty: isSet(object.realQty) ? globalThis.Number(object.realQty) : 0,
      freezeQty: isSet(object.freezeQty)
        ? globalThis.Number(object.freezeQty)
        : 0,
      availableQty: isSet(object.availableQty)
        ? globalThis.Number(object.availableQty)
        : 0,
    };
  },

  toJSON(message: MidSkuStockModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.salesQty !== 0) {
      obj.salesQty = Math.round(message.salesQty);
    }
    if (message.realQty !== 0) {
      obj.realQty = Math.round(message.realQty);
    }
    if (message.freezeQty !== 0) {
      obj.freezeQty = Math.round(message.freezeQty);
    }
    if (message.availableQty !== 0) {
      obj.availableQty = Math.round(message.availableQty);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuStockModel>, I>>(
    base?: I
  ): MidSkuStockModel {
    return MidSkuStockModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuStockModel>, I>>(
    object: I
  ): MidSkuStockModel {
    const message = createBaseMidSkuStockModel();
    message.id = object.id ?? "";
    message.salesQty = object.salesQty ?? 0;
    message.realQty = object.realQty ?? 0;
    message.freezeQty = object.freezeQty ?? 0;
    message.availableQty = object.availableQty ?? 0;
    return message;
  },
};

function createBaseMidSkuSpecIdPairModel(): MidSkuSpecIdPairModel {
  return { specId: "", specName: "", itemId: "", itemName: "" };
}

export const MidSkuSpecIdPairModel = {
  encode(
    message: MidSkuSpecIdPairModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.specId !== "") {
      writer.uint32(82).string(message.specId);
    }
    if (message.specName !== "") {
      writer.uint32(162).string(message.specName);
    }
    if (message.itemId !== "") {
      writer.uint32(242).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(322).string(message.itemName);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidSkuSpecIdPairModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuSpecIdPairModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.specId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.specName = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.itemName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuSpecIdPairModel {
    return {
      specId: isSet(object.specId) ? globalThis.String(object.specId) : "",
      specName: isSet(object.specName)
        ? globalThis.String(object.specName)
        : "",
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName)
        ? globalThis.String(object.itemName)
        : "",
    };
  },

  toJSON(message: MidSkuSpecIdPairModel): unknown {
    const obj: any = {};
    if (message.specId !== "") {
      obj.specId = message.specId;
    }
    if (message.specName !== "") {
      obj.specName = message.specName;
    }
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuSpecIdPairModel>, I>>(
    base?: I
  ): MidSkuSpecIdPairModel {
    return MidSkuSpecIdPairModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuSpecIdPairModel>, I>>(
    object: I
  ): MidSkuSpecIdPairModel {
    const message = createBaseMidSkuSpecIdPairModel();
    message.specId = object.specId ?? "";
    message.specName = object.specName ?? "";
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    return message;
  },
};

function createBaseMidCategoryPathItemModel(): MidCategoryPathItemModel {
  return { id: "", name: "" };
}

export const MidCategoryPathItemModel = {
  encode(
    message: MidCategoryPathItemModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidCategoryPathItemModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCategoryPathItemModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCategoryPathItemModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: MidCategoryPathItemModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCategoryPathItemModel>, I>>(
    base?: I
  ): MidCategoryPathItemModel {
    return MidCategoryPathItemModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCategoryPathItemModel>, I>>(
    object: I
  ): MidCategoryPathItemModel {
    const message = createBaseMidCategoryPathItemModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseMidCategoryTreeResp(): MidCategoryTreeResp {
  return { result: undefined, data: undefined };
}

export const MidCategoryTreeResp = {
  encode(
    message: MidCategoryTreeResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MidCategoryModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCategoryTreeResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCategoryTreeResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MidCategoryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCategoryTreeResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data)
        ? MidCategoryModel.fromJSON(object.data)
        : undefined,
    };
  },

  toJSON(message: MidCategoryTreeResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MidCategoryModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCategoryTreeResp>, I>>(
    base?: I
  ): MidCategoryTreeResp {
    return MidCategoryTreeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCategoryTreeResp>, I>>(
    object: I
  ): MidCategoryTreeResp {
    const message = createBaseMidCategoryTreeResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? MidCategoryModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseMidCategoryModel(): MidCategoryModel {
  return { id: "", name: "", cateLogo: "", cateIcon: "", children: [] };
}

export const MidCategoryModel = {
  encode(
    message: MidCategoryModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.cateLogo !== "") {
      writer.uint32(26).string(message.cateLogo);
    }
    if (message.cateIcon !== "") {
      writer.uint32(34).string(message.cateIcon);
    }
    for (const v of message.children) {
      MidCategoryModel.encode(v!, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCategoryModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCategoryModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.cateLogo = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.cateIcon = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.children.push(
            MidCategoryModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCategoryModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      cateLogo: isSet(object.cateLogo)
        ? globalThis.String(object.cateLogo)
        : "",
      cateIcon: isSet(object.cateIcon)
        ? globalThis.String(object.cateIcon)
        : "",
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) => MidCategoryModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidCategoryModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.cateLogo !== "") {
      obj.cateLogo = message.cateLogo;
    }
    if (message.cateIcon !== "") {
      obj.cateIcon = message.cateIcon;
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) => MidCategoryModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCategoryModel>, I>>(
    base?: I
  ): MidCategoryModel {
    return MidCategoryModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCategoryModel>, I>>(
    object: I
  ): MidCategoryModel {
    const message = createBaseMidCategoryModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.cateLogo = object.cateLogo ?? "";
    message.cateIcon = object.cateIcon ?? "";
    message.children =
      object.children?.map((e) => MidCategoryModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidGoodsPageResp(): MidGoodsPageResp {
  return { result: undefined, page: undefined, data: [] };
}

export const MidGoodsPageResp = {
  encode(
    message: MidGoodsPageResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      MidGoodsModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsPageResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsPageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(MidGoodsModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsPageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MidGoodsModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidGoodsPageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MidGoodsModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsPageResp>, I>>(
    base?: I
  ): MidGoodsPageResp {
    return MidGoodsPageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsPageResp>, I>>(
    object: I
  ): MidGoodsPageResp {
    const message = createBaseMidGoodsPageResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.page =
      object.page !== undefined && object.page !== null
        ? Page.fromPartial(object.page)
        : undefined;
    message.data = object.data?.map((e) => MidGoodsModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidGoodsModel(): MidGoodsModel {
  return {
    id: "",
    goodsImageShow: "",
    goodsName: "",
    goodsNo: "",
    minPrice: 0,
    minOldPrice: 0,
    brandId: "",
    brandName: "",
    categoryId: "",
    categoryName: "",
    categoryId2th: "",
    categoryName2th: "",
    categoryId1th: "",
    categoryName1th: "",
    goodsImage: "",
    goodsWeight: 0,
    minBuyQuantity: 0,
    stockQuantity: 0,
    goodsTitle: "",
    goodsPriceUnitId: "",
    goodsPriceUnitName: "",
    taxRate: 0,
    skuList: [],
    minSalesPrice: 0,
    minOldSalesPrice: 0,
    defaultSku: undefined,
    sales: 0,
  };
}

export const MidGoodsModel = {
  encode(
    message: MidGoodsModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.goodsImageShow !== "") {
      writer.uint32(18).string(message.goodsImageShow);
    }
    if (message.goodsName !== "") {
      writer.uint32(26).string(message.goodsName);
    }
    if (message.goodsNo !== "") {
      writer.uint32(34).string(message.goodsNo);
    }
    if (message.minPrice !== 0) {
      writer.uint32(41).double(message.minPrice);
    }
    if (message.minOldPrice !== 0) {
      writer.uint32(49).double(message.minOldPrice);
    }
    if (message.brandId !== "") {
      writer.uint32(58).string(message.brandId);
    }
    if (message.brandName !== "") {
      writer.uint32(66).string(message.brandName);
    }
    if (message.categoryId !== "") {
      writer.uint32(74).string(message.categoryId);
    }
    if (message.categoryName !== "") {
      writer.uint32(82).string(message.categoryName);
    }
    if (message.categoryId2th !== "") {
      writer.uint32(90).string(message.categoryId2th);
    }
    if (message.categoryName2th !== "") {
      writer.uint32(98).string(message.categoryName2th);
    }
    if (message.categoryId1th !== "") {
      writer.uint32(106).string(message.categoryId1th);
    }
    if (message.categoryName1th !== "") {
      writer.uint32(114).string(message.categoryName1th);
    }
    if (message.goodsImage !== "") {
      writer.uint32(122).string(message.goodsImage);
    }
    if (message.goodsWeight !== 0) {
      writer.uint32(129).double(message.goodsWeight);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(136).int32(message.minBuyQuantity);
    }
    if (message.stockQuantity !== 0) {
      writer.uint32(144).int32(message.stockQuantity);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(154).string(message.goodsTitle);
    }
    if (message.goodsPriceUnitId !== "") {
      writer.uint32(162).string(message.goodsPriceUnitId);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(170).string(message.goodsPriceUnitName);
    }
    if (message.taxRate !== 0) {
      writer.uint32(177).double(message.taxRate);
    }
    for (const v of message.skuList) {
      MidSkuModel.encode(v!, writer.uint32(202).fork()).ldelim();
    }
    if (message.minSalesPrice !== 0) {
      writer.uint32(209).double(message.minSalesPrice);
    }
    if (message.minOldSalesPrice !== 0) {
      writer.uint32(217).double(message.minOldSalesPrice);
    }
    if (message.defaultSku !== undefined) {
      MidSkuModel.encode(
        message.defaultSku,
        writer.uint32(226).fork()
      ).ldelim();
    }
    if (message.sales !== 0) {
      writer.uint32(232).int32(message.sales);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.goodsImageShow = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }

          message.minOldPrice = reader.double();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.brandId = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.brandName = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.categoryName = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.categoryId2th = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.categoryName2th = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.categoryId1th = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.categoryName1th = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.goodsImage = reader.string();
          continue;
        case 16:
          if (tag !== 129) {
            break;
          }

          message.goodsWeight = reader.double();
          continue;
        case 17:
          if (tag !== 136) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.stockQuantity = reader.int32();
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsPriceUnitId = reader.string();
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 22:
          if (tag !== 177) {
            break;
          }

          message.taxRate = reader.double();
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.skuList.push(MidSkuModel.decode(reader, reader.uint32()));
          continue;
        case 26:
          if (tag !== 209) {
            break;
          }

          message.minSalesPrice = reader.double();
          continue;
        case 27:
          if (tag !== 217) {
            break;
          }

          message.minOldSalesPrice = reader.double();
          continue;
        case 28:
          if (tag !== 226) {
            break;
          }

          message.defaultSku = MidSkuModel.decode(reader, reader.uint32());
          continue;
        case 29:
          if (tag !== 232) {
            break;
          }

          message.sales = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      goodsImageShow: isSet(object.goodsImageShow)
        ? globalThis.String(object.goodsImageShow)
        : "",
      goodsName: isSet(object.goodsName)
        ? globalThis.String(object.goodsName)
        : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      minOldPrice: isSet(object.minOldPrice)
        ? globalThis.Number(object.minOldPrice)
        : 0,
      brandId: isSet(object.brandId) ? globalThis.String(object.brandId) : "",
      brandName: isSet(object.brandName)
        ? globalThis.String(object.brandName)
        : "",
      categoryId: isSet(object.categoryId)
        ? globalThis.String(object.categoryId)
        : "",
      categoryName: isSet(object.categoryName)
        ? globalThis.String(object.categoryName)
        : "",
      categoryId2th: isSet(object.categoryId2th)
        ? globalThis.String(object.categoryId2th)
        : "",
      categoryName2th: isSet(object.categoryName2th)
        ? globalThis.String(object.categoryName2th)
        : "",
      categoryId1th: isSet(object.categoryId1th)
        ? globalThis.String(object.categoryId1th)
        : "",
      categoryName1th: isSet(object.categoryName1th)
        ? globalThis.String(object.categoryName1th)
        : "",
      goodsImage: isSet(object.goodsImage)
        ? globalThis.String(object.goodsImage)
        : "",
      goodsWeight: isSet(object.goodsWeight)
        ? globalThis.Number(object.goodsWeight)
        : 0,
      minBuyQuantity: isSet(object.minBuyQuantity)
        ? globalThis.Number(object.minBuyQuantity)
        : 0,
      stockQuantity: isSet(object.stockQuantity)
        ? globalThis.Number(object.stockQuantity)
        : 0,
      goodsTitle: isSet(object.goodsTitle)
        ? globalThis.String(object.goodsTitle)
        : "",
      goodsPriceUnitId: isSet(object.goodsPriceUnitId)
        ? globalThis.String(object.goodsPriceUnitId)
        : "",
      goodsPriceUnitName: isSet(object.goodsPriceUnitName)
        ? globalThis.String(object.goodsPriceUnitName)
        : "",
      taxRate: isSet(object.taxRate) ? globalThis.Number(object.taxRate) : 0,
      skuList: globalThis.Array.isArray(object?.skuList)
        ? object.skuList.map((e: any) => MidSkuModel.fromJSON(e))
        : [],
      minSalesPrice: isSet(object.minSalesPrice)
        ? globalThis.Number(object.minSalesPrice)
        : 0,
      minOldSalesPrice: isSet(object.minOldSalesPrice)
        ? globalThis.Number(object.minOldSalesPrice)
        : 0,
      defaultSku: isSet(object.defaultSku)
        ? MidSkuModel.fromJSON(object.defaultSku)
        : undefined,
      sales: isSet(object.sales) ? globalThis.Number(object.sales) : 0,
    };
  },

  toJSON(message: MidGoodsModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.goodsImageShow !== "") {
      obj.goodsImageShow = message.goodsImageShow;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.minOldPrice !== 0) {
      obj.minOldPrice = message.minOldPrice;
    }
    if (message.brandId !== "") {
      obj.brandId = message.brandId;
    }
    if (message.brandName !== "") {
      obj.brandName = message.brandName;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.categoryName !== "") {
      obj.categoryName = message.categoryName;
    }
    if (message.categoryId2th !== "") {
      obj.categoryId2th = message.categoryId2th;
    }
    if (message.categoryName2th !== "") {
      obj.categoryName2th = message.categoryName2th;
    }
    if (message.categoryId1th !== "") {
      obj.categoryId1th = message.categoryId1th;
    }
    if (message.categoryName1th !== "") {
      obj.categoryName1th = message.categoryName1th;
    }
    if (message.goodsImage !== "") {
      obj.goodsImage = message.goodsImage;
    }
    if (message.goodsWeight !== 0) {
      obj.goodsWeight = message.goodsWeight;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.stockQuantity !== 0) {
      obj.stockQuantity = Math.round(message.stockQuantity);
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.goodsPriceUnitId !== "") {
      obj.goodsPriceUnitId = message.goodsPriceUnitId;
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.taxRate !== 0) {
      obj.taxRate = message.taxRate;
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => MidSkuModel.toJSON(e));
    }
    if (message.minSalesPrice !== 0) {
      obj.minSalesPrice = message.minSalesPrice;
    }
    if (message.minOldSalesPrice !== 0) {
      obj.minOldSalesPrice = message.minOldSalesPrice;
    }
    if (message.defaultSku !== undefined) {
      obj.defaultSku = MidSkuModel.toJSON(message.defaultSku);
    }
    if (message.sales !== 0) {
      obj.sales = Math.round(message.sales);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsModel>, I>>(
    base?: I
  ): MidGoodsModel {
    return MidGoodsModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsModel>, I>>(
    object: I
  ): MidGoodsModel {
    const message = createBaseMidGoodsModel();
    message.id = object.id ?? "";
    message.goodsImageShow = object.goodsImageShow ?? "";
    message.goodsName = object.goodsName ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.minPrice = object.minPrice ?? 0;
    message.minOldPrice = object.minOldPrice ?? 0;
    message.brandId = object.brandId ?? "";
    message.brandName = object.brandName ?? "";
    message.categoryId = object.categoryId ?? "";
    message.categoryName = object.categoryName ?? "";
    message.categoryId2th = object.categoryId2th ?? "";
    message.categoryName2th = object.categoryName2th ?? "";
    message.categoryId1th = object.categoryId1th ?? "";
    message.categoryName1th = object.categoryName1th ?? "";
    message.goodsImage = object.goodsImage ?? "";
    message.goodsWeight = object.goodsWeight ?? 0;
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.stockQuantity = object.stockQuantity ?? 0;
    message.goodsTitle = object.goodsTitle ?? "";
    message.goodsPriceUnitId = object.goodsPriceUnitId ?? "";
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.taxRate = object.taxRate ?? 0;
    message.skuList =
      object.skuList?.map((e) => MidSkuModel.fromPartial(e)) || [];
    message.minSalesPrice = object.minSalesPrice ?? 0;
    message.minOldSalesPrice = object.minOldSalesPrice ?? 0;
    message.defaultSku =
      object.defaultSku !== undefined && object.defaultSku !== null
        ? MidSkuModel.fromPartial(object.defaultSku)
        : undefined;
    message.sales = object.sales ?? 0;
    return message;
  },
};

function createBaseMidSkuModel(): MidSkuModel {
  return {
    skuId: "",
    skuNo: "",
    skuName: "",
    minPrice: 0,
    packageCount: 0,
    packageUnit: "",
    deliveryDate: "",
    image: "",
    priceObj: undefined,
    specList: [],
    skuStockQuantity: 0,
    goodsPrice: [],
  };
}

export const MidSkuModel = {
  encode(
    message: MidSkuModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.skuId !== "") {
      writer.uint32(10).string(message.skuId);
    }
    if (message.skuNo !== "") {
      writer.uint32(18).string(message.skuNo);
    }
    if (message.skuName !== "") {
      writer.uint32(26).string(message.skuName);
    }
    if (message.minPrice !== 0) {
      writer.uint32(33).double(message.minPrice);
    }
    if (message.packageCount !== 0) {
      writer.uint32(40).int32(message.packageCount);
    }
    if (message.packageUnit !== "") {
      writer.uint32(50).string(message.packageUnit);
    }
    if (message.deliveryDate !== "") {
      writer.uint32(58).string(message.deliveryDate);
    }
    if (message.image !== "") {
      writer.uint32(66).string(message.image);
    }
    if (message.priceObj !== undefined) {
      MidSkuPriceModel.encode(
        message.priceObj,
        writer.uint32(74).fork()
      ).ldelim();
    }
    for (const v of message.specList) {
      MidSkuSpecIdPairModel.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    if (message.skuStockQuantity !== 0) {
      writer.uint32(88).int32(message.skuStockQuantity);
    }
    for (const v of message.goodsPrice) {
      SkuStepPrice.encode(v!, writer.uint32(98).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.skuId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.skuName = reader.string();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.packageCount = reader.int32();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.packageUnit = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.deliveryDate = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.image = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.priceObj = MidSkuPriceModel.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.specList.push(
            MidSkuSpecIdPairModel.decode(reader, reader.uint32())
          );
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.skuStockQuantity = reader.int32();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.goodsPrice.push(SkuStepPrice.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuModel {
    return {
      skuId: isSet(object.skuId) ? globalThis.String(object.skuId) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      skuName: isSet(object.skuName) ? globalThis.String(object.skuName) : "",
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      packageCount: isSet(object.packageCount)
        ? globalThis.Number(object.packageCount)
        : 0,
      packageUnit: isSet(object.packageUnit)
        ? globalThis.String(object.packageUnit)
        : "",
      deliveryDate: isSet(object.deliveryDate)
        ? globalThis.String(object.deliveryDate)
        : "",
      image: isSet(object.image) ? globalThis.String(object.image) : "",
      priceObj: isSet(object.priceObj)
        ? MidSkuPriceModel.fromJSON(object.priceObj)
        : undefined,
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => MidSkuSpecIdPairModel.fromJSON(e))
        : [],
      skuStockQuantity: isSet(object.skuStockQuantity)
        ? globalThis.Number(object.skuStockQuantity)
        : 0,
      goodsPrice: globalThis.Array.isArray(object?.goodsPrice)
        ? object.goodsPrice.map((e: any) => SkuStepPrice.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidSkuModel): unknown {
    const obj: any = {};
    if (message.skuId !== "") {
      obj.skuId = message.skuId;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.skuName !== "") {
      obj.skuName = message.skuName;
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.packageCount !== 0) {
      obj.packageCount = Math.round(message.packageCount);
    }
    if (message.packageUnit !== "") {
      obj.packageUnit = message.packageUnit;
    }
    if (message.deliveryDate !== "") {
      obj.deliveryDate = message.deliveryDate;
    }
    if (message.image !== "") {
      obj.image = message.image;
    }
    if (message.priceObj !== undefined) {
      obj.priceObj = MidSkuPriceModel.toJSON(message.priceObj);
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) =>
        MidSkuSpecIdPairModel.toJSON(e)
      );
    }
    if (message.skuStockQuantity !== 0) {
      obj.skuStockQuantity = Math.round(message.skuStockQuantity);
    }
    if (message.goodsPrice?.length) {
      obj.goodsPrice = message.goodsPrice.map((e) => SkuStepPrice.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuModel>, I>>(base?: I): MidSkuModel {
    return MidSkuModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuModel>, I>>(
    object: I
  ): MidSkuModel {
    const message = createBaseMidSkuModel();
    message.skuId = object.skuId ?? "";
    message.skuNo = object.skuNo ?? "";
    message.skuName = object.skuName ?? "";
    message.minPrice = object.minPrice ?? 0;
    message.packageCount = object.packageCount ?? 0;
    message.packageUnit = object.packageUnit ?? "";
    message.deliveryDate = object.deliveryDate ?? "";
    message.image = object.image ?? "";
    message.priceObj =
      object.priceObj !== undefined && object.priceObj !== null
        ? MidSkuPriceModel.fromPartial(object.priceObj)
        : undefined;
    message.specList =
      object.specList?.map((e) => MidSkuSpecIdPairModel.fromPartial(e)) || [];
    message.skuStockQuantity = object.skuStockQuantity ?? 0;
    message.goodsPrice =
      object.goodsPrice?.map((e) => SkuStepPrice.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidSkuPriceModel(): MidSkuPriceModel {
  return {
    salePrice: 0,
    oldPrice: 0,
    oldPriceShow: 0,
    costPrice: 0,
    stepPrice: "",
    memPriceShow: 0,
    supplierSettlePrice: 0,
  };
}

export const MidSkuPriceModel = {
  encode(
    message: MidSkuPriceModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.salePrice !== 0) {
      writer.uint32(9).double(message.salePrice);
    }
    if (message.oldPrice !== 0) {
      writer.uint32(17).double(message.oldPrice);
    }
    if (message.oldPriceShow !== 0) {
      writer.uint32(24).int32(message.oldPriceShow);
    }
    if (message.costPrice !== 0) {
      writer.uint32(33).double(message.costPrice);
    }
    if (message.stepPrice !== "") {
      writer.uint32(42).string(message.stepPrice);
    }
    if (message.memPriceShow !== 0) {
      writer.uint32(48).int32(message.memPriceShow);
    }
    if (message.supplierSettlePrice !== 0) {
      writer.uint32(57).double(message.supplierSettlePrice);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSkuPriceModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSkuPriceModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.salePrice = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.oldPrice = reader.double();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.oldPriceShow = reader.int32();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.costPrice = reader.double();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.stepPrice = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.memPriceShow = reader.int32();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }

          message.supplierSettlePrice = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSkuPriceModel {
    return {
      salePrice: isSet(object.salePrice)
        ? globalThis.Number(object.salePrice)
        : 0,
      oldPrice: isSet(object.oldPrice) ? globalThis.Number(object.oldPrice) : 0,
      oldPriceShow: isSet(object.oldPriceShow)
        ? globalThis.Number(object.oldPriceShow)
        : 0,
      costPrice: isSet(object.costPrice)
        ? globalThis.Number(object.costPrice)
        : 0,
      stepPrice: isSet(object.stepPrice)
        ? globalThis.String(object.stepPrice)
        : "",
      memPriceShow: isSet(object.memPriceShow)
        ? globalThis.Number(object.memPriceShow)
        : 0,
      supplierSettlePrice: isSet(object.supplierSettlePrice)
        ? globalThis.Number(object.supplierSettlePrice)
        : 0,
    };
  },

  toJSON(message: MidSkuPriceModel): unknown {
    const obj: any = {};
    if (message.salePrice !== 0) {
      obj.salePrice = message.salePrice;
    }
    if (message.oldPrice !== 0) {
      obj.oldPrice = message.oldPrice;
    }
    if (message.oldPriceShow !== 0) {
      obj.oldPriceShow = Math.round(message.oldPriceShow);
    }
    if (message.costPrice !== 0) {
      obj.costPrice = message.costPrice;
    }
    if (message.stepPrice !== "") {
      obj.stepPrice = message.stepPrice;
    }
    if (message.memPriceShow !== 0) {
      obj.memPriceShow = Math.round(message.memPriceShow);
    }
    if (message.supplierSettlePrice !== 0) {
      obj.supplierSettlePrice = message.supplierSettlePrice;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSkuPriceModel>, I>>(
    base?: I
  ): MidSkuPriceModel {
    return MidSkuPriceModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSkuPriceModel>, I>>(
    object: I
  ): MidSkuPriceModel {
    const message = createBaseMidSkuPriceModel();
    message.salePrice = object.salePrice ?? 0;
    message.oldPrice = object.oldPrice ?? 0;
    message.oldPriceShow = object.oldPriceShow ?? 0;
    message.costPrice = object.costPrice ?? 0;
    message.stepPrice = object.stepPrice ?? "";
    message.memPriceShow = object.memPriceShow ?? 0;
    message.supplierSettlePrice = object.supplierSettlePrice ?? 0;
    return message;
  },
};

function createBaseMidGoodsStockSummaryResp(): MidGoodsStockSummaryResp {
  return { result: undefined, data: {} };
}

export const MidGoodsStockSummaryResp = {
  encode(
    message: MidGoodsStockSummaryResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    Object.entries(message.data).forEach(([key, value]) => {
      MidGoodsStockSummaryResp_DataEntry.encode(
        { key: key as any, value },
        writer.uint32(18).fork()
      ).ldelim();
    });
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidGoodsStockSummaryResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsStockSummaryResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          const entry2 = MidGoodsStockSummaryResp_DataEntry.decode(
            reader,
            reader.uint32()
          );
          if (entry2.value !== undefined) {
            message.data[entry2.key] = entry2.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsStockSummaryResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isObject(object.data)
        ? Object.entries(object.data).reduce<{
            [key: string]: MidStockSummaryModel;
          }>((acc, [key, value]) => {
            acc[key] = MidStockSummaryModel.fromJSON(value);
            return acc;
          }, {})
        : {},
    };
  },

  toJSON(message: MidGoodsStockSummaryResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data) {
      const entries = Object.entries(message.data);
      if (entries.length > 0) {
        obj.data = {};
        entries.forEach(([k, v]) => {
          obj.data[k] = MidStockSummaryModel.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsStockSummaryResp>, I>>(
    base?: I
  ): MidGoodsStockSummaryResp {
    return MidGoodsStockSummaryResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsStockSummaryResp>, I>>(
    object: I
  ): MidGoodsStockSummaryResp {
    const message = createBaseMidGoodsStockSummaryResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data = Object.entries(object.data ?? {}).reduce<{
      [key: string]: MidStockSummaryModel;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = MidStockSummaryModel.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseMidGoodsStockSummaryResp_DataEntry(): MidGoodsStockSummaryResp_DataEntry {
  return { key: "", value: undefined };
}

export const MidGoodsStockSummaryResp_DataEntry = {
  encode(
    message: MidGoodsStockSummaryResp_DataEntry,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      MidStockSummaryModel.encode(
        message.value,
        writer.uint32(18).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidGoodsStockSummaryResp_DataEntry {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsStockSummaryResp_DataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = MidStockSummaryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsStockSummaryResp_DataEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value)
        ? MidStockSummaryModel.fromJSON(object.value)
        : undefined,
    };
  },

  toJSON(message: MidGoodsStockSummaryResp_DataEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = MidStockSummaryModel.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsStockSummaryResp_DataEntry>, I>>(
    base?: I
  ): MidGoodsStockSummaryResp_DataEntry {
    return MidGoodsStockSummaryResp_DataEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<
    I extends Exact<DeepPartial<MidGoodsStockSummaryResp_DataEntry>, I>
  >(object: I): MidGoodsStockSummaryResp_DataEntry {
    const message = createBaseMidGoodsStockSummaryResp_DataEntry();
    message.key = object.key ?? "";
    message.value =
      object.value !== undefined && object.value !== null
        ? MidStockSummaryModel.fromPartial(object.value)
        : undefined;
    return message;
  },
};

function createBaseMidStockSummaryModel(): MidStockSummaryModel {
  return { salesQty: 0, realStockQty: 0, availableStockQty: 0 };
}

export const MidStockSummaryModel = {
  encode(
    message: MidStockSummaryModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.salesQty !== 0) {
      writer.uint32(80).int32(message.salesQty);
    }
    if (message.realStockQty !== 0) {
      writer.uint32(160).int32(message.realStockQty);
    }
    if (message.availableStockQty !== 0) {
      writer.uint32(240).int32(message.availableStockQty);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidStockSummaryModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidStockSummaryModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.salesQty = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.realStockQty = reader.int32();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.availableStockQty = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidStockSummaryModel {
    return {
      salesQty: isSet(object.salesQty) ? globalThis.Number(object.salesQty) : 0,
      realStockQty: isSet(object.realStockQty)
        ? globalThis.Number(object.realStockQty)
        : 0,
      availableStockQty: isSet(object.availableStockQty)
        ? globalThis.Number(object.availableStockQty)
        : 0,
    };
  },

  toJSON(message: MidStockSummaryModel): unknown {
    const obj: any = {};
    if (message.salesQty !== 0) {
      obj.salesQty = Math.round(message.salesQty);
    }
    if (message.realStockQty !== 0) {
      obj.realStockQty = Math.round(message.realStockQty);
    }
    if (message.availableStockQty !== 0) {
      obj.availableStockQty = Math.round(message.availableStockQty);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidStockSummaryModel>, I>>(
    base?: I
  ): MidStockSummaryModel {
    return MidStockSummaryModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidStockSummaryModel>, I>>(
    object: I
  ): MidStockSummaryModel {
    const message = createBaseMidStockSummaryModel();
    message.salesQty = object.salesQty ?? 0;
    message.realStockQty = object.realStockQty ?? 0;
    message.availableStockQty = object.availableStockQty ?? 0;
    return message;
  },
};

function createBaseMidSearchResultResp(): MidSearchResultResp {
  return { result: undefined, data: undefined };
}

export const MidSearchResultResp = {
  encode(
    message: MidSearchResultResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MidSearchResultModel.encode(
        message.data,
        writer.uint32(18).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSearchResultResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSearchResultResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MidSearchResultModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSearchResultResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data)
        ? MidSearchResultModel.fromJSON(object.data)
        : undefined,
    };
  },

  toJSON(message: MidSearchResultResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MidSearchResultModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSearchResultResp>, I>>(
    base?: I
  ): MidSearchResultResp {
    return MidSearchResultResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSearchResultResp>, I>>(
    object: I
  ): MidSearchResultResp {
    const message = createBaseMidSearchResultResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? MidSearchResultModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseMidSearchResultModel(): MidSearchResultModel {
  return {
    page: undefined,
    goodsList: [],
    mallCategoryFilters: [],
    tokenizeWords: "",
  };
}

export const MidSearchResultModel = {
  encode(
    message: MidSearchResultModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    for (const v of message.goodsList) {
      MidSearchGoodsModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    for (const v of message.mallCategoryFilters) {
      MidSearchCategoryFilterModel.encode(
        v!,
        writer.uint32(242).fork()
      ).ldelim();
    }
    if (message.tokenizeWords !== "") {
      writer.uint32(322).string(message.tokenizeWords);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidSearchResultModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSearchResultModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsList.push(
            MidSearchGoodsModel.decode(reader, reader.uint32())
          );
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.mallCategoryFilters.push(
            MidSearchCategoryFilterModel.decode(reader, reader.uint32())
          );
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.tokenizeWords = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSearchResultModel {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      goodsList: globalThis.Array.isArray(object?.goodsList)
        ? object.goodsList.map((e: any) => MidSearchGoodsModel.fromJSON(e))
        : [],
      mallCategoryFilters: globalThis.Array.isArray(object?.mallCategoryFilters)
        ? object.mallCategoryFilters.map((e: any) =>
            MidSearchCategoryFilterModel.fromJSON(e)
          )
        : [],
      tokenizeWords: isSet(object.tokenizeWords)
        ? globalThis.String(object.tokenizeWords)
        : "",
    };
  },

  toJSON(message: MidSearchResultModel): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.goodsList?.length) {
      obj.goodsList = message.goodsList.map((e) =>
        MidSearchGoodsModel.toJSON(e)
      );
    }
    if (message.mallCategoryFilters?.length) {
      obj.mallCategoryFilters = message.mallCategoryFilters.map((e) =>
        MidSearchCategoryFilterModel.toJSON(e)
      );
    }
    if (message.tokenizeWords !== "") {
      obj.tokenizeWords = message.tokenizeWords;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSearchResultModel>, I>>(
    base?: I
  ): MidSearchResultModel {
    return MidSearchResultModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSearchResultModel>, I>>(
    object: I
  ): MidSearchResultModel {
    const message = createBaseMidSearchResultModel();
    message.page =
      object.page !== undefined && object.page !== null
        ? Page.fromPartial(object.page)
        : undefined;
    message.goodsList =
      object.goodsList?.map((e) => MidSearchGoodsModel.fromPartial(e)) || [];
    message.mallCategoryFilters =
      object.mallCategoryFilters?.map((e) =>
        MidSearchCategoryFilterModel.fromPartial(e)
      ) || [];
    message.tokenizeWords = object.tokenizeWords ?? "";
    return message;
  },
};

function createBaseMidSearchGoodsModel(): MidSearchGoodsModel {
  return {
    goodsId: "",
    goodsNo: "",
    goodsName: "",
    goodsTitle: "",
    goodsPriceUnitName: "",
    currency: 0,
    minPrice: 0,
    maxPrice: 0,
    minBuyQuantity: 0,
    minIncreaseQuantity: 0,
    mainImageUrl: "",
    backendCategoryName: "",
    goodsRank: 0,
    hitScore: 0,
  };
}

export const MidSearchGoodsModel = {
  encode(
    message: MidSearchGoodsModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(82).string(message.goodsId);
    }
    if (message.goodsNo !== "") {
      writer.uint32(162).string(message.goodsNo);
    }
    if (message.goodsName !== "") {
      writer.uint32(242).string(message.goodsName);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(322).string(message.goodsTitle);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(362).string(message.goodsPriceUnitName);
    }
    if (message.currency !== 0) {
      writer.uint32(368).int32(message.currency);
    }
    if (message.minPrice !== 0) {
      writer.uint32(401).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(441).double(message.maxPrice);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(480).int32(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      writer.uint32(560).int32(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      writer.uint32(642).string(message.mainImageUrl);
    }
    if (message.backendCategoryName !== "") {
      writer.uint32(722).string(message.backendCategoryName);
    }
    if (message.goodsRank !== 0) {
      writer.uint32(1685).float(message.goodsRank);
    }
    if (message.hitScore !== 0) {
      writer.uint32(1765).float(message.hitScore);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSearchGoodsModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSearchGoodsModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 45:
          if (tag !== 362) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 46:
          if (tag !== 368) {
            break;
          }

          message.currency = reader.int32() as any;
          continue;
        case 50:
          if (tag !== 401) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 55:
          if (tag !== 441) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 60:
          if (tag !== 480) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.minIncreaseQuantity = reader.int32();
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.mainImageUrl = reader.string();
          continue;
        case 90:
          if (tag !== 722) {
            break;
          }

          message.backendCategoryName = reader.string();
          continue;
        case 210:
          if (tag !== 1685) {
            break;
          }

          message.goodsRank = reader.float();
          continue;
        case 220:
          if (tag !== 1765) {
            break;
          }

          message.hitScore = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSearchGoodsModel {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      goodsName: isSet(object.goodsName)
        ? globalThis.String(object.goodsName)
        : "",
      goodsTitle: isSet(object.goodsTitle)
        ? globalThis.String(object.goodsTitle)
        : "",
      goodsPriceUnitName: isSet(object.goodsPriceUnitName)
        ? globalThis.String(object.goodsPriceUnitName)
        : "",
      currency: isSet(object.currency)
        ? currencyTypeFromJSON(object.currency)
        : 0,
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      minBuyQuantity: isSet(object.minBuyQuantity)
        ? globalThis.Number(object.minBuyQuantity)
        : 0,
      minIncreaseQuantity: isSet(object.minIncreaseQuantity)
        ? globalThis.Number(object.minIncreaseQuantity)
        : 0,
      mainImageUrl: isSet(object.mainImageUrl)
        ? globalThis.String(object.mainImageUrl)
        : "",
      backendCategoryName: isSet(object.backendCategoryName)
        ? globalThis.String(object.backendCategoryName)
        : "",
      goodsRank: isSet(object.goodsRank)
        ? globalThis.Number(object.goodsRank)
        : 0,
      hitScore: isSet(object.hitScore) ? globalThis.Number(object.hitScore) : 0,
    };
  },

  toJSON(message: MidSearchGoodsModel): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.currency !== 0) {
      obj.currency = currencyTypeToJSON(message.currency);
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      obj.minIncreaseQuantity = Math.round(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      obj.mainImageUrl = message.mainImageUrl;
    }
    if (message.backendCategoryName !== "") {
      obj.backendCategoryName = message.backendCategoryName;
    }
    if (message.goodsRank !== 0) {
      obj.goodsRank = message.goodsRank;
    }
    if (message.hitScore !== 0) {
      obj.hitScore = message.hitScore;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSearchGoodsModel>, I>>(
    base?: I
  ): MidSearchGoodsModel {
    return MidSearchGoodsModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSearchGoodsModel>, I>>(
    object: I
  ): MidSearchGoodsModel {
    const message = createBaseMidSearchGoodsModel();
    message.goodsId = object.goodsId ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.goodsName = object.goodsName ?? "";
    message.goodsTitle = object.goodsTitle ?? "";
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.currency = object.currency ?? 0;
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.minIncreaseQuantity = object.minIncreaseQuantity ?? 0;
    message.mainImageUrl = object.mainImageUrl ?? "";
    message.backendCategoryName = object.backendCategoryName ?? "";
    message.goodsRank = object.goodsRank ?? 0;
    message.hitScore = object.hitScore ?? 0;
    return message;
  },
};

function createBaseMidSearchCategoryFilterModel(): MidSearchCategoryFilterModel {
  return { id: "", name: "", children: [] };
}

export const MidSearchCategoryFilterModel = {
  encode(
    message: MidSearchCategoryFilterModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    for (const v of message.children) {
      MidSearchCategoryFilterModel.encode(
        v!,
        writer.uint32(242).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidSearchCategoryFilterModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSearchCategoryFilterModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.children.push(
            MidSearchCategoryFilterModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSearchCategoryFilterModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) =>
            MidSearchCategoryFilterModel.fromJSON(e)
          )
        : [],
    };
  },

  toJSON(message: MidSearchCategoryFilterModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) =>
        MidSearchCategoryFilterModel.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSearchCategoryFilterModel>, I>>(
    base?: I
  ): MidSearchCategoryFilterModel {
    return MidSearchCategoryFilterModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSearchCategoryFilterModel>, I>>(
    object: I
  ): MidSearchCategoryFilterModel {
    const message = createBaseMidSearchCategoryFilterModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.children =
      object.children?.map((e) =>
        MidSearchCategoryFilterModel.fromPartial(e)
      ) || [];
    return message;
  },
};

function createBaseMallCategoryTreeModel(): MallCategoryTreeModel {
  return { id: "", cateName: "", cateLogo: "", goodsCount: 0, children: [] };
}

export const MallCategoryTreeModel = {
  encode(
    message: MallCategoryTreeModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.cateName !== "") {
      writer.uint32(18).string(message.cateName);
    }
    if (message.cateLogo !== "") {
      writer.uint32(26).string(message.cateLogo);
    }
    if (message.goodsCount !== 0) {
      writer.uint32(32).int32(message.goodsCount);
    }
    for (const v of message.children) {
      MallCategoryTreeModel.encode(v!, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MallCategoryTreeModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallCategoryTreeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.cateName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.cateLogo = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.goodsCount = reader.int32();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.children.push(
            MallCategoryTreeModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallCategoryTreeModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      cateName: isSet(object.cateName)
        ? globalThis.String(object.cateName)
        : "",
      cateLogo: isSet(object.cateLogo)
        ? globalThis.String(object.cateLogo)
        : "",
      goodsCount: isSet(object.goodsCount)
        ? globalThis.Number(object.goodsCount)
        : 0,
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) => MallCategoryTreeModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MallCategoryTreeModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.cateName !== "") {
      obj.cateName = message.cateName;
    }
    if (message.cateLogo !== "") {
      obj.cateLogo = message.cateLogo;
    }
    if (message.goodsCount !== 0) {
      obj.goodsCount = Math.round(message.goodsCount);
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) =>
        MallCategoryTreeModel.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallCategoryTreeModel>, I>>(
    base?: I
  ): MallCategoryTreeModel {
    return MallCategoryTreeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallCategoryTreeModel>, I>>(
    object: I
  ): MallCategoryTreeModel {
    const message = createBaseMallCategoryTreeModel();
    message.id = object.id ?? "";
    message.cateName = object.cateName ?? "";
    message.cateLogo = object.cateLogo ?? "";
    message.goodsCount = object.goodsCount ?? 0;
    message.children =
      object.children?.map((e) => MallCategoryTreeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMallCategoryPathItemModel(): MallCategoryPathItemModel {
  return { id: "", name: "" };
}

export const MallCategoryPathItemModel = {
  encode(
    message: MallCategoryPathItemModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MallCategoryPathItemModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallCategoryPathItemModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallCategoryPathItemModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: MallCategoryPathItemModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallCategoryPathItemModel>, I>>(
    base?: I
  ): MallCategoryPathItemModel {
    return MallCategoryPathItemModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallCategoryPathItemModel>, I>>(
    object: I
  ): MallCategoryPathItemModel {
    const message = createBaseMallCategoryPathItemModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseRecreateESIndexResp(): RecreateESIndexResp {
  return { result: undefined, data: undefined };
}

export const RecreateESIndexResp = {
  encode(
    message: RecreateESIndexResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      RecreateESIndexModel.encode(
        message.data,
        writer.uint32(18).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RecreateESIndexResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecreateESIndexResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = RecreateESIndexModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecreateESIndexResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data)
        ? RecreateESIndexModel.fromJSON(object.data)
        : undefined,
    };
  },

  toJSON(message: RecreateESIndexResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = RecreateESIndexModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecreateESIndexResp>, I>>(
    base?: I
  ): RecreateESIndexResp {
    return RecreateESIndexResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecreateESIndexResp>, I>>(
    object: I
  ): RecreateESIndexResp {
    const message = createBaseRecreateESIndexResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? RecreateESIndexModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseRecreateESIndexModel(): RecreateESIndexModel {
  return { updateCount: 0, remark: "" };
}

export const RecreateESIndexModel = {
  encode(
    message: RecreateESIndexModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.updateCount !== 0) {
      writer.uint32(80).int32(message.updateCount);
    }
    if (message.remark !== "") {
      writer.uint32(162).string(message.remark);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): RecreateESIndexModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecreateESIndexModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.updateCount = reader.int32();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.remark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecreateESIndexModel {
    return {
      updateCount: isSet(object.updateCount)
        ? globalThis.Number(object.updateCount)
        : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : "",
    };
  },

  toJSON(message: RecreateESIndexModel): unknown {
    const obj: any = {};
    if (message.updateCount !== 0) {
      obj.updateCount = Math.round(message.updateCount);
    }
    if (message.remark !== "") {
      obj.remark = message.remark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecreateESIndexModel>, I>>(
    base?: I
  ): RecreateESIndexModel {
    return RecreateESIndexModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecreateESIndexModel>, I>>(
    object: I
  ): RecreateESIndexModel {
    const message = createBaseRecreateESIndexModel();
    message.updateCount = object.updateCount ?? 0;
    message.remark = object.remark ?? "";
    return message;
  },
};

function createBaseUpdateAllGoodsESIndexResp(): UpdateAllGoodsESIndexResp {
  return { result: undefined, data: undefined };
}

export const UpdateAllGoodsESIndexResp = {
  encode(
    message: UpdateAllGoodsESIndexResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      UpdateAllGoodsESIndexModel.encode(
        message.data,
        writer.uint32(18).fork()
      ).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): UpdateAllGoodsESIndexResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAllGoodsESIndexResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = UpdateAllGoodsESIndexModel.decode(
            reader,
            reader.uint32()
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateAllGoodsESIndexResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data)
        ? UpdateAllGoodsESIndexModel.fromJSON(object.data)
        : undefined,
    };
  },

  toJSON(message: UpdateAllGoodsESIndexResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = UpdateAllGoodsESIndexModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAllGoodsESIndexResp>, I>>(
    base?: I
  ): UpdateAllGoodsESIndexResp {
    return UpdateAllGoodsESIndexResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAllGoodsESIndexResp>, I>>(
    object: I
  ): UpdateAllGoodsESIndexResp {
    const message = createBaseUpdateAllGoodsESIndexResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data !== undefined && object.data !== null
        ? UpdateAllGoodsESIndexModel.fromPartial(object.data)
        : undefined;
    return message;
  },
};

function createBaseUpdateAllGoodsESIndexModel(): UpdateAllGoodsESIndexModel {
  return { updateCount: 0, remark: "" };
}

export const UpdateAllGoodsESIndexModel = {
  encode(
    message: UpdateAllGoodsESIndexModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.updateCount !== 0) {
      writer.uint32(80).int32(message.updateCount);
    }
    if (message.remark !== "") {
      writer.uint32(162).string(message.remark);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): UpdateAllGoodsESIndexModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAllGoodsESIndexModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.updateCount = reader.int32();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.remark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateAllGoodsESIndexModel {
    return {
      updateCount: isSet(object.updateCount)
        ? globalThis.Number(object.updateCount)
        : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : "",
    };
  },

  toJSON(message: UpdateAllGoodsESIndexModel): unknown {
    const obj: any = {};
    if (message.updateCount !== 0) {
      obj.updateCount = Math.round(message.updateCount);
    }
    if (message.remark !== "") {
      obj.remark = message.remark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAllGoodsESIndexModel>, I>>(
    base?: I
  ): UpdateAllGoodsESIndexModel {
    return UpdateAllGoodsESIndexModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAllGoodsESIndexModel>, I>>(
    object: I
  ): UpdateAllGoodsESIndexModel {
    const message = createBaseUpdateAllGoodsESIndexModel();
    message.updateCount = object.updateCount ?? 0;
    message.remark = object.remark ?? "";
    return message;
  },
};

function createBaseHomePageGoodsResp(): HomePageGoodsResp {
  return { result: undefined, data: [] };
}

export const HomePageGoodsResp = {
  encode(
    message: HomePageGoodsResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      HomePageGoodsModel.encode(v!, writer.uint32(1042).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): HomePageGoodsResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomePageGoodsResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 130:
          if (tag !== 1042) {
            break;
          }

          message.data.push(HomePageGoodsModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomePageGoodsResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => HomePageGoodsModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: HomePageGoodsResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => HomePageGoodsModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomePageGoodsResp>, I>>(
    base?: I
  ): HomePageGoodsResp {
    return HomePageGoodsResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomePageGoodsResp>, I>>(
    object: I
  ): HomePageGoodsResp {
    const message = createBaseHomePageGoodsResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data?.map((e) => HomePageGoodsModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseHomePageGoodsModel(): HomePageGoodsModel {
  return { categoryId: "", categoryName: "", goodsList: [] };
}

export const HomePageGoodsModel = {
  encode(
    message: HomePageGoodsModel,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    if (message.categoryName !== "") {
      writer.uint32(18).string(message.categoryName);
    }
    for (const v of message.goodsList) {
      MidSearchGoodsModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): HomePageGoodsModel {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomePageGoodsModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.categoryName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.goodsList.push(
            MidSearchGoodsModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomePageGoodsModel {
    return {
      categoryId: isSet(object.categoryId)
        ? globalThis.String(object.categoryId)
        : "",
      categoryName: isSet(object.categoryName)
        ? globalThis.String(object.categoryName)
        : "",
      goodsList: globalThis.Array.isArray(object?.goodsList)
        ? object.goodsList.map((e: any) => MidSearchGoodsModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: HomePageGoodsModel): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.categoryName !== "") {
      obj.categoryName = message.categoryName;
    }
    if (message.goodsList?.length) {
      obj.goodsList = message.goodsList.map((e) =>
        MidSearchGoodsModel.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomePageGoodsModel>, I>>(
    base?: I
  ): HomePageGoodsModel {
    return HomePageGoodsModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomePageGoodsModel>, I>>(
    object: I
  ): HomePageGoodsModel {
    const message = createBaseHomePageGoodsModel();
    message.categoryId = object.categoryId ?? "";
    message.categoryName = object.categoryName ?? "";
    message.goodsList =
      object.goodsList?.map((e) => MidSearchGoodsModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidRecommendGoodsResp(): MidRecommendGoodsResp {
  return { result: undefined, data: [] };
}

export const MidRecommendGoodsResp = {
  encode(
    message: MidRecommendGoodsResp,
    writer: _m0.Writer = _m0.Writer.create()
  ): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      MidSearchGoodsModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number
  ): MidRecommendGoodsResp {
    const reader =
      input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidRecommendGoodsResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(
            MidSearchGoodsModel.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidRecommendGoodsResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MidSearchGoodsModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidRecommendGoodsResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MidSearchGoodsModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidRecommendGoodsResp>, I>>(
    base?: I
  ): MidRecommendGoodsResp {
    return MidRecommendGoodsResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidRecommendGoodsResp>, I>>(
    object: I
  ): MidRecommendGoodsResp {
    const message = createBaseMidRecommendGoodsResp();
    message.result =
      object.result !== undefined && object.result !== null
        ? Result.fromPartial(object.result)
        : undefined;
    message.data =
      object.data?.map((e) => MidSearchGoodsModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin =
  | Date
  | Function
  | Uint8Array
  | string
  | number
  | boolean
  | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
  ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U>
  ? ReadonlyArray<DeepPartial<U>>
  : T extends {}
  ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & {
      [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
    };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
