syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.param";

import "common.proto";
import "common/business.proto";

// SQL报表分页参数
message SqlReportPageQueryParam {
    common.PageParam page = 1;
    SqlReportListQueryParam query = 2;
}

// SQL报表列表查询参数
message SqlReportListQueryParam {
    string id = 1; // 报表ID
}

// SQL报表数据查询参数
message SqlReportDataQueryParam {
    string id = 10; // 报表ID
    repeated SqlReportFilterParam filterParams = 20;
}

// 过滤参数
message SqlReportFilterParam {
    string fieldName = 10; //字段名
    string fieldValue = 20; //字段值
}