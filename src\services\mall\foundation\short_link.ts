/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { GetFullLinkResp, GetShortLinkResp } from "./model/short_link_model";
import { GetFullLinkParam, GetShortLinkParam } from "./param/short_link_param";

export const protobufPackage = "mall.foundation";

/** 短链接服务 */
export interface ShortLink {
  /** 获取短链接（传参完整URL，返回转换后的URL） */
  getShortLink(request: GetShortLinkParam): Promise<GetShortLinkResp>;
  /** 获取完整链接（传参链接代码，返回源URL） */
  getFullLink(request: GetFullLinkParam): Promise<GetFullLinkResp>;
}

export const ShortLinkServiceName = "mall.foundation.ShortLink";
export class ShortLinkClientImpl implements ShortLink {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || ShortLinkServiceName;
    this.rpc = rpc;
    this.getShortLink = this.getShortLink.bind(this);
    this.getFullLink = this.getFullLink.bind(this);
  }
  getShortLink(request: GetShortLinkParam): Promise<GetShortLinkResp> {
    const data = GetShortLinkParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getShortLink", data);
    return promise.then((data) => GetShortLinkResp.decode(_m0.Reader.create(data)));
  }

  getFullLink(request: GetFullLinkParam): Promise<GetFullLinkResp> {
    const data = GetFullLinkParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getFullLink", data);
    return promise.then((data) => GetFullLinkResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
