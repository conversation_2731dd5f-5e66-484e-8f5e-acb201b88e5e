syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "common.proto";
import "chilat/basis/param/milvus_param.proto";
import "chilat/basis/model/milvus_model.proto";

service MilvusManager {
  rpc milvusCreateCollection(basis.MilvusCreateCollectionParam) returns (common.ApiResult);

  rpc milvusCreateGoodsCollection(basis.MilvusCreateCollectionParam) returns (common.ApiResult);

  rpc milvusDropCollection(basis.MilvusDropCollectionParam) returns (common.ApiResult);

  rpc milvusShowCollections(basis.MilvusShowCollectionsParam) returns (basis.MilvusShowCollectionsResult);

  rpc milvusDeleteByIds(basis.MilvusDeleteByIdsParam) returns (common.ApiResult);
}