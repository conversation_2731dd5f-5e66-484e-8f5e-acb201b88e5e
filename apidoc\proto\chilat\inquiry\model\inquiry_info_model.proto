syntax = "proto3";

package chilat.inquiry;

import "chilat/inquiry/inquiry_common.proto";
import "common.proto";


option java_package = "com.chilat.rpc.inquiry.model";


/**
询盘列表返回
 */
message InquiryInfoPageResp {
    common.Result result = 1;
    InquiryInfoPageData data = 2; // 商品列表数据
}

message InquiryInfoPageData {
    common.Page page = 3;
    repeated InquiryInfoModel data = 4;
    int64 noInquiryCount = 5; // 未询价数
    int64 progressInquiryCount = 6; // 询价中数
    int64 hasInquiryCount = 7; // 询价完成数
}
//询盘列表
message InquiryInfoModel {
    string id = 1; // 询价主键ID
    string inquiryBatchNo = 2; // 询价批次号，用于唯一标识一个询价批次
    string submitId = 3; // 客户id
    string submitName = 4; // 客户名称，表示发起询价的客户
    int64 storeCount = 9; // 店铺数
    int64 goodsCount = 10; // 商品数
    int64 skuCount = 11; // sku数(未回填+已回填)
    int64 noSkuInquiryCount = 12; // 未询sku数（未回填 详情店铺中SKU商品完整装箱信息的数)
    int64 hasSkuInquiryCount = 13; // 已询sku数（已回填 详情店铺中SKU商品完整装箱信息的数）
    string currentInquiryPersonId = 14; // 现询价人，ID
    string currentInquiryPerson = 15; // 现询价人，当前负责处理询价的员工姓名
    string originalInquiryPerson = 16; // 原询价人，最初负责处理询价的员工姓名
    string originalInquiryPersonId = 17; // 原询价人，ID
    int64 cdate = 18; // 创建时间
    SkuStatus skuStatus = 19; // SKU状态, 0未询，1已询
    InquiryStatus inquiryStatus = 20;//询价单状态，通过点击 开始询价按钮/完成询价按钮变更状态（未询价：NO_INQUIRY  询价中：PROGRESS_INQUIRY 已询价：HAS_INQUIRY）
    int64 inquiryStartTime = 21;//询价开始时间
    int64  inquiryFinshTime = 22;//询价完成时间

}
