syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/foundation/model/system_config_model.proto";
import "chilat/foundation/param/system_config_param.proto";
import "common.proto";

// 系统配置
service SystemConfig {
  // 获取系统配置
  rpc getSystemConfig (common.EmptyParam) returns (SystemConfigResp);
  // 保存系统配置
  rpc saveSystemConfig (SystemConfigSaveParam) returns (common.ApiResult);
  // 查询系统配置日志
  rpc listLog (common.EmptyParam) returns (SystemConfigLogListResp);
  // 获取商品标签
  rpc listGoodsTag(common.EmptyParam) returns (GoodsTagListResp);
}
