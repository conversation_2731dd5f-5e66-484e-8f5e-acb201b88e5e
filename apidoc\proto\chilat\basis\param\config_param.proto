syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";

message SysTranslateParam {
    string id = 1;
    int32 systemId = 2;
    string moduleCode = 3;
    string tranCode = 4;
    string termZH = 5;
    string termEN = 6;
    string termES = 7;
}

message TranslatePageQueryParam {
    common.PageParam page = 1;
    TranslateQueryParam query = 2;
}

message TranslateQueryParam {
    TranslateQueryType type = 1; //字段类型
    string term = 2; //字段
}

enum TranslateQueryType {
    TRANSLATE_QUERY_TYPE_UNKNOWN = 0;
    TRANSLATE_QUERY_TYPE_KEY = 1; // 关键字
    TRANSLATE_QUERY_TYPE_ZH = 2; //中文
    TRANSLATE_QUERY_TYPE_EN = 3; //英文
    TRANSLATE_QUERY_TYPE_ES = 4; //西班牙语
}

//-----------   中间件缓存更新  ----------
message ProviderCacheUpdateParam {
    repeated ProviderCacheUpdateItem list = 1;
}
message ProviderCacheUpdateItem {
    int32 type = 1; // 缓存数据类型（定义：enum ProviderDataType）
    repeated string ids = 2; // ID列表
}
