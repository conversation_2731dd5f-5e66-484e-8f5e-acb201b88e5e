syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.model";

import "common.proto";

message MemberPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated MemberModel data = 3;
}

message MemberResp {
  common.Result result = 1;
  MemberModel data = 2;
}

// 客户信息
message MemberModel {
  string id = 1;
  string userId = 2; // 用户ID
  string memberName = 3; // 客户名
  string whatsapp = 4; // whatsapp
  string countryId = 5; // 国家ID
  string countryName = 6; // 国家名称
  string email = 7; // 邮箱
  string memberId = 8; // 客户userID
}