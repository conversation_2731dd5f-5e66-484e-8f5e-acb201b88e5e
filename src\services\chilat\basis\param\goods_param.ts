/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { PageParam } from "../../../common";
import {
  CurrencyType,
  currencyTypeFromJSON,
  currencyTypeToJSON,
  VisitDeviceType,
  visitDeviceTypeFromJSON,
  visitDeviceTypeToJSON,
} from "../../../common/business";
import {
  GoodsOnlineDays,
  goodsOnlineDaysFromJSON,
  goodsOnlineDaysToJSON,
  StockLocation,
  stockLocationFromJSON,
  stockLocationToJSON,
} from "../../marketing/marketing_rule_common";
import Long from "long";

export const protobufPackage = "chilat.basis";

export interface MidGoodsPageQueryParamByRule {
  page: PageParam | undefined;
  query: MidGoodsQueryParamByRuleParam | undefined;
}

export interface MidGoodsQueryParamByRuleParam {
  /** 规则id */
  marketingRuleId: string;
  /** 分类 */
  categoryIds: string[];
  /** 品牌 */
  brandIds: string[];
  /** 最低销量 */
  minimumSales: number;
  /** 展示数量 */
  quantity: number;
  /** 商品上架时间 */
  goodsOnlineDays: GoodsOnlineDays;
  /** 商品id */
  goodsIds: string[];
  /** 关键字 */
  keyword: string;
  /** 是否是商城 */
  isMall: boolean;
  /** 库存位置 */
  stockLocation: StockLocation;
}

export interface MidGoodsInfoQueryParam {
  /** 根据接口，传 goodsId 或 skuId */
  id: string;
  /** 响应结果中的货币单位（必填） */
  responseCurrency: CurrencyType;
  /** 是否为预览模式（预览模式下，若商品未下架状态，则返回所有下架SKU；默认false） */
  previewMode: boolean;
}

export interface MidSearchGoodsParam {
  /** 商品ID列表 */
  goodsIds: string[];
  /** 商城前台类目ID（营销分类；营销分类、营销规则ID、营销规则代码等三者取并集） */
  frontendCategoryIds: string[];
  /** 商城前台子类目ID（若此值非空，则以子类目ID取商品数据，父类目ID取过滤项） */
  frontendChildCategoryIds: string[];
  /** admin后台类目ID（admin商品分类） */
  backendCategoryIds: string[];
  /** 营销规则ID（营销分类、营销规则ID、营销规则代码等三者取并集） */
  marketingRuleIds: string[];
  /** 营销规则代码（营销分类、营销规则ID、营销规则代码等三者取并集） */
  marketingRuleCodes: string[];
  /** 品牌ID，多个用下划线（_）分隔 */
  brandIds: string[];
  /** 关键字 */
  keyword: string;
  /** 最低价限制 */
  minPrice: number;
  /** 最高价限制 */
  maxPrice: number;
  /** 最早发布时间（包含；毫秒时间戳，GTM+8时区） */
  minPublishTime: number;
  /** 最晚发布时间（不包含；毫秒时间戳，GTM+8时区） */
  maxPublishTime: number;
  /** 小于等于“最小购买数量”（起订量） */
  leMinBuyQuantity: number;
  /** 大于等于“最小购买数量”（起订量） */
  geMinBuyQuantity: number;
  /** 排序字段（11:人气升序；12:人气倒序；21:销量升序；22:销量倒序；31:价格升序；32:价格倒序） */
  sortField: number;
  /** 页号（从1开始，默认1） */
  pageNo: number;
  /** 每页显示条数（后端自动匹配到最接近的页面条数） */
  pageSize: number;
  /** 查询条数限制（仅大于0的数字，限制条数起作用） */
  fetchCountLimit: number;
  /** int32 fetchAggregationFlag = 110; //获取聚合参数表示位（参考：AggregationFlagEnum） */
  abtestMode: string;
  /** 请求参数或返回中的默认货币单位 */
  defaultCurrency: CurrencyType;
  /** 请求参数中的货币单位（若defaultCurrency留空，则必填） */
  requestCurrency: CurrencyType;
  /** 响应结果中的货币单位（若defaultCurrency留空，则必填） */
  responseCurrency: CurrencyType;
  /** 是否随机 */
  random: boolean;
  /** 标签id */
  tagIds: string[];
}

export interface MidAddGoodsImpressParam {
  /** 每页显示条数 */
  pageSize: number;
  /** 总共找到的条数 */
  totalHits: number;
  /** Key: 商品ID, Value: Position（Position从0开始） */
  goodsImpressPostionMap: { [key: string]: number };
}

export interface MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
  key: string;
  value: number;
}

export interface MidSetImpressConversionParam {
  /** 商品ID */
  goodsId: string;
  /** 转化类型（GoodsImpressConversionEnum） */
  conversion: number;
}

export interface RecommendGoodsParam {
  /** 返回多少个商品 */
  goodsCount: number;
  /** 访问设备类型 */
  deviceType: VisitDeviceType;
}

function createBaseMidGoodsPageQueryParamByRule(): MidGoodsPageQueryParamByRule {
  return { page: undefined, query: undefined };
}

export const MidGoodsPageQueryParamByRule = {
  encode(message: MidGoodsPageQueryParamByRule, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(10).fork()).ldelim();
    }
    if (message.query !== undefined) {
      MidGoodsQueryParamByRuleParam.encode(message.query, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsPageQueryParamByRule {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsPageQueryParamByRule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.query = MidGoodsQueryParamByRuleParam.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsPageQueryParamByRule {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      query: isSet(object.query) ? MidGoodsQueryParamByRuleParam.fromJSON(object.query) : undefined,
    };
  },

  toJSON(message: MidGoodsPageQueryParamByRule): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.query !== undefined) {
      obj.query = MidGoodsQueryParamByRuleParam.toJSON(message.query);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsPageQueryParamByRule>, I>>(base?: I): MidGoodsPageQueryParamByRule {
    return MidGoodsPageQueryParamByRule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsPageQueryParamByRule>, I>>(object: I): MidGoodsPageQueryParamByRule {
    const message = createBaseMidGoodsPageQueryParamByRule();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.query = (object.query !== undefined && object.query !== null)
      ? MidGoodsQueryParamByRuleParam.fromPartial(object.query)
      : undefined;
    return message;
  },
};

function createBaseMidGoodsQueryParamByRuleParam(): MidGoodsQueryParamByRuleParam {
  return {
    marketingRuleId: "",
    categoryIds: [],
    brandIds: [],
    minimumSales: 0,
    quantity: 0,
    goodsOnlineDays: 0,
    goodsIds: [],
    keyword: "",
    isMall: false,
    stockLocation: 0,
  };
}

export const MidGoodsQueryParamByRuleParam = {
  encode(message: MidGoodsQueryParamByRuleParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.marketingRuleId !== "") {
      writer.uint32(10).string(message.marketingRuleId);
    }
    for (const v of message.categoryIds) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.brandIds) {
      writer.uint32(42).string(v!);
    }
    if (message.minimumSales !== 0) {
      writer.uint32(48).int32(message.minimumSales);
    }
    if (message.quantity !== 0) {
      writer.uint32(56).int32(message.quantity);
    }
    if (message.goodsOnlineDays !== 0) {
      writer.uint32(64).int32(message.goodsOnlineDays);
    }
    for (const v of message.goodsIds) {
      writer.uint32(74).string(v!);
    }
    if (message.keyword !== "") {
      writer.uint32(82).string(message.keyword);
    }
    if (message.isMall !== false) {
      writer.uint32(88).bool(message.isMall);
    }
    if (message.stockLocation !== 0) {
      writer.uint32(96).int32(message.stockLocation);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsQueryParamByRuleParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsQueryParamByRuleParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.marketingRuleId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.categoryIds.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.brandIds.push(reader.string());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.minimumSales = reader.int32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.quantity = reader.int32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.goodsOnlineDays = reader.int32() as any;
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.goodsIds.push(reader.string());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.keyword = reader.string();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.isMall = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.stockLocation = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsQueryParamByRuleParam {
    return {
      marketingRuleId: isSet(object.marketingRuleId) ? globalThis.String(object.marketingRuleId) : "",
      categoryIds: globalThis.Array.isArray(object?.categoryIds)
        ? object.categoryIds.map((e: any) => globalThis.String(e))
        : [],
      brandIds: globalThis.Array.isArray(object?.brandIds) ? object.brandIds.map((e: any) => globalThis.String(e)) : [],
      minimumSales: isSet(object.minimumSales) ? globalThis.Number(object.minimumSales) : 0,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0,
      goodsOnlineDays: isSet(object.goodsOnlineDays) ? goodsOnlineDaysFromJSON(object.goodsOnlineDays) : 0,
      goodsIds: globalThis.Array.isArray(object?.goodsIds) ? object.goodsIds.map((e: any) => globalThis.String(e)) : [],
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      isMall: isSet(object.isMall) ? globalThis.Boolean(object.isMall) : false,
      stockLocation: isSet(object.stockLocation) ? stockLocationFromJSON(object.stockLocation) : 0,
    };
  },

  toJSON(message: MidGoodsQueryParamByRuleParam): unknown {
    const obj: any = {};
    if (message.marketingRuleId !== "") {
      obj.marketingRuleId = message.marketingRuleId;
    }
    if (message.categoryIds?.length) {
      obj.categoryIds = message.categoryIds;
    }
    if (message.brandIds?.length) {
      obj.brandIds = message.brandIds;
    }
    if (message.minimumSales !== 0) {
      obj.minimumSales = Math.round(message.minimumSales);
    }
    if (message.quantity !== 0) {
      obj.quantity = Math.round(message.quantity);
    }
    if (message.goodsOnlineDays !== 0) {
      obj.goodsOnlineDays = goodsOnlineDaysToJSON(message.goodsOnlineDays);
    }
    if (message.goodsIds?.length) {
      obj.goodsIds = message.goodsIds;
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.isMall !== false) {
      obj.isMall = message.isMall;
    }
    if (message.stockLocation !== 0) {
      obj.stockLocation = stockLocationToJSON(message.stockLocation);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsQueryParamByRuleParam>, I>>(base?: I): MidGoodsQueryParamByRuleParam {
    return MidGoodsQueryParamByRuleParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsQueryParamByRuleParam>, I>>(
    object: I,
  ): MidGoodsQueryParamByRuleParam {
    const message = createBaseMidGoodsQueryParamByRuleParam();
    message.marketingRuleId = object.marketingRuleId ?? "";
    message.categoryIds = object.categoryIds?.map((e) => e) || [];
    message.brandIds = object.brandIds?.map((e) => e) || [];
    message.minimumSales = object.minimumSales ?? 0;
    message.quantity = object.quantity ?? 0;
    message.goodsOnlineDays = object.goodsOnlineDays ?? 0;
    message.goodsIds = object.goodsIds?.map((e) => e) || [];
    message.keyword = object.keyword ?? "";
    message.isMall = object.isMall ?? false;
    message.stockLocation = object.stockLocation ?? 0;
    return message;
  },
};

function createBaseMidGoodsInfoQueryParam(): MidGoodsInfoQueryParam {
  return { id: "", responseCurrency: 0, previewMode: false };
}

export const MidGoodsInfoQueryParam = {
  encode(message: MidGoodsInfoQueryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.responseCurrency !== 0) {
      writer.uint32(160).int32(message.responseCurrency);
    }
    if (message.previewMode !== false) {
      writer.uint32(240).bool(message.previewMode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidGoodsInfoQueryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidGoodsInfoQueryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.responseCurrency = reader.int32() as any;
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.previewMode = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidGoodsInfoQueryParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      responseCurrency: isSet(object.responseCurrency) ? currencyTypeFromJSON(object.responseCurrency) : 0,
      previewMode: isSet(object.previewMode) ? globalThis.Boolean(object.previewMode) : false,
    };
  },

  toJSON(message: MidGoodsInfoQueryParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.responseCurrency !== 0) {
      obj.responseCurrency = currencyTypeToJSON(message.responseCurrency);
    }
    if (message.previewMode !== false) {
      obj.previewMode = message.previewMode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidGoodsInfoQueryParam>, I>>(base?: I): MidGoodsInfoQueryParam {
    return MidGoodsInfoQueryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidGoodsInfoQueryParam>, I>>(object: I): MidGoodsInfoQueryParam {
    const message = createBaseMidGoodsInfoQueryParam();
    message.id = object.id ?? "";
    message.responseCurrency = object.responseCurrency ?? 0;
    message.previewMode = object.previewMode ?? false;
    return message;
  },
};

function createBaseMidSearchGoodsParam(): MidSearchGoodsParam {
  return {
    goodsIds: [],
    frontendCategoryIds: [],
    frontendChildCategoryIds: [],
    backendCategoryIds: [],
    marketingRuleIds: [],
    marketingRuleCodes: [],
    brandIds: [],
    keyword: "",
    minPrice: 0,
    maxPrice: 0,
    minPublishTime: 0,
    maxPublishTime: 0,
    leMinBuyQuantity: 0,
    geMinBuyQuantity: 0,
    sortField: 0,
    pageNo: 0,
    pageSize: 0,
    fetchCountLimit: 0,
    abtestMode: "",
    defaultCurrency: 0,
    requestCurrency: 0,
    responseCurrency: 0,
    random: false,
    tagIds: [],
  };
}

export const MidSearchGoodsParam = {
  encode(message: MidSearchGoodsParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.goodsIds) {
      writer.uint32(42).string(v!);
    }
    for (const v of message.frontendCategoryIds) {
      writer.uint32(82).string(v!);
    }
    for (const v of message.frontendChildCategoryIds) {
      writer.uint32(90).string(v!);
    }
    for (const v of message.backendCategoryIds) {
      writer.uint32(122).string(v!);
    }
    for (const v of message.marketingRuleIds) {
      writer.uint32(170).string(v!);
    }
    for (const v of message.marketingRuleCodes) {
      writer.uint32(178).string(v!);
    }
    for (const v of message.brandIds) {
      writer.uint32(202).string(v!);
    }
    if (message.keyword !== "") {
      writer.uint32(242).string(message.keyword);
    }
    if (message.minPrice !== 0) {
      writer.uint32(361).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(481).double(message.maxPrice);
    }
    if (message.minPublishTime !== 0) {
      writer.uint32(488).int64(message.minPublishTime);
    }
    if (message.maxPublishTime !== 0) {
      writer.uint32(496).int64(message.maxPublishTime);
    }
    if (message.leMinBuyQuantity !== 0) {
      writer.uint32(536).int32(message.leMinBuyQuantity);
    }
    if (message.geMinBuyQuantity !== 0) {
      writer.uint32(544).int32(message.geMinBuyQuantity);
    }
    if (message.sortField !== 0) {
      writer.uint32(560).int32(message.sortField);
    }
    if (message.pageNo !== 0) {
      writer.uint32(640).int32(message.pageNo);
    }
    if (message.pageSize !== 0) {
      writer.uint32(720).int32(message.pageSize);
    }
    if (message.fetchCountLimit !== 0) {
      writer.uint32(800).int32(message.fetchCountLimit);
    }
    if (message.abtestMode !== "") {
      writer.uint32(1602).string(message.abtestMode);
    }
    if (message.defaultCurrency !== 0) {
      writer.uint32(4808).int32(message.defaultCurrency);
    }
    if (message.requestCurrency !== 0) {
      writer.uint32(4816).int32(message.requestCurrency);
    }
    if (message.responseCurrency !== 0) {
      writer.uint32(4824).int32(message.responseCurrency);
    }
    if (message.random !== false) {
      writer.uint32(4832).bool(message.random);
    }
    for (const v of message.tagIds) {
      writer.uint32(4842).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSearchGoodsParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSearchGoodsParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 5:
          if (tag !== 42) {
            break;
          }

          message.goodsIds.push(reader.string());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.frontendCategoryIds.push(reader.string());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.frontendChildCategoryIds.push(reader.string());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.backendCategoryIds.push(reader.string());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.marketingRuleIds.push(reader.string());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.marketingRuleCodes.push(reader.string());
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.brandIds.push(reader.string());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.keyword = reader.string();
          continue;
        case 45:
          if (tag !== 361) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 60:
          if (tag !== 481) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 61:
          if (tag !== 488) {
            break;
          }

          message.minPublishTime = longToNumber(reader.int64() as Long);
          continue;
        case 62:
          if (tag !== 496) {
            break;
          }

          message.maxPublishTime = longToNumber(reader.int64() as Long);
          continue;
        case 67:
          if (tag !== 536) {
            break;
          }

          message.leMinBuyQuantity = reader.int32();
          continue;
        case 68:
          if (tag !== 544) {
            break;
          }

          message.geMinBuyQuantity = reader.int32();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.sortField = reader.int32();
          continue;
        case 80:
          if (tag !== 640) {
            break;
          }

          message.pageNo = reader.int32();
          continue;
        case 90:
          if (tag !== 720) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        case 100:
          if (tag !== 800) {
            break;
          }

          message.fetchCountLimit = reader.int32();
          continue;
        case 200:
          if (tag !== 1602) {
            break;
          }

          message.abtestMode = reader.string();
          continue;
        case 601:
          if (tag !== 4808) {
            break;
          }

          message.defaultCurrency = reader.int32() as any;
          continue;
        case 602:
          if (tag !== 4816) {
            break;
          }

          message.requestCurrency = reader.int32() as any;
          continue;
        case 603:
          if (tag !== 4824) {
            break;
          }

          message.responseCurrency = reader.int32() as any;
          continue;
        case 604:
          if (tag !== 4832) {
            break;
          }

          message.random = reader.bool();
          continue;
        case 605:
          if (tag !== 4842) {
            break;
          }

          message.tagIds.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSearchGoodsParam {
    return {
      goodsIds: globalThis.Array.isArray(object?.goodsIds) ? object.goodsIds.map((e: any) => globalThis.String(e)) : [],
      frontendCategoryIds: globalThis.Array.isArray(object?.frontendCategoryIds)
        ? object.frontendCategoryIds.map((e: any) => globalThis.String(e))
        : [],
      frontendChildCategoryIds: globalThis.Array.isArray(object?.frontendChildCategoryIds)
        ? object.frontendChildCategoryIds.map((e: any) => globalThis.String(e))
        : [],
      backendCategoryIds: globalThis.Array.isArray(object?.backendCategoryIds)
        ? object.backendCategoryIds.map((e: any) => globalThis.String(e))
        : [],
      marketingRuleIds: globalThis.Array.isArray(object?.marketingRuleIds)
        ? object.marketingRuleIds.map((e: any) => globalThis.String(e))
        : [],
      marketingRuleCodes: globalThis.Array.isArray(object?.marketingRuleCodes)
        ? object.marketingRuleCodes.map((e: any) => globalThis.String(e))
        : [],
      brandIds: globalThis.Array.isArray(object?.brandIds)
        ? object.brandIds.map((e: any) => globalThis.String(e))
        : [],
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      minPublishTime: isSet(object.minPublishTime) ? globalThis.Number(object.minPublishTime) : 0,
      maxPublishTime: isSet(object.maxPublishTime) ? globalThis.Number(object.maxPublishTime) : 0,
      leMinBuyQuantity: isSet(object.leMinBuyQuantity) ? globalThis.Number(object.leMinBuyQuantity) : 0,
      geMinBuyQuantity: isSet(object.geMinBuyQuantity) ? globalThis.Number(object.geMinBuyQuantity) : 0,
      sortField: isSet(object.sortField) ? globalThis.Number(object.sortField) : 0,
      pageNo: isSet(object.pageNo) ? globalThis.Number(object.pageNo) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      fetchCountLimit: isSet(object.fetchCountLimit) ? globalThis.Number(object.fetchCountLimit) : 0,
      abtestMode: isSet(object.abtestMode) ? globalThis.String(object.abtestMode) : "",
      defaultCurrency: isSet(object.defaultCurrency) ? currencyTypeFromJSON(object.defaultCurrency) : 0,
      requestCurrency: isSet(object.requestCurrency) ? currencyTypeFromJSON(object.requestCurrency) : 0,
      responseCurrency: isSet(object.responseCurrency) ? currencyTypeFromJSON(object.responseCurrency) : 0,
      random: isSet(object.random) ? globalThis.Boolean(object.random) : false,
      tagIds: globalThis.Array.isArray(object?.tagIds) ? object.tagIds.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: MidSearchGoodsParam): unknown {
    const obj: any = {};
    if (message.goodsIds?.length) {
      obj.goodsIds = message.goodsIds;
    }
    if (message.frontendCategoryIds?.length) {
      obj.frontendCategoryIds = message.frontendCategoryIds;
    }
    if (message.frontendChildCategoryIds?.length) {
      obj.frontendChildCategoryIds = message.frontendChildCategoryIds;
    }
    if (message.backendCategoryIds?.length) {
      obj.backendCategoryIds = message.backendCategoryIds;
    }
    if (message.marketingRuleIds?.length) {
      obj.marketingRuleIds = message.marketingRuleIds;
    }
    if (message.marketingRuleCodes?.length) {
      obj.marketingRuleCodes = message.marketingRuleCodes;
    }
    if (message.brandIds?.length) {
      obj.brandIds = message.brandIds;
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.minPublishTime !== 0) {
      obj.minPublishTime = Math.round(message.minPublishTime);
    }
    if (message.maxPublishTime !== 0) {
      obj.maxPublishTime = Math.round(message.maxPublishTime);
    }
    if (message.leMinBuyQuantity !== 0) {
      obj.leMinBuyQuantity = Math.round(message.leMinBuyQuantity);
    }
    if (message.geMinBuyQuantity !== 0) {
      obj.geMinBuyQuantity = Math.round(message.geMinBuyQuantity);
    }
    if (message.sortField !== 0) {
      obj.sortField = Math.round(message.sortField);
    }
    if (message.pageNo !== 0) {
      obj.pageNo = Math.round(message.pageNo);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    if (message.fetchCountLimit !== 0) {
      obj.fetchCountLimit = Math.round(message.fetchCountLimit);
    }
    if (message.abtestMode !== "") {
      obj.abtestMode = message.abtestMode;
    }
    if (message.defaultCurrency !== 0) {
      obj.defaultCurrency = currencyTypeToJSON(message.defaultCurrency);
    }
    if (message.requestCurrency !== 0) {
      obj.requestCurrency = currencyTypeToJSON(message.requestCurrency);
    }
    if (message.responseCurrency !== 0) {
      obj.responseCurrency = currencyTypeToJSON(message.responseCurrency);
    }
    if (message.random !== false) {
      obj.random = message.random;
    }
    if (message.tagIds?.length) {
      obj.tagIds = message.tagIds;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSearchGoodsParam>, I>>(base?: I): MidSearchGoodsParam {
    return MidSearchGoodsParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSearchGoodsParam>, I>>(object: I): MidSearchGoodsParam {
    const message = createBaseMidSearchGoodsParam();
    message.goodsIds = object.goodsIds?.map((e) => e) || [];
    message.frontendCategoryIds = object.frontendCategoryIds?.map((e) => e) || [];
    message.frontendChildCategoryIds = object.frontendChildCategoryIds?.map((e) => e) || [];
    message.backendCategoryIds = object.backendCategoryIds?.map((e) => e) || [];
    message.marketingRuleIds = object.marketingRuleIds?.map((e) => e) || [];
    message.marketingRuleCodes = object.marketingRuleCodes?.map((e) => e) || [];
    message.brandIds = object.brandIds?.map((e) => e) || [];
    message.keyword = object.keyword ?? "";
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    message.minPublishTime = object.minPublishTime ?? 0;
    message.maxPublishTime = object.maxPublishTime ?? 0;
    message.leMinBuyQuantity = object.leMinBuyQuantity ?? 0;
    message.geMinBuyQuantity = object.geMinBuyQuantity ?? 0;
    message.sortField = object.sortField ?? 0;
    message.pageNo = object.pageNo ?? 0;
    message.pageSize = object.pageSize ?? 0;
    message.fetchCountLimit = object.fetchCountLimit ?? 0;
    message.abtestMode = object.abtestMode ?? "";
    message.defaultCurrency = object.defaultCurrency ?? 0;
    message.requestCurrency = object.requestCurrency ?? 0;
    message.responseCurrency = object.responseCurrency ?? 0;
    message.random = object.random ?? false;
    message.tagIds = object.tagIds?.map((e) => e) || [];
    return message;
  },
};

function createBaseMidAddGoodsImpressParam(): MidAddGoodsImpressParam {
  return { pageSize: 0, totalHits: 0, goodsImpressPostionMap: {} };
}

export const MidAddGoodsImpressParam = {
  encode(message: MidAddGoodsImpressParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.pageSize !== 0) {
      writer.uint32(80).int32(message.pageSize);
    }
    if (message.totalHits !== 0) {
      writer.uint32(160).int32(message.totalHits);
    }
    Object.entries(message.goodsImpressPostionMap).forEach(([key, value]) => {
      MidAddGoodsImpressParam_GoodsImpressPostionMapEntry.encode({ key: key as any, value }, writer.uint32(242).fork())
        .ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidAddGoodsImpressParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidAddGoodsImpressParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.totalHits = reader.int32();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          const entry30 = MidAddGoodsImpressParam_GoodsImpressPostionMapEntry.decode(reader, reader.uint32());
          if (entry30.value !== undefined) {
            message.goodsImpressPostionMap[entry30.key] = entry30.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidAddGoodsImpressParam {
    return {
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      totalHits: isSet(object.totalHits) ? globalThis.Number(object.totalHits) : 0,
      goodsImpressPostionMap: isObject(object.goodsImpressPostionMap)
        ? Object.entries(object.goodsImpressPostionMap).reduce<{ [key: string]: number }>((acc, [key, value]) => {
          acc[key] = Number(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: MidAddGoodsImpressParam): unknown {
    const obj: any = {};
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    if (message.totalHits !== 0) {
      obj.totalHits = Math.round(message.totalHits);
    }
    if (message.goodsImpressPostionMap) {
      const entries = Object.entries(message.goodsImpressPostionMap);
      if (entries.length > 0) {
        obj.goodsImpressPostionMap = {};
        entries.forEach(([k, v]) => {
          obj.goodsImpressPostionMap[k] = Math.round(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidAddGoodsImpressParam>, I>>(base?: I): MidAddGoodsImpressParam {
    return MidAddGoodsImpressParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidAddGoodsImpressParam>, I>>(object: I): MidAddGoodsImpressParam {
    const message = createBaseMidAddGoodsImpressParam();
    message.pageSize = object.pageSize ?? 0;
    message.totalHits = object.totalHits ?? 0;
    message.goodsImpressPostionMap = Object.entries(object.goodsImpressPostionMap ?? {}).reduce<
      { [key: string]: number }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseMidAddGoodsImpressParam_GoodsImpressPostionMapEntry(): MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
  return { key: "", value: 0 };
}

export const MidAddGoodsImpressParam_GoodsImpressPostionMapEntry = {
  encode(
    message: MidAddGoodsImpressParam_GoodsImpressPostionMapEntry,
    writer: _m0.Writer = _m0.Writer.create(),
  ): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidAddGoodsImpressParam_GoodsImpressPostionMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: MidAddGoodsImpressParam_GoodsImpressPostionMapEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidAddGoodsImpressParam_GoodsImpressPostionMapEntry>, I>>(
    base?: I,
  ): MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
    return MidAddGoodsImpressParam_GoodsImpressPostionMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidAddGoodsImpressParam_GoodsImpressPostionMapEntry>, I>>(
    object: I,
  ): MidAddGoodsImpressParam_GoodsImpressPostionMapEntry {
    const message = createBaseMidAddGoodsImpressParam_GoodsImpressPostionMapEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseMidSetImpressConversionParam(): MidSetImpressConversionParam {
  return { goodsId: "", conversion: 0 };
}

export const MidSetImpressConversionParam = {
  encode(message: MidSetImpressConversionParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(82).string(message.goodsId);
    }
    if (message.conversion !== 0) {
      writer.uint32(160).int32(message.conversion);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidSetImpressConversionParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidSetImpressConversionParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.conversion = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidSetImpressConversionParam {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      conversion: isSet(object.conversion) ? globalThis.Number(object.conversion) : 0,
    };
  },

  toJSON(message: MidSetImpressConversionParam): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.conversion !== 0) {
      obj.conversion = Math.round(message.conversion);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidSetImpressConversionParam>, I>>(base?: I): MidSetImpressConversionParam {
    return MidSetImpressConversionParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidSetImpressConversionParam>, I>>(object: I): MidSetImpressConversionParam {
    const message = createBaseMidSetImpressConversionParam();
    message.goodsId = object.goodsId ?? "";
    message.conversion = object.conversion ?? 0;
    return message;
  },
};

function createBaseRecommendGoodsParam(): RecommendGoodsParam {
  return { goodsCount: 0, deviceType: 0 };
}

export const RecommendGoodsParam = {
  encode(message: RecommendGoodsParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.goodsCount !== 0) {
      writer.uint32(8).int32(message.goodsCount);
    }
    if (message.deviceType !== 0) {
      writer.uint32(16).int32(message.deviceType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RecommendGoodsParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendGoodsParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.goodsCount = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendGoodsParam {
    return {
      goodsCount: isSet(object.goodsCount) ? globalThis.Number(object.goodsCount) : 0,
      deviceType: isSet(object.deviceType) ? visitDeviceTypeFromJSON(object.deviceType) : 0,
    };
  },

  toJSON(message: RecommendGoodsParam): unknown {
    const obj: any = {};
    if (message.goodsCount !== 0) {
      obj.goodsCount = Math.round(message.goodsCount);
    }
    if (message.deviceType !== 0) {
      obj.deviceType = visitDeviceTypeToJSON(message.deviceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendGoodsParam>, I>>(base?: I): RecommendGoodsParam {
    return RecommendGoodsParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendGoodsParam>, I>>(object: I): RecommendGoodsParam {
    const message = createBaseRecommendGoodsParam();
    message.goodsCount = object.goodsCount ?? 0;
    message.deviceType = object.deviceType ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
