<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>
    <div class="px-[0.24rem] py-[0.24rem]">
      <div
        class="text-[0.46rem] font-medium text-[#333] flex items-center mb-[0.32rem]"
      >
        <img loading="lazy"
          src="@/assets/icons/submitSuccess.svg"
          class="mr-[0.16rem] w-[0.82rem]"
        />
        <span>{{ authStore.i18n("cm_common.thankYouRegister") }}</span>
      </div>
      <n-space vertical :style="{ gap: '0.16rem 0' }" class="text-[0.32rem]">
        <div>
          {{ authStore.i18n("cm_common.regSuccessStart") }}
        </div>
        <div>
          {{ authStore.i18n("cm_common.checkQuickGuide") }}
          <a
            href="/h5/article/quick-guide"
            class="text-[#e50113] hover:underline"
            >{{ authStore.i18n("cm_news.quickGuide") }}</a
          >.
        </div>
        <div>
          <span v-if="pageData.couponList?.length">
            {{ authStore.i18n("cm_common.activationReward") }}:
            <span
              v-for="(coupon, index) in pageData.couponList"
              :key="index"
              class="text-[0.3rem] font-medium"
            >
              {{ coupon?.couponName }}
              <span>x{{ coupon?.count }}</span>
              <span
                class="text-[#555]"
                v-if="index < pageData.couponList.length - 1"
                >,
              </span> </span
            >.
          </span>
          <span v-else>{{ authStore.i18n("cm_nota.emailActivate") }}</span>
          {{ authStore.i18n("cm_common.activationEmail") }}
          <span class="text-[0.3rem] font-medium break-all">
            {{ userInfo?.username }} </span
          >,
          {{ authStore.i18n("cm_common.activationValidTime") }}
          <span class="text-[0.3rem] font-medium">
            {{ pageData.expireHour }}
            <span v-if="pageData?.expireHour > 1">{{
              authStore.i18n("cm_common.activationTimeUnits")
            }}</span>
            <span v-if="pageData?.expireHour === 1">{{
              authStore.i18n("cm_common.activationTimeUnit")
            }}</span> </span
          >.
        </div>
        <div>
          <n-button
            color="#E50113"
            text-color="#fff"
            @click="onQueryVerifyMailResult('verifyMail')"
            class="rounded-[0.08rem] w-[2.2rem] h-[0.64rem] text-[0.32rem] mr-[0.14rem] mt-[0.32rem]"
          >
            <div>{{ authStore.i18n("cm_common_emailActivate") }}</div>
          </n-button>
          {{ authStore.i18n("cm_common.or") }}
          <a href="/" class="hover:underline hover:text-[#E50113]">{{
            authStore.i18n("cm_common.skipToMyChilatshop")
          }}</a>
        </div>
      </n-space>
      <div class="border-t-1 mt-[0.4rem]">
        <div class="mt-[0.2rem] mb-[0.24rem] text-[0.36rem] font-medium">
          {{ authStore.i18n("cm_common.verificationEmail") }}
        </div>
        <ul>
          <n-space vertical :style="{ gap: '0.12rem 0' }">
            <li>
              {{ authStore.i18n("cm_common.spamCheck") }}
            </li>
            <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
            <li>
              {{ authStore.i18n("cm_common.verificationDelay") }}

              <span
                class="text-[#636ded] cursor-pointer"
                @click="resendVerification"
              >
                {{ authStore.i18n("cm_common.resendVerification") }}
              </span>
            </li>
          </n-space>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  expireHour: 0,
  couponList: <any>[],
});

onQueryVerifyMailResult();
// 查询邮箱是否已验证, 以及验证后可以得到的优惠券
async function onQueryVerifyMailResult(type?: any) {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "REGISTER",
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (res?.data?.isMailVerified) {
      if (type === "verifyMail") {
        window.location.replace("/h5/user/coupon");
      } else {
        navigateTo("/h5/user/coupon");
      }
    } else if (type === "verifyMail") {
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification() {
  const res: any = await useSendVerifyMail({
    verifyMailScene: "REGISTER",
  });
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/h5/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>
<style scoped lang="scss">
ul {
  list-style-type: disc;
  padding-left: 0.32rem;
}
</style>
