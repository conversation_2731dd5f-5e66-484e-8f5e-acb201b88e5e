syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport";

import "common.proto";
import "mall/passport/model/auth_model.proto";
import "mall/passport/param/auth_param.proto";

// 登录
service Auth {
  // 登录
  rpc login(AuthLoginParam) returns (AuthLoginResp);
  // 登出
  rpc logout(common.EmptyParam) returns (common.ApiResult);
  // 找回密码
  rpc modifyPassword(ModifyPasswordParam) returns (common.ApiResult);
  // 发送验证码
  rpc sendCaptcha(common.StringParam) returns (common.ApiResult);
  // 注册
  rpc register(RegisterParam) returns (common.ApiResult);
}
