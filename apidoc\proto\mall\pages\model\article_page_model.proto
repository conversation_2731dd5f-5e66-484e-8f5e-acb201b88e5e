syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";
import "chilat/support/model/article_category_model.proto";
import "common.proto";


message ListArticleResp {
  common.Result result = 1;
  repeated chilat.support.ArticleModel data = 2;
}

message ArticleResp {
  common.Result result = 1;
  chilat.support.ArticleModel data = 2;
}

//获取BLOG列表的返回
message GetBlogListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated GetBlogListModel data = 3;
}

//获取BLOG列表的Model
message GetBlogListModel {
  string id = 10; //文章ID
  string articleCode = 20; //文章代码
  string title = 30; //标题
  string logo = 40; //logo
  string author = 50; //作者
  string introduce = 60; //简介
  int64 cdate = 70; //创建时间
  int64 udate = 80; //修改时间
}

message SearchArticleListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated ArticleListModel data = 3;
}

message ArticleListModel {
  string id = 10; //文章ID
  string articleCode = 20; //文章代码
  string title = 30; //标题
  string logo = 40; //logo
  string author = 50; //作者
  string introduce = 60; //简介
  int64 cdate = 70; //创建时间
  int64 udate = 80; //修改时间
  repeated chilat.support.ArticleCategoryTreeModel articleCategories = 90; //标签列表,文章二级栏目
}
