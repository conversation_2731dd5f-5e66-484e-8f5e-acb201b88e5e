/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";
import {
  CouponEffectiveTypeStatus,
  couponEffectiveTypeStatusFromJSON,
  couponEffectiveTypeStatusToJSON,
  CouponTypeStatus,
  couponTypeStatusFromJSON,
  couponTypeStatusToJSON,
  CouponUseConditionsTypestatus,
  couponUseConditionsTypestatusFromJSON,
  couponUseConditionsTypestatusToJSON,
  CouponWayStatus,
  couponWayStatusFromJSON,
  couponWayStatusToJSON,
} from "../../coupon/coupon_common";
import { TicketStatus, ticketStatusFromJSON, ticketStatusToJSON } from "../../coupon/coupon_detail_common";
import { CouponInfoUseRuleModel } from "../../coupon/model/coupon_info_model";
import Long from "long";

export const protobufPackage = "chilat.basis";

/** 我的优惠券列表 */
export interface MyCouponDetailModelResp {
  result: Result | undefined;
  page:
    | Page
    | undefined;
  /** 优惠券基本规则参数 */
  data: MyCouponInfoDetailModel[];
}

/** RpcService调用 通用状态返回 */
export interface CouponBasicResp {
  result: Result | undefined;
  data: CouponBasicModel | undefined;
}

/** 我的优惠券详情组装Model */
export interface MyCouponInfoDetailModel {
  /** 优惠券id */
  couponId: string;
  /** 优惠券名称 */
  couponName: string;
  /** 优惠券别名 */
  couponAlias: string;
  /** 用户使用说明 */
  userInstructions: string;
  /** 优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券 */
  couponType: CouponTypeStatus;
  /** 优惠券状态: ticketNotUse 未使用 ticketUse 已使用 ticketLoseEfficacy 已失效 */
  ticketStatus: TicketStatus;
  /** 优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券 */
  couponWay: CouponWayStatus;
  /** 使用条件(0不限制，达到多少金额可以使用，每满xxx可用) */
  useConditionsAmount: number;
  /** 优惠额度 */
  preferentialAmount: number;
  /** 折扣（券类型为折扣券使用） */
  discount: number;
  /** 活动编码 */
  activeId: string;
  /** 有效期类型 （distributionDateEffectiveDays 发放日期+有效天数  distributionDateEffectiveHours 发放日期+有效小时 fixedTime 固定时间） */
  couponEffectiveType: CouponEffectiveTypeStatus;
  /** 优惠券生效日期 */
  ticketStartExpirationDate: number;
  /** 优惠券结束日期 */
  ticketEndExpirationDate: number;
  /** 有效类型为distributionDateEffectiveDays 时取有效天数,有效类型为distributionDateEffectiveHours 时取、有效小时,fixedTime 固定时间 */
  effectiveNum: number;
  /** 使用规则 */
  couponInfoUseRuleModelList: CouponInfoUseRuleModel[];
  /** 订单号 */
  orderNo: string;
  /** 兑换码 */
  exchangeCode: string;
  /** 主键id */
  id: string;
  /** 使用条件类型 */
  couponUseConditionsType: CouponUseConditionsTypestatus;
}

/** 通用返回data */
export interface CouponBasicModel {
  code: number;
  message: string;
}

function createBaseMyCouponDetailModelResp(): MyCouponDetailModelResp {
  return { result: undefined, page: undefined, data: [] };
}

export const MyCouponDetailModelResp = {
  encode(message: MyCouponDetailModelResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      MyCouponInfoDetailModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MyCouponDetailModelResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMyCouponDetailModelResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(MyCouponInfoDetailModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MyCouponDetailModelResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MyCouponInfoDetailModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MyCouponDetailModelResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MyCouponInfoDetailModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MyCouponDetailModelResp>, I>>(base?: I): MyCouponDetailModelResp {
    return MyCouponDetailModelResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyCouponDetailModelResp>, I>>(object: I): MyCouponDetailModelResp {
    const message = createBaseMyCouponDetailModelResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => MyCouponInfoDetailModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCouponBasicResp(): CouponBasicResp {
  return { result: undefined, data: undefined };
}

export const CouponBasicResp = {
  encode(message: CouponBasicResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      CouponBasicModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponBasicResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponBasicResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = CouponBasicModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponBasicResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? CouponBasicModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: CouponBasicResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = CouponBasicModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponBasicResp>, I>>(base?: I): CouponBasicResp {
    return CouponBasicResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponBasicResp>, I>>(object: I): CouponBasicResp {
    const message = createBaseCouponBasicResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? CouponBasicModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseMyCouponInfoDetailModel(): MyCouponInfoDetailModel {
  return {
    couponId: "",
    couponName: "",
    couponAlias: "",
    userInstructions: "",
    couponType: 0,
    ticketStatus: 0,
    couponWay: 0,
    useConditionsAmount: 0,
    preferentialAmount: 0,
    discount: 0,
    activeId: "",
    couponEffectiveType: 0,
    ticketStartExpirationDate: 0,
    ticketEndExpirationDate: 0,
    effectiveNum: 0,
    couponInfoUseRuleModelList: [],
    orderNo: "",
    exchangeCode: "",
    id: "",
    couponUseConditionsType: 0,
  };
}

export const MyCouponInfoDetailModel = {
  encode(message: MyCouponInfoDetailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.couponId !== "") {
      writer.uint32(10).string(message.couponId);
    }
    if (message.couponName !== "") {
      writer.uint32(18).string(message.couponName);
    }
    if (message.couponAlias !== "") {
      writer.uint32(26).string(message.couponAlias);
    }
    if (message.userInstructions !== "") {
      writer.uint32(34).string(message.userInstructions);
    }
    if (message.couponType !== 0) {
      writer.uint32(40).int32(message.couponType);
    }
    if (message.ticketStatus !== 0) {
      writer.uint32(48).int32(message.ticketStatus);
    }
    if (message.couponWay !== 0) {
      writer.uint32(56).int32(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      writer.uint32(65).double(message.useConditionsAmount);
    }
    if (message.preferentialAmount !== 0) {
      writer.uint32(73).double(message.preferentialAmount);
    }
    if (message.discount !== 0) {
      writer.uint32(81).double(message.discount);
    }
    if (message.activeId !== "") {
      writer.uint32(138).string(message.activeId);
    }
    if (message.couponEffectiveType !== 0) {
      writer.uint32(176).int32(message.couponEffectiveType);
    }
    if (message.ticketStartExpirationDate !== 0) {
      writer.uint32(184).int64(message.ticketStartExpirationDate);
    }
    if (message.ticketEndExpirationDate !== 0) {
      writer.uint32(192).int64(message.ticketEndExpirationDate);
    }
    if (message.effectiveNum !== 0) {
      writer.uint32(216).int32(message.effectiveNum);
    }
    for (const v of message.couponInfoUseRuleModelList) {
      CouponInfoUseRuleModel.encode(v!, writer.uint32(234).fork()).ldelim();
    }
    if (message.orderNo !== "") {
      writer.uint32(242).string(message.orderNo);
    }
    if (message.exchangeCode !== "") {
      writer.uint32(250).string(message.exchangeCode);
    }
    if (message.id !== "") {
      writer.uint32(258).string(message.id);
    }
    if (message.couponUseConditionsType !== 0) {
      writer.uint32(264).int32(message.couponUseConditionsType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MyCouponInfoDetailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMyCouponInfoDetailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.couponId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.couponName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.couponAlias = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.userInstructions = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.ticketStatus = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.couponWay = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }

          message.useConditionsAmount = reader.double();
          continue;
        case 9:
          if (tag !== 73) {
            break;
          }

          message.preferentialAmount = reader.double();
          continue;
        case 10:
          if (tag !== 81) {
            break;
          }

          message.discount = reader.double();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.activeId = reader.string();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.couponEffectiveType = reader.int32() as any;
          continue;
        case 23:
          if (tag !== 184) {
            break;
          }

          message.ticketStartExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 24:
          if (tag !== 192) {
            break;
          }

          message.ticketEndExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 27:
          if (tag !== 216) {
            break;
          }

          message.effectiveNum = reader.int32();
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.couponInfoUseRuleModelList.push(CouponInfoUseRuleModel.decode(reader, reader.uint32()));
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 31:
          if (tag !== 250) {
            break;
          }

          message.exchangeCode = reader.string();
          continue;
        case 32:
          if (tag !== 258) {
            break;
          }

          message.id = reader.string();
          continue;
        case 33:
          if (tag !== 264) {
            break;
          }

          message.couponUseConditionsType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MyCouponInfoDetailModel {
    return {
      couponId: isSet(object.couponId) ? globalThis.String(object.couponId) : "",
      couponName: isSet(object.couponName) ? globalThis.String(object.couponName) : "",
      couponAlias: isSet(object.couponAlias) ? globalThis.String(object.couponAlias) : "",
      userInstructions: isSet(object.userInstructions) ? globalThis.String(object.userInstructions) : "",
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
      ticketStatus: isSet(object.ticketStatus) ? ticketStatusFromJSON(object.ticketStatus) : 0,
      couponWay: isSet(object.couponWay) ? couponWayStatusFromJSON(object.couponWay) : 0,
      useConditionsAmount: isSet(object.useConditionsAmount) ? globalThis.Number(object.useConditionsAmount) : 0,
      preferentialAmount: isSet(object.preferentialAmount) ? globalThis.Number(object.preferentialAmount) : 0,
      discount: isSet(object.discount) ? globalThis.Number(object.discount) : 0,
      activeId: isSet(object.activeId) ? globalThis.String(object.activeId) : "",
      couponEffectiveType: isSet(object.couponEffectiveType)
        ? couponEffectiveTypeStatusFromJSON(object.couponEffectiveType)
        : 0,
      ticketStartExpirationDate: isSet(object.ticketStartExpirationDate)
        ? globalThis.Number(object.ticketStartExpirationDate)
        : 0,
      ticketEndExpirationDate: isSet(object.ticketEndExpirationDate)
        ? globalThis.Number(object.ticketEndExpirationDate)
        : 0,
      effectiveNum: isSet(object.effectiveNum) ? globalThis.Number(object.effectiveNum) : 0,
      couponInfoUseRuleModelList: globalThis.Array.isArray(object?.couponInfoUseRuleModelList)
        ? object.couponInfoUseRuleModelList.map((e: any) => CouponInfoUseRuleModel.fromJSON(e))
        : [],
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      exchangeCode: isSet(object.exchangeCode) ? globalThis.String(object.exchangeCode) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      couponUseConditionsType: isSet(object.couponUseConditionsType)
        ? couponUseConditionsTypestatusFromJSON(object.couponUseConditionsType)
        : 0,
    };
  },

  toJSON(message: MyCouponInfoDetailModel): unknown {
    const obj: any = {};
    if (message.couponId !== "") {
      obj.couponId = message.couponId;
    }
    if (message.couponName !== "") {
      obj.couponName = message.couponName;
    }
    if (message.couponAlias !== "") {
      obj.couponAlias = message.couponAlias;
    }
    if (message.userInstructions !== "") {
      obj.userInstructions = message.userInstructions;
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    if (message.ticketStatus !== 0) {
      obj.ticketStatus = ticketStatusToJSON(message.ticketStatus);
    }
    if (message.couponWay !== 0) {
      obj.couponWay = couponWayStatusToJSON(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      obj.useConditionsAmount = message.useConditionsAmount;
    }
    if (message.preferentialAmount !== 0) {
      obj.preferentialAmount = message.preferentialAmount;
    }
    if (message.discount !== 0) {
      obj.discount = message.discount;
    }
    if (message.activeId !== "") {
      obj.activeId = message.activeId;
    }
    if (message.couponEffectiveType !== 0) {
      obj.couponEffectiveType = couponEffectiveTypeStatusToJSON(message.couponEffectiveType);
    }
    if (message.ticketStartExpirationDate !== 0) {
      obj.ticketStartExpirationDate = Math.round(message.ticketStartExpirationDate);
    }
    if (message.ticketEndExpirationDate !== 0) {
      obj.ticketEndExpirationDate = Math.round(message.ticketEndExpirationDate);
    }
    if (message.effectiveNum !== 0) {
      obj.effectiveNum = Math.round(message.effectiveNum);
    }
    if (message.couponInfoUseRuleModelList?.length) {
      obj.couponInfoUseRuleModelList = message.couponInfoUseRuleModelList.map((e) => CouponInfoUseRuleModel.toJSON(e));
    }
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.exchangeCode !== "") {
      obj.exchangeCode = message.exchangeCode;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.couponUseConditionsType !== 0) {
      obj.couponUseConditionsType = couponUseConditionsTypestatusToJSON(message.couponUseConditionsType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MyCouponInfoDetailModel>, I>>(base?: I): MyCouponInfoDetailModel {
    return MyCouponInfoDetailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyCouponInfoDetailModel>, I>>(object: I): MyCouponInfoDetailModel {
    const message = createBaseMyCouponInfoDetailModel();
    message.couponId = object.couponId ?? "";
    message.couponName = object.couponName ?? "";
    message.couponAlias = object.couponAlias ?? "";
    message.userInstructions = object.userInstructions ?? "";
    message.couponType = object.couponType ?? 0;
    message.ticketStatus = object.ticketStatus ?? 0;
    message.couponWay = object.couponWay ?? 0;
    message.useConditionsAmount = object.useConditionsAmount ?? 0;
    message.preferentialAmount = object.preferentialAmount ?? 0;
    message.discount = object.discount ?? 0;
    message.activeId = object.activeId ?? "";
    message.couponEffectiveType = object.couponEffectiveType ?? 0;
    message.ticketStartExpirationDate = object.ticketStartExpirationDate ?? 0;
    message.ticketEndExpirationDate = object.ticketEndExpirationDate ?? 0;
    message.effectiveNum = object.effectiveNum ?? 0;
    message.couponInfoUseRuleModelList =
      object.couponInfoUseRuleModelList?.map((e) => CouponInfoUseRuleModel.fromPartial(e)) || [];
    message.orderNo = object.orderNo ?? "";
    message.exchangeCode = object.exchangeCode ?? "";
    message.id = object.id ?? "";
    message.couponUseConditionsType = object.couponUseConditionsType ?? 0;
    return message;
  },
};

function createBaseCouponBasicModel(): CouponBasicModel {
  return { code: 0, message: "" };
}

export const CouponBasicModel = {
  encode(message: CouponBasicModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponBasicModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponBasicModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponBasicModel {
    return {
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: CouponBasicModel): unknown {
    const obj: any = {};
    if (message.code !== 0) {
      obj.code = Math.round(message.code);
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponBasicModel>, I>>(base?: I): CouponBasicModel {
    return CouponBasicModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponBasicModel>, I>>(object: I): CouponBasicModel {
    const message = createBaseCouponBasicModel();
    message.code = object.code ?? 0;
    message.message = object.message ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
