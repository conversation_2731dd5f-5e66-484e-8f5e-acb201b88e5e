<template>
  <!-- header -->
  <div class="header_banner background_grey-1">
    <div class="container_banner-2 relative">
      <n-grid cols="1 700:2">
        <n-grid-item class="relative flex flex-col justify-center">
          <n-image
            lazy
            preview-disabled
            :src="redLogo"
            class="w-[200px] my-[20px] absolute top-[-70px] fix-logo"
          ></n-image>
          <div class="heading_banner-h1">
            La plataforma de comercio electrónico B2B internacional
          </div>
          <div class="paragraph_banner-p1">
            Selección de productos en línea, nuestro servicio de atención al
            cliente te guiará para completar el proceso de importación.
          </div>
        </n-grid-item>
        <n-grid-item>
          <n-image
            lazy
            preview-disabled
            class="image_body-1"
            :src="headerImg"
          ></n-image>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
  <div class="section_banner !pb-0">
    <div class="container_banner-2">
      <n-grid cols="1 800:2">
        <n-grid-item>
          <div class="bg-content">
            <div class="heading_body-h3">
              Termina el envío del pedido y disfruta el sorprendente precio de
              los productos importados.
            </div>
            <div class="paragraph_banner-p1">
              Usted solo necesita elejir los productos, y nosotros nos
              encargamos de todo el proceso de importación, como la orden, el
              almacenamiento, la inspección, el transporte y la entrega.
            </div>
            <n-button
              color="#C7151C"
              data-spm-index="1"
              data-spm-box="ad-open-gohome"
              @click="onHomePage(1, $event)"
              class="section_banner-button"
            >
              Empieza la prueba gratuita
            </n-button>
          </div>
        </n-grid-item>
        <n-grid-item class="flex justify-center">
          <n-image
            lazy
            preview-disabled
            :src="bannerImg"
            class="image_body-1 shrink-0 w-[70%]"
          ></n-image>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
  <!-- 流程图 -->
  <div class="background_grey-1 section_banner !px-0">
    <div class="text-center container_banner-2">
      <div class="heading_body-h3">Proceso de compra</div>
      <div
        class="flex mx-auto justify-evenly flow_pc_wrapper"
        v-if="pageData.showPC && !isMobile"
      >
        <div class="heading_body_step">
          <div class="heading_body-h4 text-[#e50113]">Paso 1</div>
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_1"
              :src="step1Img"
            ></n-image>
          </div>
          <div class="paragraph_body-p2 !font-medium">Añadir al Carrito</div>
        </div>
        <n-image
          lazy
          preview-disabled
          class="img_icon"
          :src="stepIconImg"
        ></n-image>
        <div class="heading_body_step">
          <div class="heading_body-h4 text-[#e50113]">Paso 2</div>
          <div class="img_wrapper_2">
            <n-image
              lazy
              preview-disabled
              class="img_box_2"
              :src="step2Img"
            ></n-image>
          </div>
          <div class="paragraph_body-p2 !font-medium">Consulta Inmediata</div>
        </div>
        <n-image
          lazy
          preview-disabled
          :src="stepIconImg"
          class="img_icon"
        ></n-image>
        <div class="heading_body_step">
          <div class="heading_body-h4 text-[#e50113]">Paso 3</div>
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_3"
              :src="step3Img"
            ></n-image>
          </div>
          <div class="paragraph_body-p2 !font-medium">Enviar Pedido</div>
        </div>
        <n-image
          lazy
          preview-disabled
          :src="stepIconImg"
          class="img_icon"
        ></n-image>
        <div class="heading_body_step">
          <div class="heading_body-h4 text-[#e50113]">Paso 4</div>
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_4"
              :src="step4Img"
            ></n-image>
          </div>
          <div class="paragraph_body-p2 !font-medium">Coordinar Importación</div>
        </div>
      </div>
      <div class="flow_mobile_wrapper" v-else>
        <div class="heading_body_step step_underline">
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_1"
              :src="step1Img"
            ></n-image>
          </div>
          <div class="flex flex-col justify-center items-start text-left">
            <n-space vertical>
              <div class="heading_body-h4 text-[#e50113]">Paso 1</div>
              <div class="paragraph_body-p2 !font-medium">Añadir al Carrito</div>
            </n-space>
          </div>
        </div>
        <div class="heading_body_step step_underline">
          <div class="img_wrapper_2">
            <n-image
              lazy
              preview-disabled
              class="img_box_2"
              :src="step2Img"
            ></n-image>
          </div>
          <div class="flex flex-col justify-center items-start text-left">
            <n-space vertical>
              <div class="heading_body-h4 text-[#e50113]">Paso 2</div>
              <div class="paragraph_body-p2 !font-medium">Consulta Inmediata</div>
            </n-space>
          </div>
        </div>
        <div class="heading_body_step step_underline">
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_3"
              :src="step3Img"
            ></n-image>
          </div>
          <div class="flex flex-col justify-center items-start text-left">
            <n-space vertical>
              <div class="heading_body-h4 text-[#e50113]">Paso 3</div>
              <div class="paragraph_body-p2 !font-medium">Enviar Pedido</div>
            </n-space>
          </div>
        </div>
        <div class="heading_body_step">
          <div class="img_wrapper">
            <n-image
              lazy
              preview-disabled
              class="img_box_4"
              :src="step4Img"
            ></n-image>
          </div>
          <div class="flex flex-col justify-center items-start text-left">
            <n-space vertical>
              <div class="heading_body-h4 text-[#e50113]">Paso 4</div>
              <div class="paragraph_body-p2 !font-medium">
                Coordinar Importación
              </div>
            </n-space>
          </div>
        </div>
      </div>
      <div class="step_button_wrapper">
        <n-button
          color="#C7151C"
          data-spm-index="2"
          data-spm-box="ad-open-gohome"
          @click="onHomePage(2, $event)"
          class="section_banner-button"
        >
          Empieza la prueba gratuita
        </n-button>
      </div>
    </div>
  </div>

  <div class="section_body-2">
    <div class="container_banner-2">
      <div class="heading_banner-h1">¿Listo para empezar?</div>
      <div class="text-block_body-p1">
        ¡Explore millones de productos de proveedores confiables hoy!
      </div>
      <n-button
        color="#C7151C"
        data-spm-index="3"
        data-spm-box="ad-open-gohome"
        @click="onHomePage(3, $event)"
        class="section_banner-button"
      >
        Empieza la prueba gratuita
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import _ from "lodash";
const redLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/04/6552d857-0770-4091-a929-c66961de1daf.png";
const headerImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/07/29cdca58-d4bf-4123-a556-f5e6495c279d.png";
const bannerImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/06/d96b28b8-6412-478d-88a4-37778c126632.png";
const stepIconImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/30/6ca30da6-9b4b-4f89-85ba-89c6efc8e261.png";
const step1Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/05/04c9375a-96c4-4b2c-ae2e-7b87d3d41f73.png";
const step2Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/05/278f0384-7e27-4db8-8e14-90179422334a.png";
const step3Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/05/62a00caf-9709-4ad1-b6fe-fdc19c96168f.png";
const step4Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/05/f794b787-eaae-4a76-9121-fd7d59905f1e.png";

import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const carouselRef = ref<any>(null);
const isScrollingDown = ref(true);
const pageData = reactive(<any>{
  showPC: true,
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const authStore = useAuthStore();
authStore.setShowAnchor(false);
authStore.setShowCarousel(false);

onBeforeMount(() => {
  document.title = "La plataforma de comercio electrónico B2B internacional";
  function resetFontSize() {
    let a = document.documentElement.clientWidth;
    let b: any;
    if (a <= 750) {
      pageData.showPC = false;
      b = (100 * a) / 750;
    }
    if (a > 750) {
      if (a > 1440) {
        //最大屏1440
        a = 1440;
      }
      pageData.showPC = true;
      b = (100 * a) / 1920;
    }
    document.documentElement.style.fontSize = b + "px";
  }
  resetFontSize();
  window.addEventListener("orientationchange", resetFontSize, false);
  window.addEventListener("resize", resetFontSize, false);
});
onMounted(() => {
  window.addEventListener("scroll", onPageScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", onPageScroll);
});

const onPageScroll = _.throttle(() => {
  var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
  const page: any = Number(
    Number(scrollTop) / Number(window?.innerHeight ?? 1)
  ).toFixed(2);

  // 页面向下滚动M像素，滚动N屏
  window?.MyStat?.addPageEvent(
    "ad01_page_scroll",
    `页面向滚动${scrollTop}像素，滚动至${page * 100}%屏`
  );
}, 500);

function onHomePage(page: number, event: any) {
  window?.MyStat?.addPageEvent(
    "ad01_click_top_button",
    `点击第${page}个按钮`,
    () => {
      navigateToPage("/", {}, false, event);
    }
  );
}
</script>

<style scoped lang="scss">
.bg-content {
  background-image: url("https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/06/c9fd06ab-89b1-448c-bdef-1c58fd3a3b4d.png");
  background-size: fit;
  background-position: 60px -80px;
  background-repeat: no-repeat;
  margin-top: 80px;
}
.section_banner {
  padding: 60px 80px;
}
.header_banner {
  padding: 60px 80px;
}
.background_grey-1 {
  background-color: #f4f4f4;
}

.heading_banner-h1 {
  color: #00142d;
  margin-bottom: 20px;
  font-size: 44px;
  line-height: 60px;
  font-weight: 500;
}
.paragraph_banner-p1 {
  color: #00142d;
  margin-bottom: 40px;
  font-size: 16px;
  line-height: 24px;
}
.heading_body-h3 {
  margin-top: 0;
  margin-bottom: 30px;
  font-size: 34px;
  font-weight: 500;
  line-height: 42px;
}
.heading_body-h4 {
  font-size: 22px;
  font-weight: 500;
  line-height: 30px;
}
.paragraph_body-p2 {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.section_body-1 {
  padding: 40px 80px;
  display: block;
}
.section_body-2 {
  height: auto;
  position: relative;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem 0.6rem;
  text-align: center;
}
.text-block_body-p1 {
  color: #00142d;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.flow_pc_wrapper {
  display: flex;
  .heading_body_step {
    width: 4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
  }
  .img_wrapper {
    width: 2rem;
    height: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #ededed;
    margin-bottom: 0.3rem;
  }
  .img_wrapper_2 {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #ededed;
    margin-bottom: 0.3rem;
    .img_box_2 {
      width: 1.1rem;
      height: 1.2rem;
      margin-left: 0.1rem;
    }
  }
  .img_box_1 {
    width: 1.2rem;
    height: 1rem;
  }
  .img_box_3 {
    width: 1.2rem;
    height: 1.2rem;
    margin-left: 0.2rem;
  }
  .img_box_4 {
    width: 1.2rem;
    height: 1.4rem;
  }

  .img_icon {
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 1.2rem;
  }
}
.flow_mobile_wrapper {
  display: block;
  .heading_body_step {
    display: flex;
    width: 90%;
    margin: 0 auto;
    padding: 0.25rem 0;
  }
  .step_underline {
    border-bottom: 1px dashed #e501133b;
  }
  .img_wrapper {
    width: 64px;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #ededed;
    margin-right: 6px;
    flex-shrink: 0;
    margin-right: 12px;
  }
  .img_wrapper_2 {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #ededed;
    margin-right: 6px;
    flex-shrink: 0;
    .img_box_2 {
      width: 36px;
      height: 34px;
      margin-left: 2px;
    }
  }
  .img_box_1 {
    width: 36px;
    height: 34px;
  }
  .img_box_3 {
    width: 36px;
    height: 34px;
    margin-left: 6px;
  }
  .img_box_4 {
    width: 36px;
    height: 40px;
  }
  .paragraph_body-p2 {
    margin: 0;
  }
}
.container_banner-2 {
  max-width: 1280px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
}
.section_banner-button {
  border-radius: 12px;
  margin: 30px auto;
  box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
}
</style>
<style scoped lang="scss">
@media screen and (max-width: 991px) {
  .section_banner {
    padding: 40px 60px;
  }
  .header_banner {
    padding: 60px 60px 40px 60px;
  }
  .heading_body-h3 {
    margin-bottom: 20px;
    font-size: 32px;
    line-height: 40px;
  }
  .paragraph_body-p2 {
    margin-bottom: 16px;
    font-size: 16px;
    line-height: 22px;
  }
  .section_body-1 {
    padding-left: 60px;
    padding-right: 60px;
  }
}
@media screen and (max-width: 750px) {
  .section_banner {
    padding: 40px 20px;
  }
  .header_banner {
    padding: 60px 20px 40px 20px;
  }
  .heading_banner-h1 {
    font-size: 34px;
    line-height: 42px;
  }
  .paragraph_banner-p1 {
    font-size: 18px;
    line-height: 26px;
  }
  .paragraph_banner-p1 {
    margin-bottom: 20px;
  }
  .section_body-1 {
    padding: 40px 30px;
  }
  .image_body-1 {
    width: 80%;
    margin: 0.5rem auto;
  }
  .bg-content {
    margin-top: 0;
  }
}
@media screen and (max-width: 479px) {
  .section_banner {
    padding: 40px 30px;
  }
  .header_banner {
    padding: 70px 30px 40px 30px;
  }
  .fix-logo {
    top: -70px;
    width: 170px;
  }
  .heading_banner-h1 {
    font-size: 34px;
    line-height: 42px;
  }
  .heading_body-h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 26px;
    line-height: 34px;
  }
  .paragraph_body-p2 {
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 20px;
  }
  .section_body-1 {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .section_banner-button {
    width: 100%;
    padding: 20px 0;
    margin: 0 auto;
    display: flex;
    margin: 20px auto 10px;
  }
  .bg-content {
    margin-top: 0;
    background-position: -50px -88px;
  }
  .step_button_wrapper {
    margin-left: 30px;
    margin-right: 30px;
  }
}

@media screen and (min-width: 1280px) {
  .section_body-1 {
    padding-left: 40px;
    padding-right: 40px;
  }
  .heading_body-h3 {
    font-size: 40px;
    line-height: 54px;
  }
  .paragraph_banner-p1 {
    font-size: 20px;
    line-height: 28px;
  }
  .paragraph_body-p2 {
    font-size: 20px;
    line-height: 28px;
  }
  .section_banner-button {
    font-size: 20px;
    line-height: 28px;
    padding: 24px 30px;
  }
  .text-block_body-p1 {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 20px;
  }
  .heading_body-h4 {
    font-size: 24px;
    line-height: 30px;
  }
}
@media screen and (min-width: 1440px) {
  .section_body-1 {
    padding-left: 100px;
    padding-right: 100px;
    display: block;
  }
  .section_banner {
    padding: 80px 120px;
  }
  .header_banner {
    padding: 80px 120px 80px 120px;
  }
  .heading_body-h3 {
    font-size: 40px;
    line-height: 54px;
  }
  .paragraph_banner-p1 {
    font-size: 24px;
    line-height: 30px;
  }
  .paragraph_body-p2 {
    font-size: 24px;
    line-height: 30px;
  }
  .section_banner-button {
    font-size: 24px;
    line-height: 30px;
    padding: 24px 30px;
  }
  .text-block_body-p1 {
    font-size: 28px;
    line-height: 40px;
    margin-bottom: 20px;
  }
  .heading_body-h4 {
    font-size: 28px;
    line-height: 50px;
  }
}
@media screen and (min-width: 1920px) {
  .section_banner {
    padding-left: 260px;
    padding-right: 260px;
  }
  .header_banner {
    padding-left: 260px;
    padding-right: 260px;
  }
  .container_banner-2 {
    max-width: 1400px;
  }
  .paragraph_banner-p1 {
    font-size: 28px;
    line-height: 40px;
  }
  .heading_body-h4 {
    font-size: 30px;
    line-height: 50px;
  }
  .heading_body-h3 {
    font-size: 44px;
    line-height: 60px;
    margin-top: 20px;
  }
  .text-block_body-p1 {
    font-size: 34px;
    line-height: 44px;
    margin-bottom: 20px;
  }
  .section_banner-button {
    font-size: 28px;
    line-height: 40px;
    padding: 30px 30px;
  }
}
</style>
