syntax = "proto3";
package chilat.coupon;

import "chilat/coupon/coupon_common.proto";
import "common.proto";

option java_package = "com.chilat.rpc.coupon.model";

//卡券列表返回
message CouponInfoPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated CouponInfoListModel data = 3;
}

//优惠券主表返回
message CouponInfoListModel {
  string id = 1;
  string couponName = 2;//优惠券名称
  string couponAlias = 3; //优惠券别名
  string userInstructions = 4; //用户使用说明
  string backendRemark = 5; //后台备注
  coupon.CouponTypeStatus couponType = 6; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
  coupon.CouponWayStatus couponWay = 7; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
  double useConditionsAmount = 8; //使用条件(0不限制，达到多少金额可以使用，每满xxx可用)
  double preferentialAmount = 9; //优惠额度
  double discount = 10; //折扣（券类型为折扣券使用）
  int32 maxNumQuantity = 11; //最大发放数量
  int32 issuedQuantity = 12; //已发放数量
  coupon.CouponDistributionStatus couponDistributionMethod = 13; //优惠券发放方式: couponDistributionActive、主动领取 couponDistributionAutomatic、自动发放 couponDistributionExchange、优惠码兑换
  coupon.CouponStatus couponStatus = 14; //主体卡券的状态: couponDraft 草稿 couponNotActive 失效 couponActive 生效 couponLock 锁定 couponUnLock 解锁 couponExpire 过期 couponCancel 删除
  int64 couponIssueStartDate = 15; //优惠券发放开始日期
  int64 couponIssueEndDate = 16; //优惠券发放结束日期
  string activeId = 17;//活动编码
  int32 receivedQuantity = 20;//已领取数量
  int32 userQuantity = 21;//已使用数量
  int64 cdate = 22;//创建时间
  int64 udate = 23;//更新时间
  CouponUseConditionsTypestatus couponUseConditionsType= 24;//优惠券使用条件类型;
}
