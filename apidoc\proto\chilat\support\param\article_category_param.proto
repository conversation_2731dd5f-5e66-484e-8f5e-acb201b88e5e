syntax = "proto3";
package chilat.support;

option java_package = "com.chilat.rpc.support.param";

import "common.proto";
import "chilat/support/support_common.proto";


message ArticleCategorySaveParam {
  string id = 1;
  string name = 2; //类目名称
  string icon = 3; //类目图标
  int32 idx = 4; //排序
  string parentId = 5; //上级类目
  bool isDefault = 6; //是否默认显示
}

message BatchUpdateIdxParam {
  repeated UpdateIdxParam params = 1;
}

message UpdateIdxParam {
  string id = 1;
  int32 idx = 2; //排序
}

message ArticleSaveParam {
  string id = 1;
  string title = 2; //标题
  string content = 3; //内容
  string logo = 4; //logo
  string author = 5; //作者
  string introduce = 6; //简介
  bool isDefault = 7; //是否默认显示
  int32 idx = 8; //排序
  repeated string articleCategoryIds = 9; //文章分类id
  PageType pageType = 10; //显示在pc上还是h5上
  string articleCode = 11; //文章代码
}

message ArticlePageQueryParam {
  common.PageParam page = 1;
  ArticleQueryParam param = 2;
}

message ArticleQueryParam {
  string title = 1; //文章标题
  string categoryId = 2; //分类id
}
