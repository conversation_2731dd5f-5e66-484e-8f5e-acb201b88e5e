syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics.model";

import "common.proto";
import "chilat/logistics/logistics_common.proto";


message FeeItemLogListResp {
  common.Result result = 1;
  repeated common.LogModel data = 2;
}

message FeeItemResp {
  common.Result result = 1;
  repeated FeeItemModel data = 2;
}

message FeeItemModel {
  string id = 1;
  string parentId = 2;
  string feeName = 3; //费用名称
  string feeAlias = 4; //费用别名
  FeeStage feeStage = 5; //收费阶段
  repeated string routeIds = 6; //适用线路
  int32 idx = 7; //排序
  bool enabled = 8; //是否启用
  repeated FeeItemModel children = 9; //子费用项
}