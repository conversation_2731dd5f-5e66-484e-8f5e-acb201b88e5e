syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

// 库存调整保存信息
message GoodsStockSaveParam {
  repeated GoodsStockSaveItemParam changeList = 10; // 库存调整列表
}

// 库存调整明细
message GoodsStockSaveItemParam {
  string skuId = 10; // 商品SKU ID
  GoodsStockOperateType operateType = 20; // 库存调整类型
  int32 changeQty = 30; // 调整数量（总是正数）
}