syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param.coupon";

import "chilat/coupon/coupon_detail_common.proto";
import "common.proto";

// 优惠券发放
message CouponDistributionParam {
    string couponId = 1;//优惠券ID
    string userId = 2;//用户id
    coupon.TicketSceneStatus ticketSceneStatus = 4;//发放优惠券场景
    string orderNo=7;//订单号（选填）
}

//批量发放优惠券
message CouponDistributionListParam {
    repeated string couponIds = 1;//优惠券参数
    string userId = 2;//用户id
    coupon.TicketSceneStatus ticketSceneStatus = 4;//发放优惠券场景
    string orderNo=7;//订单号（选填）
}




