/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { PageParam } from "../../../common";
import {
  AddressLabel,
  addressLabelFromJSON,
  addressLabelToJSON,
  VerifyMailSceneEnum,
  verifyMailSceneEnumFromJSON,
  verifyMailSceneEnumToJSON,
} from "../../../common/business";
import Long from "long";

export const protobufPackage = "mall.passport";

export interface UpdatePasswordParam {
  oldPassword: string;
  newPassword: string;
}

export interface SaveUserAddressParam {
  id: string;
  /** 国家 */
  countryId: string;
  /** 省 */
  provinceCode: string;
  /** 市 */
  cityCode: string;
  /** 区 */
  regionCode: string;
  /** 详细地址 */
  address: string;
  /** 门牌号 */
  houseNo: string;
  /** 邮编 */
  postcode: string;
  /** 联系人 */
  contactName: string;
  /** 手机号 */
  phone: string;
  /** 是否是默认地址 */
  isDefault: boolean;
  /** 参考地标 */
  referLandmark: string;
  /** 地址标签 */
  addressLabel: AddressLabel;
  street: string;
  /** 省名称 */
  province: string;
  /** 市名称 */
  city: string;
  /** 县名称 */
  region: string;
}

export interface SendVerifyMailParam {
  verifyMailScene: VerifyMailSceneEnum;
}

export interface QueryVerifyMailResultParam {
  email: string;
  verifyMailScene: VerifyMailSceneEnum;
}

export interface InvitedUserMailStatusParam {
  /** 分页信息 */
  page:
    | PageParam
    | undefined;
  /** 是否已验证 */
  isMailVerified: boolean;
  /** 筛选该时间点后注册的用户 */
  datetime: number;
}

export interface queryVerifyMailResultParam {
  email: string;
  isNeedCoupon: boolean;
}

function createBaseUpdatePasswordParam(): UpdatePasswordParam {
  return { oldPassword: "", newPassword: "" };
}

export const UpdatePasswordParam = {
  encode(message: UpdatePasswordParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.oldPassword !== "") {
      writer.uint32(10).string(message.oldPassword);
    }
    if (message.newPassword !== "") {
      writer.uint32(18).string(message.newPassword);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UpdatePasswordParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePasswordParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.oldPassword = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.newPassword = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdatePasswordParam {
    return {
      oldPassword: isSet(object.oldPassword) ? globalThis.String(object.oldPassword) : "",
      newPassword: isSet(object.newPassword) ? globalThis.String(object.newPassword) : "",
    };
  },

  toJSON(message: UpdatePasswordParam): unknown {
    const obj: any = {};
    if (message.oldPassword !== "") {
      obj.oldPassword = message.oldPassword;
    }
    if (message.newPassword !== "") {
      obj.newPassword = message.newPassword;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdatePasswordParam>, I>>(base?: I): UpdatePasswordParam {
    return UpdatePasswordParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePasswordParam>, I>>(object: I): UpdatePasswordParam {
    const message = createBaseUpdatePasswordParam();
    message.oldPassword = object.oldPassword ?? "";
    message.newPassword = object.newPassword ?? "";
    return message;
  },
};

function createBaseSaveUserAddressParam(): SaveUserAddressParam {
  return {
    id: "",
    countryId: "",
    provinceCode: "",
    cityCode: "",
    regionCode: "",
    address: "",
    houseNo: "",
    postcode: "",
    contactName: "",
    phone: "",
    isDefault: false,
    referLandmark: "",
    addressLabel: 0,
    street: "",
    province: "",
    city: "",
    region: "",
  };
}

export const SaveUserAddressParam = {
  encode(message: SaveUserAddressParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.countryId !== "") {
      writer.uint32(18).string(message.countryId);
    }
    if (message.provinceCode !== "") {
      writer.uint32(26).string(message.provinceCode);
    }
    if (message.cityCode !== "") {
      writer.uint32(34).string(message.cityCode);
    }
    if (message.regionCode !== "") {
      writer.uint32(42).string(message.regionCode);
    }
    if (message.address !== "") {
      writer.uint32(50).string(message.address);
    }
    if (message.houseNo !== "") {
      writer.uint32(58).string(message.houseNo);
    }
    if (message.postcode !== "") {
      writer.uint32(66).string(message.postcode);
    }
    if (message.contactName !== "") {
      writer.uint32(74).string(message.contactName);
    }
    if (message.phone !== "") {
      writer.uint32(82).string(message.phone);
    }
    if (message.isDefault !== false) {
      writer.uint32(88).bool(message.isDefault);
    }
    if (message.referLandmark !== "") {
      writer.uint32(98).string(message.referLandmark);
    }
    if (message.addressLabel !== 0) {
      writer.uint32(104).int32(message.addressLabel);
    }
    if (message.street !== "") {
      writer.uint32(114).string(message.street);
    }
    if (message.province !== "") {
      writer.uint32(122).string(message.province);
    }
    if (message.city !== "") {
      writer.uint32(130).string(message.city);
    }
    if (message.region !== "") {
      writer.uint32(138).string(message.region);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SaveUserAddressParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveUserAddressParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.countryId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.provinceCode = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.cityCode = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.regionCode = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.address = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.houseNo = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.postcode = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.contactName = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.phone = reader.string();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.isDefault = reader.bool();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.referLandmark = reader.string();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.addressLabel = reader.int32() as any;
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.street = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.province = reader.string();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.city = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.region = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SaveUserAddressParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      countryId: isSet(object.countryId) ? globalThis.String(object.countryId) : "",
      provinceCode: isSet(object.provinceCode) ? globalThis.String(object.provinceCode) : "",
      cityCode: isSet(object.cityCode) ? globalThis.String(object.cityCode) : "",
      regionCode: isSet(object.regionCode) ? globalThis.String(object.regionCode) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      houseNo: isSet(object.houseNo) ? globalThis.String(object.houseNo) : "",
      postcode: isSet(object.postcode) ? globalThis.String(object.postcode) : "",
      contactName: isSet(object.contactName) ? globalThis.String(object.contactName) : "",
      phone: isSet(object.phone) ? globalThis.String(object.phone) : "",
      isDefault: isSet(object.isDefault) ? globalThis.Boolean(object.isDefault) : false,
      referLandmark: isSet(object.referLandmark) ? globalThis.String(object.referLandmark) : "",
      addressLabel: isSet(object.addressLabel) ? addressLabelFromJSON(object.addressLabel) : 0,
      street: isSet(object.street) ? globalThis.String(object.street) : "",
      province: isSet(object.province) ? globalThis.String(object.province) : "",
      city: isSet(object.city) ? globalThis.String(object.city) : "",
      region: isSet(object.region) ? globalThis.String(object.region) : "",
    };
  },

  toJSON(message: SaveUserAddressParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.countryId !== "") {
      obj.countryId = message.countryId;
    }
    if (message.provinceCode !== "") {
      obj.provinceCode = message.provinceCode;
    }
    if (message.cityCode !== "") {
      obj.cityCode = message.cityCode;
    }
    if (message.regionCode !== "") {
      obj.regionCode = message.regionCode;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.houseNo !== "") {
      obj.houseNo = message.houseNo;
    }
    if (message.postcode !== "") {
      obj.postcode = message.postcode;
    }
    if (message.contactName !== "") {
      obj.contactName = message.contactName;
    }
    if (message.phone !== "") {
      obj.phone = message.phone;
    }
    if (message.isDefault !== false) {
      obj.isDefault = message.isDefault;
    }
    if (message.referLandmark !== "") {
      obj.referLandmark = message.referLandmark;
    }
    if (message.addressLabel !== 0) {
      obj.addressLabel = addressLabelToJSON(message.addressLabel);
    }
    if (message.street !== "") {
      obj.street = message.street;
    }
    if (message.province !== "") {
      obj.province = message.province;
    }
    if (message.city !== "") {
      obj.city = message.city;
    }
    if (message.region !== "") {
      obj.region = message.region;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveUserAddressParam>, I>>(base?: I): SaveUserAddressParam {
    return SaveUserAddressParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveUserAddressParam>, I>>(object: I): SaveUserAddressParam {
    const message = createBaseSaveUserAddressParam();
    message.id = object.id ?? "";
    message.countryId = object.countryId ?? "";
    message.provinceCode = object.provinceCode ?? "";
    message.cityCode = object.cityCode ?? "";
    message.regionCode = object.regionCode ?? "";
    message.address = object.address ?? "";
    message.houseNo = object.houseNo ?? "";
    message.postcode = object.postcode ?? "";
    message.contactName = object.contactName ?? "";
    message.phone = object.phone ?? "";
    message.isDefault = object.isDefault ?? false;
    message.referLandmark = object.referLandmark ?? "";
    message.addressLabel = object.addressLabel ?? 0;
    message.street = object.street ?? "";
    message.province = object.province ?? "";
    message.city = object.city ?? "";
    message.region = object.region ?? "";
    return message;
  },
};

function createBaseSendVerifyMailParam(): SendVerifyMailParam {
  return { verifyMailScene: 0 };
}

export const SendVerifyMailParam = {
  encode(message: SendVerifyMailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.verifyMailScene !== 0) {
      writer.uint32(80).int32(message.verifyMailScene);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SendVerifyMailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendVerifyMailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.verifyMailScene = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendVerifyMailParam {
    return { verifyMailScene: isSet(object.verifyMailScene) ? verifyMailSceneEnumFromJSON(object.verifyMailScene) : 0 };
  },

  toJSON(message: SendVerifyMailParam): unknown {
    const obj: any = {};
    if (message.verifyMailScene !== 0) {
      obj.verifyMailScene = verifyMailSceneEnumToJSON(message.verifyMailScene);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendVerifyMailParam>, I>>(base?: I): SendVerifyMailParam {
    return SendVerifyMailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendVerifyMailParam>, I>>(object: I): SendVerifyMailParam {
    const message = createBaseSendVerifyMailParam();
    message.verifyMailScene = object.verifyMailScene ?? 0;
    return message;
  },
};

function createBaseQueryVerifyMailResultParam(): QueryVerifyMailResultParam {
  return { email: "", verifyMailScene: 0 };
}

export const QueryVerifyMailResultParam = {
  encode(message: QueryVerifyMailResultParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.email !== "") {
      writer.uint32(82).string(message.email);
    }
    if (message.verifyMailScene !== 0) {
      writer.uint32(160).int32(message.verifyMailScene);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryVerifyMailResultParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryVerifyMailResultParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.email = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.verifyMailScene = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryVerifyMailResultParam {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      verifyMailScene: isSet(object.verifyMailScene) ? verifyMailSceneEnumFromJSON(object.verifyMailScene) : 0,
    };
  },

  toJSON(message: QueryVerifyMailResultParam): unknown {
    const obj: any = {};
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.verifyMailScene !== 0) {
      obj.verifyMailScene = verifyMailSceneEnumToJSON(message.verifyMailScene);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryVerifyMailResultParam>, I>>(base?: I): QueryVerifyMailResultParam {
    return QueryVerifyMailResultParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryVerifyMailResultParam>, I>>(object: I): QueryVerifyMailResultParam {
    const message = createBaseQueryVerifyMailResultParam();
    message.email = object.email ?? "";
    message.verifyMailScene = object.verifyMailScene ?? 0;
    return message;
  },
};

function createBaseInvitedUserMailStatusParam(): InvitedUserMailStatusParam {
  return { page: undefined, isMailVerified: false, datetime: 0 };
}

export const InvitedUserMailStatusParam = {
  encode(message: InvitedUserMailStatusParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    if (message.isMailVerified !== false) {
      writer.uint32(160).bool(message.isMailVerified);
    }
    if (message.datetime !== 0) {
      writer.uint32(240).int64(message.datetime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): InvitedUserMailStatusParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvitedUserMailStatusParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.isMailVerified = reader.bool();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.datetime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvitedUserMailStatusParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      isMailVerified: isSet(object.isMailVerified) ? globalThis.Boolean(object.isMailVerified) : false,
      datetime: isSet(object.datetime) ? globalThis.Number(object.datetime) : 0,
    };
  },

  toJSON(message: InvitedUserMailStatusParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.isMailVerified !== false) {
      obj.isMailVerified = message.isMailVerified;
    }
    if (message.datetime !== 0) {
      obj.datetime = Math.round(message.datetime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvitedUserMailStatusParam>, I>>(base?: I): InvitedUserMailStatusParam {
    return InvitedUserMailStatusParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvitedUserMailStatusParam>, I>>(object: I): InvitedUserMailStatusParam {
    const message = createBaseInvitedUserMailStatusParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.isMailVerified = object.isMailVerified ?? false;
    message.datetime = object.datetime ?? 0;
    return message;
  },
};

function createBasequeryVerifyMailResultParam(): queryVerifyMailResultParam {
  return { email: "", isNeedCoupon: false };
}

export const queryVerifyMailResultParam = {
  encode(message: queryVerifyMailResultParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.email !== "") {
      writer.uint32(82).string(message.email);
    }
    if (message.isNeedCoupon !== false) {
      writer.uint32(160).bool(message.isNeedCoupon);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): queryVerifyMailResultParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasequeryVerifyMailResultParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.email = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.isNeedCoupon = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): queryVerifyMailResultParam {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      isNeedCoupon: isSet(object.isNeedCoupon) ? globalThis.Boolean(object.isNeedCoupon) : false,
    };
  },

  toJSON(message: queryVerifyMailResultParam): unknown {
    const obj: any = {};
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.isNeedCoupon !== false) {
      obj.isNeedCoupon = message.isNeedCoupon;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<queryVerifyMailResultParam>, I>>(base?: I): queryVerifyMailResultParam {
    return queryVerifyMailResultParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<queryVerifyMailResultParam>, I>>(object: I): queryVerifyMailResultParam {
    const message = createBasequeryVerifyMailResultParam();
    message.email = object.email ?? "";
    message.isNeedCoupon = object.isNeedCoupon ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
