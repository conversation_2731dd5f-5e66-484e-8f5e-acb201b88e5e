syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";
import "common.proto";

message ListWordPressCategoryResp {
  common.Result result = 1;
  ListWordPressCategoryModel data = 2;
}

message ListWordPressCategoryModel {
  repeated string categoryList = 1;
}

message WordPressDetailResp {
  common.Result result = 1;
  WordPressModel data = 2;
}

message SearchWordPressListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated WordPressModel data = 3;
}

message WordPressModel {
  string id = 1;
  string title = 2;
  string link = 3;
  string category = 4;
  string pubDate = 5;
  string product = 6;
  string cover = 7;
}