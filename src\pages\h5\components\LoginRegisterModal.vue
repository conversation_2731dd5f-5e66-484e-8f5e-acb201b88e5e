<template>
  <n-modal
    preset="dialog"
    class="login-modal-dialog"
    :closable="false"
    :show-icon="false"
    :block-scroll="true"
    :autoFocus="false"
    v-model:show="pageData.showModal"
    :on-close="onCloseGuide"
    :on-esc="onCloseGuide"
    :on-mask-click="onCloseGuide"
    :style="{ width: '6.2rem', padding: 0 }"
  >
    <div>
      <img
        loading="lazy"
        src="@/assets/icons/home/<USER>"
        class="w-[100%] block object-fill"
      />
      <icon-card
        size="26"
        color="#fff"
        class="modal-icon"
        name="iconamoon:close-light"
        @click="onCloseGuide"
        v-show="pageData.showModal"
      ></icon-card>
      <div class="modal-content" data-spm-box="unlogin-dialog">
        <n-button
          color="#fff"
          class="modal-button"
          @click="onLoginClick(0, $event)"
        >
          {{ authStore.i18n("cm_common.createAccount") }}
        </n-button>
        <div class="mt-[0.2rem] text-[0.3rem] leading-[0.3rem] text-center">
          {{ authStore.i18n("cm_common.haveAccount") }}
          <span
            class="text-[0.34rem] leading-[0.34rem] underline underline-offset-2 cursor-pointer"
            @click="onLoginClick(1, $event)"
            >{{ authStore.i18n("cm_common.loginAccount") }}</span
          >
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts" name="GuideModal">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const pageData = reactive({
  showModal: false,
});

onMounted(() => {
  setTimeout(() => {
    if (!window?.MyStat.getSessionValue("isClickLoginModal")) {
      onOpenGuide();
    }
  }, 30000);
});

function onOpenGuide() {
  pageData.showModal = true;
  window?.MyStat?.setSessionValue("isClickLoginModal", "true");
  window?.MyStat?.addPageEvent("unlogin_dialog_open", "未登录弹窗打开"); // 埋点
}
function onCloseGuide() {
  pageData.showModal = false;
  window?.MyStat?.addPageEvent("unlogin_dialog_close", "未登录弹窗关闭"); // 埋点
}

function onLoginClick(type?: any, event?: any) {
  if (type === 1) {
    window?.MyStat?.addPageEvent("unlogin_dialog_go_login", "未登录弹窗去登录"); // 埋点
    navigateToPage("/h5/user/login", { pageSource: "/h5" }, false, event);
  } else {
    window?.MyStat?.addPageEvent(
      "unlogin_dialog_go_register",
      "未登录弹窗去注册"
    ); // 埋点
    navigateToPage("/h5/user/register", { pageSource: "/h5" }, false, event);
  }
}

defineExpose({
  onOpenGuide,
});
</script>
<style scoped lang="scss">
.modal-icon {
  position: absolute;
  z-index: 1000;
  right: 0.16rem;
  top: 0.16rem;
  cursor: pointer;
}
.modal-content {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0.64rem;
  z-index: 1000;
  color: #fff;
  .modal-button {
    height: 0.72rem;
    font-size: 0.32rem;
    line-height: 0.32rem;
    padding: 0.2rem 1.84rem;
    border-radius: 2rem;
    color: #e50113;
    font-weight: 500;
  }
}
</style>
<style>
.login-modal-dialog.n-dialog {
  background: none !important;
}
.login-modal-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0 !important;
}
</style>
