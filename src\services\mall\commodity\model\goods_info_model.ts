/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { GoodsAttrModel } from "../../../chilat/commodity/commodity_common";
import { Result } from "../../../common";
import { CurrencyType, currencyTypeFromJSON, currencyTypeToJSON, SkuStepPrice } from "../../../common/business";

export const protobufPackage = "mall.commodity";

export interface GoodsInfoDetailResp {
  result: Result | undefined;
  data: GoodsInfoModel | undefined;
}

/** 商品信息 */
export interface GoodsInfoModel {
  /** 商品ID */
  id: string;
  /** 商品编号 */
  goodsNo: string;
  /** 商品名称 */
  goodsName: string;
  /** 商品标题 */
  goodsTitle: string;
  /** 是否已上架（商品预览功能可包含未上架商品） */
  isOnline: boolean;
  /** 价格单位 */
  goodsPriceUnitName: string;
  /** 价格对应的货币 */
  currency: CurrencyType;
  /** 最低价 */
  minPrice: number;
  /** 最高价（若最低价与最高价相同，则在列表显示一个价格） */
  maxPrice: number;
  /** 最小购买数量（起订量） */
  minBuyQuantity: number;
  /** 最小加购数量（一次加购数量） */
  minIncreaseQuantity: number;
  /** 视频封面图片（若非空，则在商详页优先显示视频） */
  coverImage: string;
  /** 视频 */
  videoUrl: string;
  /** 商品图片组-附属属性 */
  goodsImageList: string[];
  /** 商品长（单位：cm） */
  goodsLength: number;
  /** 商品宽（单位：cm） */
  goodsWidth: number;
  /** 商品高（单位：cm） */
  goodsHeight: number;
  /** 商品重量-附属属性 */
  goodsWeight: number;
  /** 商品描述（根据访问设备参数，自动取值PC或H5版的描述） */
  goodsDesc: string;
  /** 商品阶梯价范围（上架商品至少存在一个阶梯价范围） */
  goodsPriceRanges: GoodsPriceRange[];
  /** 属性参数-附属属性 */
  attrList: GoodsAttrModel[];
  /** 商品规格列表（必填） */
  specList: GoodsSkuSpecModel[];
  /** SKU列表（必填） */
  skuList: GoodsSkuInfoModel[];
}

/** 商品阶梯价范围（所有字段必填） */
export interface GoodsPriceRange {
  /** 阶梯价起始值（从 minBuyQuantity 开始） */
  start: number;
  /** 阶梯价结束值（-1表示不限） */
  end: number;
  /** 最低价 */
  minPrice: number;
  /** 最高价（最低价与最高价相同时，前段仅显示一个价格） */
  maxPrice: number;
}

/** 商品SKU规格 */
export interface GoodsSkuSpecModel {
  /** 规格ID（必填） */
  id: string;
  /** 规格名称（必填） */
  name: string;
  /** 规格明细（必填） */
  items: GoodsSkuSpecItemModel[];
}

/** 商品SKU规格明细（含库存数量） */
export interface GoodsSkuSpecItemModel {
  /** 规格明细ID（必填） */
  itemId: string;
  /** 规格明细名称（如颜色规格中的“红色”，必填） */
  itemName: string;
  /** 相关SKU总库存数量（必填） */
  stockQty: number;
  /** 图片链接（例：SKU图片） */
  imageUrl: string;
  /** 颜色值（格式：# + 6位16进制数，例：#CCCCCC） */
  color: string;
  /** 相关SKU在购物车中的总数量（大于0传值，仅在第1个规格项传值） */
  cartQty: number;
}

/** 商品SKU信息 */
export interface GoodsSkuInfoModel {
  /** SKU ID（必填） */
  id: string;
  /** SKU商品料号（必填） */
  skuNo: string;
  /** 库存数量（必填） */
  stockQty: number;
  /** SKU规格ID组：规格ID，规格明细ID */
  specItems: GoodsSkuSpecIdPairModel[];
  /** 阶梯价（价格单位在商品级别描述；“end=-1”表示不限） */
  stepPrices: SkuStepPrice[];
  /** 购物车中的数量（必填，默认0） */
  cartQty: number;
}

/** 商品SKU中的规格ID与规格明细ID组合（通过检索SKU与反查有SKU规格） */
export interface GoodsSkuSpecIdPairModel {
  /** 规格明细ID（当前商品内唯一） */
  itemId: string;
  /** 规格项名称 */
  itemName: string;
  /** 规格ID */
  specId: string;
}

export interface GoodsListDataItemModel {
  /** 商品ID */
  goodsId: string;
  /** 商品编码 */
  goodsNo: string;
  /** 商品名称 */
  goodsName: string;
  /** 商品标题 */
  goodsTitle: string;
  /** 价格单位 */
  goodsPriceUnitName: string;
  /** 价格对应的货币 */
  currency: CurrencyType;
  /** 最低价 */
  minPrice: number;
  /** 最高价（若最低价与最高价相同，则在列表显示一个价格） */
  maxPrice: number;
  /** 最小购买数量（起订量） */
  minBuyQuantity: number;
  /** 最小加购数量（一次加购数量） */
  minIncreaseQuantity: number;
  /** 商品主图 */
  mainImageUrl: string;
  /** 商品权重 */
  goodsRank: number;
  /** 搜索相关度得分 */
  hitScore: number;
  /** 源商品ID */
  sourceGoodsId: string;
}

export interface SkuModel {
  /** skuId */
  skuId: string;
  /** goodsId */
  goodsId: string;
  /** skuNo */
  skuNo: string;
  /** skuNo */
  goodsNo: string;
  /** 图片url */
  picUrl: string;
  /** 商品名称 */
  goodsName: string;
  /** 规格 */
  specList: SpecModel[];
  /** 数量 */
  count: number;
  /** 单位 */
  goodsPriceUnitName: string;
  /** 单价 */
  unitPrice: number;
  /** 币种 */
  currency: string;
  /** 总价 */
  totalAmount: number;
}

export interface SpecModel {
  /** 规格名称 如color */
  specName: string;
  /** 具体规则明细 如red */
  itemName: string;
}

function createBaseGoodsInfoDetailResp(): GoodsInfoDetailResp {
  return { result: undefined, data: undefined };
}

export const GoodsInfoDetailResp = {
  encode(message: GoodsInfoDetailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GoodsInfoModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsInfoDetailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsInfoDetailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GoodsInfoModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsInfoDetailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GoodsInfoModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GoodsInfoDetailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GoodsInfoModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsInfoDetailResp>, I>>(base?: I): GoodsInfoDetailResp {
    return GoodsInfoDetailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoDetailResp>, I>>(object: I): GoodsInfoDetailResp {
    const message = createBaseGoodsInfoDetailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GoodsInfoModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGoodsInfoModel(): GoodsInfoModel {
  return {
    id: "",
    goodsNo: "",
    goodsName: "",
    goodsTitle: "",
    isOnline: false,
    goodsPriceUnitName: "",
    currency: 0,
    minPrice: 0,
    maxPrice: 0,
    minBuyQuantity: 0,
    minIncreaseQuantity: 0,
    coverImage: "",
    videoUrl: "",
    goodsImageList: [],
    goodsLength: 0,
    goodsWidth: 0,
    goodsHeight: 0,
    goodsWeight: 0,
    goodsDesc: "",
    goodsPriceRanges: [],
    attrList: [],
    specList: [],
    skuList: [],
  };
}

export const GoodsInfoModel = {
  encode(message: GoodsInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.goodsNo !== "") {
      writer.uint32(162).string(message.goodsNo);
    }
    if (message.goodsName !== "") {
      writer.uint32(242).string(message.goodsName);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(322).string(message.goodsTitle);
    }
    if (message.isOnline !== false) {
      writer.uint32(400).bool(message.isOnline);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(482).string(message.goodsPriceUnitName);
    }
    if (message.currency !== 0) {
      writer.uint32(560).int32(message.currency);
    }
    if (message.minPrice !== 0) {
      writer.uint32(641).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(721).double(message.maxPrice);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(800).int32(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      writer.uint32(880).int32(message.minIncreaseQuantity);
    }
    if (message.coverImage !== "") {
      writer.uint32(962).string(message.coverImage);
    }
    if (message.videoUrl !== "") {
      writer.uint32(1042).string(message.videoUrl);
    }
    for (const v of message.goodsImageList) {
      writer.uint32(1122).string(v!);
    }
    if (message.goodsLength !== 0) {
      writer.uint32(1201).double(message.goodsLength);
    }
    if (message.goodsWidth !== 0) {
      writer.uint32(1281).double(message.goodsWidth);
    }
    if (message.goodsHeight !== 0) {
      writer.uint32(1361).double(message.goodsHeight);
    }
    if (message.goodsWeight !== 0) {
      writer.uint32(1441).double(message.goodsWeight);
    }
    if (message.goodsDesc !== "") {
      writer.uint32(1522).string(message.goodsDesc);
    }
    for (const v of message.goodsPriceRanges) {
      GoodsPriceRange.encode(v!, writer.uint32(1562).fork()).ldelim();
    }
    for (const v of message.attrList) {
      GoodsAttrModel.encode(v!, writer.uint32(1602).fork()).ldelim();
    }
    for (const v of message.specList) {
      GoodsSkuSpecModel.encode(v!, writer.uint32(1682).fork()).ldelim();
    }
    for (const v of message.skuList) {
      GoodsSkuInfoModel.encode(v!, writer.uint32(1762).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.isOnline = reader.bool();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.currency = reader.int32() as any;
          continue;
        case 80:
          if (tag !== 641) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 90:
          if (tag !== 721) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 100:
          if (tag !== 800) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 110:
          if (tag !== 880) {
            break;
          }

          message.minIncreaseQuantity = reader.int32();
          continue;
        case 120:
          if (tag !== 962) {
            break;
          }

          message.coverImage = reader.string();
          continue;
        case 130:
          if (tag !== 1042) {
            break;
          }

          message.videoUrl = reader.string();
          continue;
        case 140:
          if (tag !== 1122) {
            break;
          }

          message.goodsImageList.push(reader.string());
          continue;
        case 150:
          if (tag !== 1201) {
            break;
          }

          message.goodsLength = reader.double();
          continue;
        case 160:
          if (tag !== 1281) {
            break;
          }

          message.goodsWidth = reader.double();
          continue;
        case 170:
          if (tag !== 1361) {
            break;
          }

          message.goodsHeight = reader.double();
          continue;
        case 180:
          if (tag !== 1441) {
            break;
          }

          message.goodsWeight = reader.double();
          continue;
        case 190:
          if (tag !== 1522) {
            break;
          }

          message.goodsDesc = reader.string();
          continue;
        case 195:
          if (tag !== 1562) {
            break;
          }

          message.goodsPriceRanges.push(GoodsPriceRange.decode(reader, reader.uint32()));
          continue;
        case 200:
          if (tag !== 1602) {
            break;
          }

          message.attrList.push(GoodsAttrModel.decode(reader, reader.uint32()));
          continue;
        case 210:
          if (tag !== 1682) {
            break;
          }

          message.specList.push(GoodsSkuSpecModel.decode(reader, reader.uint32()));
          continue;
        case 220:
          if (tag !== 1762) {
            break;
          }

          message.skuList.push(GoodsSkuInfoModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsInfoModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      goodsName: isSet(object.goodsName) ? globalThis.String(object.goodsName) : "",
      goodsTitle: isSet(object.goodsTitle) ? globalThis.String(object.goodsTitle) : "",
      isOnline: isSet(object.isOnline) ? globalThis.Boolean(object.isOnline) : false,
      goodsPriceUnitName: isSet(object.goodsPriceUnitName) ? globalThis.String(object.goodsPriceUnitName) : "",
      currency: isSet(object.currency) ? currencyTypeFromJSON(object.currency) : 0,
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      minBuyQuantity: isSet(object.minBuyQuantity) ? globalThis.Number(object.minBuyQuantity) : 0,
      minIncreaseQuantity: isSet(object.minIncreaseQuantity) ? globalThis.Number(object.minIncreaseQuantity) : 0,
      coverImage: isSet(object.coverImage) ? globalThis.String(object.coverImage) : "",
      videoUrl: isSet(object.videoUrl) ? globalThis.String(object.videoUrl) : "",
      goodsImageList: globalThis.Array.isArray(object?.goodsImageList)
        ? object.goodsImageList.map((e: any) => globalThis.String(e))
        : [],
      goodsLength: isSet(object.goodsLength) ? globalThis.Number(object.goodsLength) : 0,
      goodsWidth: isSet(object.goodsWidth) ? globalThis.Number(object.goodsWidth) : 0,
      goodsHeight: isSet(object.goodsHeight) ? globalThis.Number(object.goodsHeight) : 0,
      goodsWeight: isSet(object.goodsWeight) ? globalThis.Number(object.goodsWeight) : 0,
      goodsDesc: isSet(object.goodsDesc) ? globalThis.String(object.goodsDesc) : "",
      goodsPriceRanges: globalThis.Array.isArray(object?.goodsPriceRanges)
        ? object.goodsPriceRanges.map((e: any) => GoodsPriceRange.fromJSON(e))
        : [],
      attrList: globalThis.Array.isArray(object?.attrList)
        ? object.attrList.map((e: any) => GoodsAttrModel.fromJSON(e))
        : [],
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => GoodsSkuSpecModel.fromJSON(e))
        : [],
      skuList: globalThis.Array.isArray(object?.skuList)
        ? object.skuList.map((e: any) => GoodsSkuInfoModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsInfoModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.isOnline !== false) {
      obj.isOnline = message.isOnline;
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.currency !== 0) {
      obj.currency = currencyTypeToJSON(message.currency);
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      obj.minIncreaseQuantity = Math.round(message.minIncreaseQuantity);
    }
    if (message.coverImage !== "") {
      obj.coverImage = message.coverImage;
    }
    if (message.videoUrl !== "") {
      obj.videoUrl = message.videoUrl;
    }
    if (message.goodsImageList?.length) {
      obj.goodsImageList = message.goodsImageList;
    }
    if (message.goodsLength !== 0) {
      obj.goodsLength = message.goodsLength;
    }
    if (message.goodsWidth !== 0) {
      obj.goodsWidth = message.goodsWidth;
    }
    if (message.goodsHeight !== 0) {
      obj.goodsHeight = message.goodsHeight;
    }
    if (message.goodsWeight !== 0) {
      obj.goodsWeight = message.goodsWeight;
    }
    if (message.goodsDesc !== "") {
      obj.goodsDesc = message.goodsDesc;
    }
    if (message.goodsPriceRanges?.length) {
      obj.goodsPriceRanges = message.goodsPriceRanges.map((e) => GoodsPriceRange.toJSON(e));
    }
    if (message.attrList?.length) {
      obj.attrList = message.attrList.map((e) => GoodsAttrModel.toJSON(e));
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) => GoodsSkuSpecModel.toJSON(e));
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => GoodsSkuInfoModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsInfoModel>, I>>(base?: I): GoodsInfoModel {
    return GoodsInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoModel>, I>>(object: I): GoodsInfoModel {
    const message = createBaseGoodsInfoModel();
    message.id = object.id ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.goodsName = object.goodsName ?? "";
    message.goodsTitle = object.goodsTitle ?? "";
    message.isOnline = object.isOnline ?? false;
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.currency = object.currency ?? 0;
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.minIncreaseQuantity = object.minIncreaseQuantity ?? 0;
    message.coverImage = object.coverImage ?? "";
    message.videoUrl = object.videoUrl ?? "";
    message.goodsImageList = object.goodsImageList?.map((e) => e) || [];
    message.goodsLength = object.goodsLength ?? 0;
    message.goodsWidth = object.goodsWidth ?? 0;
    message.goodsHeight = object.goodsHeight ?? 0;
    message.goodsWeight = object.goodsWeight ?? 0;
    message.goodsDesc = object.goodsDesc ?? "";
    message.goodsPriceRanges = object.goodsPriceRanges?.map((e) => GoodsPriceRange.fromPartial(e)) || [];
    message.attrList = object.attrList?.map((e) => GoodsAttrModel.fromPartial(e)) || [];
    message.specList = object.specList?.map((e) => GoodsSkuSpecModel.fromPartial(e)) || [];
    message.skuList = object.skuList?.map((e) => GoodsSkuInfoModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoodsPriceRange(): GoodsPriceRange {
  return { start: 0, end: 0, minPrice: 0, maxPrice: 0 };
}

export const GoodsPriceRange = {
  encode(message: GoodsPriceRange, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.start !== 0) {
      writer.uint32(80).int32(message.start);
    }
    if (message.end !== 0) {
      writer.uint32(160).int32(message.end);
    }
    if (message.minPrice !== 0) {
      writer.uint32(241).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(321).double(message.maxPrice);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsPriceRange {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsPriceRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.start = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.end = reader.int32();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 40:
          if (tag !== 321) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsPriceRange {
    return {
      start: isSet(object.start) ? globalThis.Number(object.start) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0,
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
    };
  },

  toJSON(message: GoodsPriceRange): unknown {
    const obj: any = {};
    if (message.start !== 0) {
      obj.start = Math.round(message.start);
    }
    if (message.end !== 0) {
      obj.end = Math.round(message.end);
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsPriceRange>, I>>(base?: I): GoodsPriceRange {
    return GoodsPriceRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsPriceRange>, I>>(object: I): GoodsPriceRange {
    const message = createBaseGoodsPriceRange();
    message.start = object.start ?? 0;
    message.end = object.end ?? 0;
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    return message;
  },
};

function createBaseGoodsSkuSpecModel(): GoodsSkuSpecModel {
  return { id: "", name: "", items: [] };
}

export const GoodsSkuSpecModel = {
  encode(message: GoodsSkuSpecModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    for (const v of message.items) {
      GoodsSkuSpecItemModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsSkuSpecModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsSkuSpecModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.items.push(GoodsSkuSpecItemModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsSkuSpecModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => GoodsSkuSpecItemModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsSkuSpecModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.items?.length) {
      obj.items = message.items.map((e) => GoodsSkuSpecItemModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsSkuSpecModel>, I>>(base?: I): GoodsSkuSpecModel {
    return GoodsSkuSpecModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsSkuSpecModel>, I>>(object: I): GoodsSkuSpecModel {
    const message = createBaseGoodsSkuSpecModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.items = object.items?.map((e) => GoodsSkuSpecItemModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoodsSkuSpecItemModel(): GoodsSkuSpecItemModel {
  return { itemId: "", itemName: "", stockQty: 0, imageUrl: "", color: "", cartQty: 0 };
}

export const GoodsSkuSpecItemModel = {
  encode(message: GoodsSkuSpecItemModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.itemId !== "") {
      writer.uint32(82).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(162).string(message.itemName);
    }
    if (message.stockQty !== 0) {
      writer.uint32(240).int32(message.stockQty);
    }
    if (message.imageUrl !== "") {
      writer.uint32(322).string(message.imageUrl);
    }
    if (message.color !== "") {
      writer.uint32(402).string(message.color);
    }
    if (message.cartQty !== 0) {
      writer.uint32(480).int32(message.cartQty);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsSkuSpecItemModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsSkuSpecItemModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.itemName = reader.string();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.stockQty = reader.int32();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.color = reader.string();
          continue;
        case 60:
          if (tag !== 480) {
            break;
          }

          message.cartQty = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsSkuSpecItemModel {
    return {
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName) ? globalThis.String(object.itemName) : "",
      stockQty: isSet(object.stockQty) ? globalThis.Number(object.stockQty) : 0,
      imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      cartQty: isSet(object.cartQty) ? globalThis.Number(object.cartQty) : 0,
    };
  },

  toJSON(message: GoodsSkuSpecItemModel): unknown {
    const obj: any = {};
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    if (message.stockQty !== 0) {
      obj.stockQty = Math.round(message.stockQty);
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.cartQty !== 0) {
      obj.cartQty = Math.round(message.cartQty);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsSkuSpecItemModel>, I>>(base?: I): GoodsSkuSpecItemModel {
    return GoodsSkuSpecItemModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsSkuSpecItemModel>, I>>(object: I): GoodsSkuSpecItemModel {
    const message = createBaseGoodsSkuSpecItemModel();
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    message.stockQty = object.stockQty ?? 0;
    message.imageUrl = object.imageUrl ?? "";
    message.color = object.color ?? "";
    message.cartQty = object.cartQty ?? 0;
    return message;
  },
};

function createBaseGoodsSkuInfoModel(): GoodsSkuInfoModel {
  return { id: "", skuNo: "", stockQty: 0, specItems: [], stepPrices: [], cartQty: 0 };
}

export const GoodsSkuInfoModel = {
  encode(message: GoodsSkuInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.skuNo !== "") {
      writer.uint32(162).string(message.skuNo);
    }
    if (message.stockQty !== 0) {
      writer.uint32(240).int32(message.stockQty);
    }
    for (const v of message.specItems) {
      GoodsSkuSpecIdPairModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    for (const v of message.stepPrices) {
      SkuStepPrice.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    if (message.cartQty !== 0) {
      writer.uint32(480).int32(message.cartQty);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsSkuInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsSkuInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.stockQty = reader.int32();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.specItems.push(GoodsSkuSpecIdPairModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.stepPrices.push(SkuStepPrice.decode(reader, reader.uint32()));
          continue;
        case 60:
          if (tag !== 480) {
            break;
          }

          message.cartQty = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsSkuInfoModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      stockQty: isSet(object.stockQty) ? globalThis.Number(object.stockQty) : 0,
      specItems: globalThis.Array.isArray(object?.specItems)
        ? object.specItems.map((e: any) => GoodsSkuSpecIdPairModel.fromJSON(e))
        : [],
      stepPrices: globalThis.Array.isArray(object?.stepPrices)
        ? object.stepPrices.map((e: any) => SkuStepPrice.fromJSON(e))
        : [],
      cartQty: isSet(object.cartQty) ? globalThis.Number(object.cartQty) : 0,
    };
  },

  toJSON(message: GoodsSkuInfoModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.stockQty !== 0) {
      obj.stockQty = Math.round(message.stockQty);
    }
    if (message.specItems?.length) {
      obj.specItems = message.specItems.map((e) => GoodsSkuSpecIdPairModel.toJSON(e));
    }
    if (message.stepPrices?.length) {
      obj.stepPrices = message.stepPrices.map((e) => SkuStepPrice.toJSON(e));
    }
    if (message.cartQty !== 0) {
      obj.cartQty = Math.round(message.cartQty);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsSkuInfoModel>, I>>(base?: I): GoodsSkuInfoModel {
    return GoodsSkuInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsSkuInfoModel>, I>>(object: I): GoodsSkuInfoModel {
    const message = createBaseGoodsSkuInfoModel();
    message.id = object.id ?? "";
    message.skuNo = object.skuNo ?? "";
    message.stockQty = object.stockQty ?? 0;
    message.specItems = object.specItems?.map((e) => GoodsSkuSpecIdPairModel.fromPartial(e)) || [];
    message.stepPrices = object.stepPrices?.map((e) => SkuStepPrice.fromPartial(e)) || [];
    message.cartQty = object.cartQty ?? 0;
    return message;
  },
};

function createBaseGoodsSkuSpecIdPairModel(): GoodsSkuSpecIdPairModel {
  return { itemId: "", itemName: "", specId: "" };
}

export const GoodsSkuSpecIdPairModel = {
  encode(message: GoodsSkuSpecIdPairModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.itemId !== "") {
      writer.uint32(82).string(message.itemId);
    }
    if (message.itemName !== "") {
      writer.uint32(90).string(message.itemName);
    }
    if (message.specId !== "") {
      writer.uint32(162).string(message.specId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsSkuSpecIdPairModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsSkuSpecIdPairModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.itemId = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.itemName = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.specId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsSkuSpecIdPairModel {
    return {
      itemId: isSet(object.itemId) ? globalThis.String(object.itemId) : "",
      itemName: isSet(object.itemName) ? globalThis.String(object.itemName) : "",
      specId: isSet(object.specId) ? globalThis.String(object.specId) : "",
    };
  },

  toJSON(message: GoodsSkuSpecIdPairModel): unknown {
    const obj: any = {};
    if (message.itemId !== "") {
      obj.itemId = message.itemId;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    if (message.specId !== "") {
      obj.specId = message.specId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsSkuSpecIdPairModel>, I>>(base?: I): GoodsSkuSpecIdPairModel {
    return GoodsSkuSpecIdPairModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsSkuSpecIdPairModel>, I>>(object: I): GoodsSkuSpecIdPairModel {
    const message = createBaseGoodsSkuSpecIdPairModel();
    message.itemId = object.itemId ?? "";
    message.itemName = object.itemName ?? "";
    message.specId = object.specId ?? "";
    return message;
  },
};

function createBaseGoodsListDataItemModel(): GoodsListDataItemModel {
  return {
    goodsId: "",
    goodsNo: "",
    goodsName: "",
    goodsTitle: "",
    goodsPriceUnitName: "",
    currency: 0,
    minPrice: 0,
    maxPrice: 0,
    minBuyQuantity: 0,
    minIncreaseQuantity: 0,
    mainImageUrl: "",
    goodsRank: 0,
    hitScore: 0,
    sourceGoodsId: "",
  };
}

export const GoodsListDataItemModel = {
  encode(message: GoodsListDataItemModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(82).string(message.goodsId);
    }
    if (message.goodsNo !== "") {
      writer.uint32(162).string(message.goodsNo);
    }
    if (message.goodsName !== "") {
      writer.uint32(242).string(message.goodsName);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(322).string(message.goodsTitle);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(362).string(message.goodsPriceUnitName);
    }
    if (message.currency !== 0) {
      writer.uint32(368).int32(message.currency);
    }
    if (message.minPrice !== 0) {
      writer.uint32(401).double(message.minPrice);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(441).double(message.maxPrice);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(880).int32(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      writer.uint32(960).int32(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      writer.uint32(482).string(message.mainImageUrl);
    }
    if (message.goodsRank !== 0) {
      writer.uint32(1685).float(message.goodsRank);
    }
    if (message.hitScore !== 0) {
      writer.uint32(1765).float(message.hitScore);
    }
    if (message.sourceGoodsId !== "") {
      writer.uint32(1842).string(message.sourceGoodsId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListDataItemModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListDataItemModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 45:
          if (tag !== 362) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 46:
          if (tag !== 368) {
            break;
          }

          message.currency = reader.int32() as any;
          continue;
        case 50:
          if (tag !== 401) {
            break;
          }

          message.minPrice = reader.double();
          continue;
        case 55:
          if (tag !== 441) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 110:
          if (tag !== 880) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 120:
          if (tag !== 960) {
            break;
          }

          message.minIncreaseQuantity = reader.int32();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.mainImageUrl = reader.string();
          continue;
        case 210:
          if (tag !== 1685) {
            break;
          }

          message.goodsRank = reader.float();
          continue;
        case 220:
          if (tag !== 1765) {
            break;
          }

          message.hitScore = reader.float();
          continue;
        case 230:
          if (tag !== 1842) {
            break;
          }

          message.sourceGoodsId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListDataItemModel {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      goodsName: isSet(object.goodsName) ? globalThis.String(object.goodsName) : "",
      goodsTitle: isSet(object.goodsTitle) ? globalThis.String(object.goodsTitle) : "",
      goodsPriceUnitName: isSet(object.goodsPriceUnitName) ? globalThis.String(object.goodsPriceUnitName) : "",
      currency: isSet(object.currency) ? currencyTypeFromJSON(object.currency) : 0,
      minPrice: isSet(object.minPrice) ? globalThis.Number(object.minPrice) : 0,
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      minBuyQuantity: isSet(object.minBuyQuantity) ? globalThis.Number(object.minBuyQuantity) : 0,
      minIncreaseQuantity: isSet(object.minIncreaseQuantity) ? globalThis.Number(object.minIncreaseQuantity) : 0,
      mainImageUrl: isSet(object.mainImageUrl) ? globalThis.String(object.mainImageUrl) : "",
      goodsRank: isSet(object.goodsRank) ? globalThis.Number(object.goodsRank) : 0,
      hitScore: isSet(object.hitScore) ? globalThis.Number(object.hitScore) : 0,
      sourceGoodsId: isSet(object.sourceGoodsId) ? globalThis.String(object.sourceGoodsId) : "",
    };
  },

  toJSON(message: GoodsListDataItemModel): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.currency !== 0) {
      obj.currency = currencyTypeToJSON(message.currency);
    }
    if (message.minPrice !== 0) {
      obj.minPrice = message.minPrice;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.minIncreaseQuantity !== 0) {
      obj.minIncreaseQuantity = Math.round(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      obj.mainImageUrl = message.mainImageUrl;
    }
    if (message.goodsRank !== 0) {
      obj.goodsRank = message.goodsRank;
    }
    if (message.hitScore !== 0) {
      obj.hitScore = message.hitScore;
    }
    if (message.sourceGoodsId !== "") {
      obj.sourceGoodsId = message.sourceGoodsId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListDataItemModel>, I>>(base?: I): GoodsListDataItemModel {
    return GoodsListDataItemModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListDataItemModel>, I>>(object: I): GoodsListDataItemModel {
    const message = createBaseGoodsListDataItemModel();
    message.goodsId = object.goodsId ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.goodsName = object.goodsName ?? "";
    message.goodsTitle = object.goodsTitle ?? "";
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.currency = object.currency ?? 0;
    message.minPrice = object.minPrice ?? 0;
    message.maxPrice = object.maxPrice ?? 0;
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.minIncreaseQuantity = object.minIncreaseQuantity ?? 0;
    message.mainImageUrl = object.mainImageUrl ?? "";
    message.goodsRank = object.goodsRank ?? 0;
    message.hitScore = object.hitScore ?? 0;
    message.sourceGoodsId = object.sourceGoodsId ?? "";
    return message;
  },
};

function createBaseSkuModel(): SkuModel {
  return {
    skuId: "",
    goodsId: "",
    skuNo: "",
    goodsNo: "",
    picUrl: "",
    goodsName: "",
    specList: [],
    count: 0,
    goodsPriceUnitName: "",
    unitPrice: 0,
    currency: "",
    totalAmount: 0,
  };
}

export const SkuModel = {
  encode(message: SkuModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.skuId !== "") {
      writer.uint32(82).string(message.skuId);
    }
    if (message.goodsId !== "") {
      writer.uint32(90).string(message.goodsId);
    }
    if (message.skuNo !== "") {
      writer.uint32(98).string(message.skuNo);
    }
    if (message.goodsNo !== "") {
      writer.uint32(106).string(message.goodsNo);
    }
    if (message.picUrl !== "") {
      writer.uint32(162).string(message.picUrl);
    }
    if (message.goodsName !== "") {
      writer.uint32(242).string(message.goodsName);
    }
    for (const v of message.specList) {
      SpecModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.count !== 0) {
      writer.uint32(400).int32(message.count);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(482).string(message.goodsPriceUnitName);
    }
    if (message.unitPrice !== 0) {
      writer.uint32(561).double(message.unitPrice);
    }
    if (message.currency !== "") {
      writer.uint32(642).string(message.currency);
    }
    if (message.totalAmount !== 0) {
      writer.uint32(721).double(message.totalAmount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SkuModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkuModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.skuId = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.picUrl = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.specList.push(SpecModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.count = reader.int32();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 70:
          if (tag !== 561) {
            break;
          }

          message.unitPrice = reader.double();
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.currency = reader.string();
          continue;
        case 90:
          if (tag !== 721) {
            break;
          }

          message.totalAmount = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SkuModel {
    return {
      skuId: isSet(object.skuId) ? globalThis.String(object.skuId) : "",
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      picUrl: isSet(object.picUrl) ? globalThis.String(object.picUrl) : "",
      goodsName: isSet(object.goodsName) ? globalThis.String(object.goodsName) : "",
      specList: globalThis.Array.isArray(object?.specList)
        ? object.specList.map((e: any) => SpecModel.fromJSON(e))
        : [],
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
      goodsPriceUnitName: isSet(object.goodsPriceUnitName) ? globalThis.String(object.goodsPriceUnitName) : "",
      unitPrice: isSet(object.unitPrice) ? globalThis.Number(object.unitPrice) : 0,
      currency: isSet(object.currency) ? globalThis.String(object.currency) : "",
      totalAmount: isSet(object.totalAmount) ? globalThis.Number(object.totalAmount) : 0,
    };
  },

  toJSON(message: SkuModel): unknown {
    const obj: any = {};
    if (message.skuId !== "") {
      obj.skuId = message.skuId;
    }
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.picUrl !== "") {
      obj.picUrl = message.picUrl;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.specList?.length) {
      obj.specList = message.specList.map((e) => SpecModel.toJSON(e));
    }
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.unitPrice !== 0) {
      obj.unitPrice = message.unitPrice;
    }
    if (message.currency !== "") {
      obj.currency = message.currency;
    }
    if (message.totalAmount !== 0) {
      obj.totalAmount = message.totalAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SkuModel>, I>>(base?: I): SkuModel {
    return SkuModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuModel>, I>>(object: I): SkuModel {
    const message = createBaseSkuModel();
    message.skuId = object.skuId ?? "";
    message.goodsId = object.goodsId ?? "";
    message.skuNo = object.skuNo ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.picUrl = object.picUrl ?? "";
    message.goodsName = object.goodsName ?? "";
    message.specList = object.specList?.map((e) => SpecModel.fromPartial(e)) || [];
    message.count = object.count ?? 0;
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.unitPrice = object.unitPrice ?? 0;
    message.currency = object.currency ?? "";
    message.totalAmount = object.totalAmount ?? 0;
    return message;
  },
};

function createBaseSpecModel(): SpecModel {
  return { specName: "", itemName: "" };
}

export const SpecModel = {
  encode(message: SpecModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.specName !== "") {
      writer.uint32(82).string(message.specName);
    }
    if (message.itemName !== "") {
      writer.uint32(162).string(message.itemName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SpecModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSpecModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.specName = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.itemName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SpecModel {
    return {
      specName: isSet(object.specName) ? globalThis.String(object.specName) : "",
      itemName: isSet(object.itemName) ? globalThis.String(object.itemName) : "",
    };
  },

  toJSON(message: SpecModel): unknown {
    const obj: any = {};
    if (message.specName !== "") {
      obj.specName = message.specName;
    }
    if (message.itemName !== "") {
      obj.itemName = message.itemName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SpecModel>, I>>(base?: I): SpecModel {
    return SpecModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpecModel>, I>>(object: I): SpecModel {
    const message = createBaseSpecModel();
    message.specName = object.specName ?? "";
    message.itemName = object.itemName ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
