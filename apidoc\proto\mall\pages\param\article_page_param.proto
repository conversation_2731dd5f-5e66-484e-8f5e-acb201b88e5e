syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";

message ListArticleCategoryParam {
    repeated string ids = 1;
    repeated string names = 2;
}

message ListArticleParam {
    common.VisitDeviceType deviceType = 1;
    string articleCategoryId = 2;
}

message ArticleDetailParam {
    string id = 1;
    string title = 2; //文章标题
    string articleCode = 3; //文章代码
}

// 查询BLOG列表的参数
message GetBlogListParam {
    common.PageParam page = 10;
    common.VisitDeviceType deviceType = 20;
}

message SearchArticleListParam {
    common.PageParam page = 10;
    string keyword = 20; //搜索关键词
    string publishDate = 30; //发布日期
    string cateId = 40; //子类目ID(标签)
}

