syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.param";

import "common.proto";

message WarehouseSaveParam {
  string id = 1;
  string code = 2; // 仓库代码
  string name = 3; // 仓库名称
  string contact = 4; // 联系人
  string phone = 5; // 联系电话
  string postcode = 6; // 邮编
  string province = 7; // 省
  string city = 8; // 市
  string district = 9; // 区
  string town = 10; // 镇
  string streetAddress = 11; // 街道地址
}

message WarehouseListParam {
  bool enabled = 1; // 是否启用
}