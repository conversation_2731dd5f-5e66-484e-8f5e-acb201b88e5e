syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";

message ConfigResp {
  common.Result result = 1;
  ConfigModel data = 2;
}

message LanguageResp {
  common.Result result = 1;
  LanguageModel data = 2;
}

// 全局配置
message ConfigModel {
  string lang = 1; // 当前语言
  map<string, string> langs = 2; // 所有语言
  map<string, string> trans = 3; // 语言翻译
}

// 语言包
message LanguageModel {
  map<string, string> trans = 1; // 语言翻译
}

// 翻译字典表
message SysTranslateModel {
  string id = 1;
  int32 systemId = 2;
  string moduleCode = 3;
  string tranCode = 4;
  string termZH = 5;
  string termEN = 6;
  string termES = 7;
  string moduleDesc = 8;
}

message TranslatePageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated SysTranslateModel data = 3;
}