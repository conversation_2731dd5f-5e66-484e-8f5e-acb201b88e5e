syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport.param";

import "common.proto";

message AuthLoginParam {
  string username = 1; // 用户名
  string password = 2; // 密码
}

message ModifyPasswordParam {
  string email = 1; // 邮箱
  string password = 2; // 密码
  string captcha = 3; // 验证码
}

message RegisterParam {
  string username = 1; // 用户名
  string password = 2; // 密码
  string fromInviteCode = 3; //邀请人邀请码
}