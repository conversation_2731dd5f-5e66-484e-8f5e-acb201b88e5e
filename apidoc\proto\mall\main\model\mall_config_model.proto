syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main.model";


import "common.proto";

message SiteOptionResp {
  common.Result result = 1;
  SiteOptionModel data = 2;
}

message SiteOptionModel {
  string optionCodeGroup = 1; //分组设置
  string optionCode = 2; //配置编码
  string optionName = 3; //配置名
  string optionData = 4; //配置值
}

message ThemePageForMallResp {
  common.Result result = 1;
  ThemeModel data = 2;
}

message ThemeModel {
  string globalSetting = 1; //主题参数配置
  repeated ThemePageModel content = 2; //页面内容
}

message ThemePageModel {
  string create_time = 1; //创建时间
  string name = 2; //内容类型
  bool hidden = 3; //展示标志
  ThemePageWidgetsModel widgets = 4; //内容
}

message ThemePageWidgetsModel {
  repeated string config = 1; // 配置
  string content = 2; //内容
  string name = 3; //内容类型
  string icon = 4;
  string title = 5; //标题
}

//预估运费接口返回
message CalculateEstimateFreightResp {
  common.Result result = 1;
  CalculateEstimateFreightModel data = 2;
}

//预估运费接口返回对象
message CalculateEstimateFreightModel {
  double totalEstimateFreight = 10; //选中线路的预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 20; //选中线路的预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 30; //选中线路的参与预估运费的商品数量
  string selectedRouteId = 40; //选中的线路ID（以此更新页面中选中的线路ID）
  repeated CalculateRouteFreightModel routeList = 50; //线路运费列表
}

//线路运费对象
message CalculateRouteFreightModel {
  string routeId = 10; //线路ID
  string routeName = 20; //线路名称
  string deliverTimeName = 30; //交期名称（将交期格式化后的字符串）
  double totalEstimateFreight = 110; //预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 120; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 130; //参与预估运费的商品数量
  bool selected = 140; //是否选中
}