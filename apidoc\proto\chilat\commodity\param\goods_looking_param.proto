syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "chilat/commodity/commodity_common.proto";
import "chilat/inquiry/inquiry_common.proto";
import "common.proto";

// 询盘列表参数
message GoodsLookingPageQueryParam {
  common.PageParam page = 1;
  GoodsLookingQueryParam query = 2;
}

// 询盘查询参数
message GoodsLookingQueryParam {
  int64 startSubmitTime = 1; // 开始询盘时间
  int64 endSubmitTime = 2; // 结束询盘时间
  string goodsLookingNo = 3; // 询盘号
  string whatsapp = 4; // whatsapp
  string email = 5; // 邮箱
  string goodsSubmitter = 6; // 询盘人
  string goodsNo = 7; // 商品编号
  string goodsName = 8; // 商品名称
  repeated string countryIds = 9; // 国家列表
  string goodsFinder = 10; // 找货人
  repeated GoodsLookingState states = 11; // 询盘状态
  repeated string selectedIds = 12; // 选中ID列表
  bool isSummary = 13; //是否导出汇总询盘信息
  string submitId = 14; //求购人id
  string token = 15; //访客id
  string categoryId = 16; //分类id
  repeated string tagIds = 17; //标签id
  bool bindOrder = 18; //是否过滤已绑定订单号
  inquiry.InquiryXpState inquiryXpstate = 19; //询盘列表的询价状态
}

// 备注参数
message GoodsLookingRemarkParam {
  string id = 1; // 询盘ID
  string remark = 2; // 备注
}

// 分配参数
message GoodsLookingAssignParam {
  repeated string ids = 1; // 询盘ID列表
  repeated GoodsLookingFinderParam users = 2; // 业务员列表
}

// 变更负责人参数
message GoodsLookingTransferParam {
  repeated string ids = 1; // 询盘ID列表
  GoodsLookingFinderParam user = 2; // 业务员
  string remark = 3; // 备注
}

// 流转参数
message FlowStatusParam {
  string id = 10; //询盘单ID
  GoodsLookingState newState = 20; //新状态
  string remark = 3; // 备注
}

// 业务员参数
message GoodsLookingFinderParam {
  string userId = 1; // 业务员ID
  string username = 2; // 业务员名称
}

// 询盘模板类型参数
message GoodsLookingTemplateTypeParam {
    GoodsLookingTemplateType type = 1; // 询盘模板类型
}

enum GoodsLookingTemplateType {
  GOODS_LOOOKING_TEMPLATE_TYPE_UNKNOWN = 0;
  GOODS_LOOOKING_TEMPLATE_TYPE_LOOKING = 1; // 询盘
  GOODS_LOOOKING_TEMPLATE_TYPE_PACKAGE = 2; // 包装
}

message PageHistoryGoodsLookingParam {
  common.PageParam page = 1;
  HistoryGoodsLookingParam query = 2;
}

message HistoryGoodsLookingParam {
  string token = 1;
  string whatsapp = 2; //whatsapp
  bool today = 3; //是否只查询今日
}