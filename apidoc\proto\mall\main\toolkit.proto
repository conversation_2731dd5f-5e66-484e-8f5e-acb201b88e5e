syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main";

import "common.proto";
import "mall/main/model/toolkit_model.proto";
import "mall/main/param/toolkit_param.proto";

// 网站工具箱
service Toolkit {
  // 获取验证码的图形数据
  rpc getCaptchaImage (GetCaptchaImageParam) returns (GetCaptchaImageResp);
  // 检查图形验证码（验证码错误的错误码：952401）
  rpc checkCaptchaImage (CheckCaptchaImageParam) returns (common.ApiResult);
}
