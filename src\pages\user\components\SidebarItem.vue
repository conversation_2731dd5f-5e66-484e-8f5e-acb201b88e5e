<template>
  <div>
    <div
      class="sidebar-item"
      data-spm-box="myhome-left-menu"
      :class="{
        'text-[#E50013]': hasActiveChild || isHovered,
        'text-[#000]': !hasActiveChild && !isHovered,
        '!text-[#FFF] bg-[#E50013]': isActive(props.item),
      }"
      @click="handleClick($event)"
      @mouseover="isHovered = true"
      @mouseleave="isHovered = false"
    >
      <n-flex vertical style="gap: 0px">
        <div>
          <span
            ><icon-card
              v-if="props.item.icon"
              :name="props.item.icon"
              :size="props.item.iconSize"
              :color="iconColor"
              class="mr-1.5"
            ></icon-card
          ></span>
          <span>{{ props.item.name }}</span>
        </div>
        <div
          v-if="props.item.subtitle && !isActive(props.item)"
          class="text-[12px] leading-[12px] font-medium mt-[4px] text-[#e50113]"
        >
          {{ props.item.subtitle }}
        </div>
      </n-flex>
      <div
        v-if="props.item.children && props.item.children.length"
        class="ml-auto"
      >
        <button class="p-1 toggle-button">
          <icon-card
            size="22"
            :name="
              isExpanded
                ? 'iconamoon:arrow-up-2-light'
                : 'iconamoon:arrow-down-2-light'
            "
            :color="iconColor"
          ></icon-card>
        </button>
      </div>
    </div>
    <transition
      name="expand"
      @before-enter="beforeEnter"
      @enter="enter"
      @leave="leave"
    >
      <div
        v-if="isExpanded && props.item.children && props.item.children.length"
        class="ml-4 text-[14px] child-container"
      >
        <SidebarItem
          v-for="child in props.item.children"
          :key="child.path"
          :item="child"
          :isActive="isActive"
          :onGoPage="onGoPage"
        />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { defineProps, ref, computed } from "vue";

const props = defineProps({
  item: Object,
  isActive: Function,
  onGoPage: Function,
});

const isExpanded = ref(false);
const isHovered = ref(false);

onBeforeMount(() => {
  if (props.item.children && props.item.children.length) {
    isExpanded.value = props.item.children.some((child) =>
      props.isActive(child),
    );
  }
});

const handleClick = (event) => {
  if (props.item.children && props.item.children.length) {
    isExpanded.value = !isExpanded.value;
  } else {
    props.onGoPage(props.item, event);
  }
};

const hasActiveChild = computed(() => {
  if (props.item.children && props.item.children.length) {
    return props.item.children.some((child) => props.isActive(child));
  }
  return false;
});

const iconColor = computed(() => {
  if (props.isActive(props.item)) return "#FFF";
  if (isHovered.value || hasActiveChild.value) return "#E50013";
  return "#000";
});

const beforeEnter = (el) => {
  el.style.height = "0";
  el.style.opacity = "0";
};

const enter = (el) => {
  el.style.height = el.scrollHeight + "px";
  el.style.opacity = "1";
  el.style.transition = "height 0.3s ease, opacity 0.3s ease";
};

const leave = (el) => {
  el.style.height = "0";
  el.style.opacity = "0";
  el.style.transition = "height 0.3s ease, opacity 0.3s ease";
};
</script>

<style scoped>
.sidebar-item {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  margin-bottom: 0.5rem;
}
.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.expand-enter-active,
.expand-leave-active {
  transition: height 0.3s ease, opacity 0.3s ease;
}
.expand-enter,
.expand-leave-to {
  height: 0;
  opacity: 0;
}
</style>
