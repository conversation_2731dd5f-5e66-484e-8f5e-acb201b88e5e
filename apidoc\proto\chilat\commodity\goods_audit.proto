syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_audit_param.proto";
import "chilat/commodity/model/goods_audit_model.proto";
import "common.proto";

// 审核管理
service GoodsAudit {
  // 审核列表
  rpc pageList (GoodsAuditPageQueryParam) returns (GoodsAuditPageResp);
  // 批次列表
  rpc batchPageList (AuditBatchPageQueryParam) returns (AuditBatchPageResp);
  // 分享列表
  rpc linkPageList (ShareLinkPageQueryParam) returns (ShareLinkPageResp);
  // 审核商品
  rpc goodsPageList (AuditGoodsPageQueryParam) returns (AuditGoodsPageResp);
  // 预览批次
  rpc previewBatch (CreateBatchParam) returns (common.IntResult);
  // 生成批次
  rpc createBatch (CreateBatchParam) returns (common.StringResult);
  // 分配任务
  rpc dispatch (DispatchParam) returns (common.ApiResult);
  // 审核日志
  rpc log (common.IdParam) returns (common.LogResult);
  // 责任人列表
  rpc leaderList (common.EmptyParam) returns (LeaderListResp);
  // 创建分享
  rpc createLink (CreateLinkParam) returns (ShareLinkResp);
  // 取消分享
  rpc cancelLink (common.IdParam) returns (common.ApiResult);
}