syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model.payment";

import "common.proto";
import "common/business.proto";

//查询支付结果
message QueryPayResultResp {
  common.Result result = 10; //错误码
  QueryPayResultModel data = 20; //返回数据
}

message QueryPayResultModel {
  PayResultModel payResult = 10;//支付结果
  common.OrderStatus orderStatus = 20; //支付时订单原始状态
  common.PayMode payMode = 30; //支付模式
}

message PayResultModel {
  common.PayStatusEnum payStatus = 10; //返回支付结果
  string errorMsg = 20; //如果失败的话，返回失败原因
}

//未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
message OpenCashDeskResp {
  common.Result result = 10; //错误码
  OpenCashDeskModel data = 20; //返回数据
}

message OpenCashDeskModel {
  string paymentId = 10;
}

//提交支付
message SubmitPaymentResp {
  common.Result result = 10; //错误码
  SubmitPaymentModel data = 20; //返回数据
}

message SubmitPaymentModel {
  string payUrl = 10; //第三方支付的pay url，前端需要跳转过去
}

message PayMethodListModel {
  repeated PayMethodModel payMethodList = 10; //支付方式列表
}

message PayMethodModel {
  string code = 10; //支付方式code
  string name = 20; //名称，显示用
  string iconUrl = 30; //支付方式图标icon url
  string id = 40; //支付方式唯一id
  double min = 50; //限额最低值
  double max = 60; //限额最大值
}

message QueryCancelReasonConfigResp {
  common.Result result = 10; //错误码
  CancelReasonConfigListModel data = 20;//数据
}

message CancelReasonConfigListModel {
  repeated CancelReasonConfigModel reasonList = 10;
}

message CancelReasonConfigModel {
  string reasonId = 10; //取消理由id
  string reasonDesc = 20; //取消理由描述
  string reasonDescTrans = 30; //取消理由描述(外文)
}

//查询收银台信息，包括待支付金额、支付方式列表、是否已支付
message GetCashDeskInfoResp {
  common.Result result = 10; //错误码
  GetCashDeskInfoModel data = 20; //数据
}

message GetCashDeskInfoModel {
  repeated PayMethodModel payMethodList = 10; //支付方式列表
  PaymentAmountModel payAmount = 20; //支付金额
  PayResultModel payResultModel = 30; //支付结果
  string goodsName = 40; //订单里的sku里挑一个
  string picUrl = 50; //sku的图片
  int32 totalCount = 60; //总的商品数量
}

message PaymentAmountModel {
  double amount = 10;
  repeated FeeModel feeList = 20;
}

message FeeModel {
  string feeName = 10; //费用名称
  double feeAmount = 20; //费用金额
  repeated FeeModel childFeeList  = 30; //子费用
}

message QueryPagSmilePayDetailResp {
  common.Result result = 10; //错误码
  QueryPagSmilePayDetailModel data = 20; //数据
}

message QueryPagSmilePayDetailModel {
  PagSmilePayDetailModel detail = 10;
}

message PagSmilePayDetailModel {
  string tradeStatus = 10; //pagsmile侧的状态
  string orderAmount = 20;
  string orderCurrency = 30;
  string createTime = 40;
  string updateTime = 50;
  string refuseDetail = 60; //Refuse only
  string platformTradeNo = 70; //pagsmile侧的tradeNo
  string localTradeNo = 80; //本地的tradeNo
  string payMethod = 90; //pagsmile pay method
  string PayMethodChannel = 100; //pagsmile method channel
  int64 defaultCreateTime = 110;
}

message QueryOnlineCountryResp {
  common.Result result = 10; //错误码
  QueryOnlineCountryModel data = 20; //数据
}

message QueryOnlineCountryModel {

  repeated string countryIdList = 10; //国家id列表
}

message GetOnlineTradeListResp {
  common.Result result = 10; //错误码
  OnlineTradeListModel data = 20; //数据
}

message OnlineTradeListModel {
  repeated OnlineTrade payingTradeList = 10;
}

message OnlineTrade {
  string orderNo = 10;
  string payId = 20;
  string localTradeNo = 30;
  string platformTradeNo = 31;
  string payMethodCode = 40;
  string payMethodName = 50;
  string payMethodId = 60;
  string payChannel = 70;
  double amount = 80;
  string currency = 90;
  string tradeStatus = 100;
  int64 createTime = 110;
  string payUrl = 120;
}

message SubmitOfflinePayResp {
  common.Result result = 10; //错误码
  SubmitOfflinePayModel data = 20; //数据
}

message SubmitOfflinePayModel {
  string payId = 10; //payId
}