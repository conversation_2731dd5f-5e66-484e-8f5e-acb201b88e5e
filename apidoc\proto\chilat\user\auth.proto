syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user";

import "chilat/user/param/auth_param.proto";
import "chilat/user/model/auth_model.proto";
import "common.proto";

// 用户认证
service Auth {
  // 登录
  rpc loginBackend(AuthLoginParam) returns (AuthLoginResp);
  // 登出
  rpc logout(common.EmptyParam) returns (common.ApiResult);
  // 修改密码
  rpc modifyPassword(common.StringParam) returns (common.ApiResult);
}
