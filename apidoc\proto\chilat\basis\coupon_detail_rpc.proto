syntax = "proto3";
package chilat.basis;

import "chilat/basis/model/coupon_active_model.proto";
import "chilat/basis/model/coupon_usable_model.proto";
import "chilat/basis/model/coupon_use_model.proto";
import "chilat/basis/model/my_coupon_detail_model.proto";
import "chilat/basis/param/coupon_active_param.proto";
import "chilat/basis/param/coupon_distribution_param.proto";
import "chilat/basis/param/coupon_usable_param.proto";
import "chilat/basis/param/coupon_use_param.proto";
import "chilat/basis/param/my_coupon_detail_param.proto";
import "common.proto";


option java_package = "com.chilat.rpc.basis.coupon";
// 卡券管理
service MyCouponDetailRpc {

  // 我的优惠券列表查询
  rpc getMyCouponDetailList (MyCouponDetailParam) returns (MyCouponDetailModelResp);

  // 根据订单锁定优惠券
  rpc orderCouponLock (CouponOrderNoLockParam) returns (common.ApiResult);

  // 根据订单使用优惠券
  rpc orderCouponUse (CouponOrderUseParam) returns (CouponUseModelResp);

  // 根据订单退回优惠券
  rpc orderCouponRefund (CouponOrderRefundParam) returns (common.ApiResult);

  // 优惠券退回
  rpc couponRefund (common.IdParam) returns (common.ApiResult);

  //发放优惠券
  rpc couponDistribution (CouponDistributionParam) returns (common.ApiResult);

  //可用产品券/佣金券/列表
  rpc getCouponUsableList (CouponUsableParam) returns (CouponUsableModelResp);

  //通过订单查询已使用产品券/佣金券/列表
  rpc getOrderCouponUsebleList (CouponUsableParam) returns (CouponUsableModelResp);

  //checkbox优惠券校验
  rpc checkAvailableList (CouponCheckParam) returns (CouponAvailableModelResp);

  //根据订单号清空优惠券
  rpc cleanCouponOrder (CouponUseParam) returns (common.ApiResult);

  //优惠券计算
  rpc calculateCoupon (CouponUseParam) returns (CouponUseModelResp);

  //批量发放优惠券
  rpc couponListDistribution (CouponDistributionListParam) returns (CouponDistributionBatchModel);

  //活动id查询可用优惠券
  rpc couponActiveCode (CouponActiveParam) returns (CouponActiveModelResp);

  //订单绑定优惠券查询
  rpc orderNoBindCouponList (CouponUsableParam) returns (CouponUseModelResp);

  //优惠券id查询可用减券
  rpc couponFullReductionId (CouponFullReductionParam) returns (CouponFullReductionResp);
}