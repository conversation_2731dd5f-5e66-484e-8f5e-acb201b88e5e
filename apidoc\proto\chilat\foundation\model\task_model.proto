syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "chilat/foundation/foundation_common.proto";
import "common.proto";

message TaskPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated TaskModel data = 3;
}

message TaskModel {
  string id = 1;
  string name = 2; // 任务名称
  TaskType type = 3; // 任务类型
  TaskState state = 4; // 任务状态
  string coperator = 5; // 操作人
  string cdate = 6; // 创建时间
}