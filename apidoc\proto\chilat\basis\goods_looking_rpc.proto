syntax = "proto3";

package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/goods_looking_goods_model.proto";
import "common.proto";
import "chilat/basis/param/goods_looking_param.proto";
import "chilat/basis/model/goods_looking_model.proto";
import "chilat/basis/model/cart_model.proto";

// 询盘
service GoodsLookingRpc {
    // 询盘
    rpc inquiry (InquiryParam) returns (MidCartResp);
    // 提交询盘
    rpc submit (SubmitSkuParam) returns (QueryInquiryResp);
    // 查询询盘数据
    rpc queryInquiry (common.StringParam) returns (QueryInquiryResp);
    // 导出给客户
    rpc exportToCustomer (common.IdParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    // 导出给采购
    rpc exportToPurchase (common.IdParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    rpc pageList (GoodsLookingPageListParam) returns (GoodsLookingPageListResp);
    //获取询盘商品数据
    rpc getGoodLookingGoodsList (common.IdParam) returns (GoodsLookingGoodsModelResp);

    // 推送询盘单到询盘云（参数为询盘单号）
    rpc pushXunPanYun(common.CodeParam) returns (common.ApiResult);
}