syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main";

import "common.proto";
import "mall/main/param/mall_config_param.proto";
import "mall/main/model/mall_config_model.proto";
import "chilat/basis/model/config_model.proto";

// 全局配置
service MallConfig {
  // 获取配置
  rpc getConfig (ConfigGetParam) returns (chilat.basis.ConfigResp);
  // 设置语言
  rpc setLanguage (LanguageSetParam) returns (chilat.basis.LanguageResp);
  // 设置当前站点的选择（传参为siteId，即配送国家ID）
  rpc setCurrentSite (common.IdParam) returns (common.ApiResult);
  // 站点配置
  rpc getSiteOption(GetSiteOptionParam) returns (SiteOptionResp);
  // 查询-商城首页相关页
  rpc getThemePageForMall(GetThemePageParam) returns (ThemePageForMallResp);
  // 计算预估运费
  rpc calculateEstimateFreight(CalculateEstimateFreightParam) returns (CalculateEstimateFreightResp);
}