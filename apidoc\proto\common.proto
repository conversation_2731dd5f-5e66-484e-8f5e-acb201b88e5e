syntax = "proto3";
package common;

option java_package = "com.chilat.rpc.common";

import "google.protobuf/descriptor.proto";

/*------------------------------ 通用入参 ------------------------------*/

message EmptyParam {
}

message IdParam {
    string id = 1;
}

message IdsParam {
    repeated string ids = 1;
}

message CodeParam {
    string code = 1;
}

message CodesParam {
    repeated string codes = 1;
}

message IdValueParam {
    string id = 1;
    string value = 2;
}

message IdValuesParam {
    string id = 1;
    repeated string values = 2;
}

message StringParam {
    string str = 1;
}

message StringsParam {
    repeated string strs = 1;
}

message IntParam {
    int32 value = 1;
}

message LongParam {
    int64 value = 1;
}

message DoubleParam {
    double value = 1;
}

message EnabledParam {
    string id = 1;
    bool enabled = 2;
}

message BatchEnabledParam {
    repeated string ids = 1;
    bool enabled = 2;
}


message PageParam {
    int64 current = 1; // 当前页
    int64 size = 2; // 每页大小
    repeated SortParam sort = 3; // 排序
}

message MapParam {
    map<string, string> map = 1;
}

/*------------------------------ 通用出参 ------------------------------*/

message Result {
    int32 code = 1;
    string message = 2;
}

message ApiResult {
    Result result = 1;
}

message UploadResult {
    Result result = 1;
    string data = 2;
}

message DownloadResult {
    Result result = 1;
    File data = 2;
}

message IntResult {
    Result result = 1;
    int32 data = 2;
}

message LongResult {
    Result result = 1;
    int64 data = 2;
}

message IntsResult {
    Result result = 1;
    repeated int32 data = 2;
}

message DoubleResult {
    Result result = 1;
    double data = 2;
}

message IdResult {
    Result result = 1;
    string data = 2;
}

message IdsResult {
    Result result = 1;
    repeated string data = 2;
}

message StringResult {
    Result result = 1;
    string data = 2;
}

message StringsResult {
    Result result = 1;
    repeated string data = 2;
}

message BooleanResult {
    Result result = 1;
    bool data = 2;
}

message BooleansResult {
    Result result = 1;
    repeated bool data = 2;
}

message IdNameResult {
    Result result = 1;
    IdNameModel data = 2;
}

message NameValueResult {
    Result result = 1;
    NameValueModel data = 2;
}

message NameValuesResult {
    Result result = 1;
    NameValuesModel data = 2;
}

message MapResult {
    Result result = 1;
    map<string, string> data = 2;
}

message LogResult {
    Result result = 1;
    repeated LogModel data = 2;
}

message ImportResult {
    Result result = 1;
    ImportModel data = 2;
}

/*------------------------------ 基础参数 ------------------------------*/

message SortParam {
    string orderBy = 1; // 排序字段
    bool isAsc = 2; // 是否升序
}

message IdNameModel {
    string id = 1;
    string name = 2;
}

message CodeNameModel {
    string code = 1;
    string name = 2;
}

message NameValueModel {
    string name = 1;
    string value = 2;
}

message NameCodeModel {
    string name = 1;
    int32 code = 2;
}

message NameValuesModel {
    string name = 1;
    repeated string values = 2;
}

message LogModel {
    int64 id = 1; // 日志ID
    string action = 2; // 操作
    string remark = 3; // 日志说明
    repeated string images = 4; // 日志图片
    string createBy = 5; // 操作人
    int64 createTime = 6; // 操作时间
}

message ImportModel {
    int32 totalCount = 1; // 导入总数
    int32 successCount = 2; // 成功总数
    int32 failCount = 3; // 失败总数
    string downloadUrl = 4; // 失败文件地址
}

message Page {
    int64 current = 1; // 当前页
    int64 size = 2; // 每页大小
    int64 pages = 3; // 总页数
    int64 total = 4; // 总数
}

message FileInfo {
    string fileName = 1;
    string fileUrl = 2;
}

/*------------------------------ 以下为基础定义！勿动！ ------------------------------*/

message File {
    FileHeader header = 1;
    bytes data = 2;
    option (msgopt).generated = false;
}

message FileHeader {
    string filename = 1;
    string contentType = 2;
    map<string, Strings> mime = 3;
}

message Strings {
    repeated string value = 1;
}

message WebapiOption {
    bool upload = 1; // 使用multipart上传文件
    bool download = 2; // 使用二进制流而不是json
}

message MessageOption {
    bool generated = 1; // 是否生成对象
}

extend google.protobuf.MethodOptions {
    WebapiOption webapi = 72295728;
    string timeout = 72295730;
}

extend google.protobuf.MessageOptions {
    MessageOption msgopt = 80000001;
}

extend google.protobuf.FileOptions {
    bool force_generated = 80000001;
}