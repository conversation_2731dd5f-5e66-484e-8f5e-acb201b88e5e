syntax = "proto3";
package xpormayor.commodity;

option java_package = "com.xpormayor.rpc.commodity";

import "xpormayor/commodity/param/goods_looking_param.proto";
import "xpormayor/commodity/model/goods_looking_model.proto";
import "common.proto";

// 求购商品
service GoodsLooking {
  // 暂存求购信息
  rpc saveTemporary (GoodsLookingSaveParam) returns (GoodsLookingSaveResp);
  // 保存求购信息
  rpc save (GoodsLookingSaveParam) returns (GoodsLookingSaveResp);
}
