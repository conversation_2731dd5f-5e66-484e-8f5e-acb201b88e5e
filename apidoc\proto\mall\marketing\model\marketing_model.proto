syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.model";

import "common.proto";

// 营销分类信息
message MarketingCategoryModel {
  string id = 1; // 营销分类id
  string parentId = 2; // 营销分类父id
  int32 cateLevel = 3; // 营销分类级别,1:一级类目,2:二级类目,3:三级类目
  int32 idx = 4; // 排序
  string cateName = 5; // 营销分类名称
  string cateAlias = 6; // 营销分类-别名
  string cateLogo = 7; // 营销分类logo
  int32 goodsCount = 8; //商品数量（通过营销分类搜索得到）
  repeated MarketingCategoryModel children = 9; // 下级营销分类
}

message MarketingCategoryTreeResp {
  common.Result result = 1;
  repeated MarketingCategoryModel data = 2;
}