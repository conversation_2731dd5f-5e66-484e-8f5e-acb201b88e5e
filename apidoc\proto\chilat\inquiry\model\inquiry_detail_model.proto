syntax = "proto3";

package chilat.inquiry;

import "chilat/basis/model/sales_order_model.proto";
import "chilat/inquiry/inquiry_common.proto";
import "common.proto";


option java_package = "com.chilat.rpc.inquiry.model";

//询盘详情返回
message InquiryDetailModelResp {
    common.Result result = 1;
    InquiryDetailModel data = 2; //询盘详情参数
}

/**
询盘详情
 */
message InquiryDetailModel {
    string inquiryBatchNo = 1; // 询价批次号，用于唯一标识一个询价批次
    string submitName = 2; // 客户名称，表示发起询价的客户
    string currentInquiryPerson = 3; // 现询价人，当前负责处理询价的员工姓名
    string originalInquiryPerson = 4; // 原询价人，最初负责处理询价的员工姓名
    InquiryStatus inquiryStatus = 5;//询价单状态，通过点击 开始询价按钮/完成询价按钮变更状态（未询价：NO_INQUIRY  询价中：PROGRESS_INQUIRY 已询价：HAS_INQUIRY）
    int64 storeCount = 6; // 店铺数
    int64 goodsCount = 7; // 商品数
    int32 skuCount = 8; // sku数(未回填+已回填)
    int64 hasSkuInquiryCount = 9; // 已询sku数（已回填 详情店铺中SKU商品完整装箱信息的数）
    int64 noSkuInquiryCount = 10; // 未询sku数（未回填 详情店铺中SKU商品完整装箱信息的数)
    repeated StoreModel storeInfo = 11;//店铺信息
    repeated SystemInquiryModel systemInquiryList = 20;//操作日志
}


/**
店铺详情
 */
message StoreDetailModel {
    string skuId = 2; // SKU_ID
    string skuNo = 3; // SKU商品货号
    string goodsName = 5; // 商品名称
    string goodsNameCn = 6; // 产品中文名称，表示商品的中文名称
    string skuImage = 7; // SKU图
    string goodsImage = 8; // 商品主图
    string supplyLink = 9; // 1688货源链接
    repeated basis.SpecInfoModel specsInfo = 10; //规格信息
    int32 buyQuantity = 12; // 下单数量，表示客户希望购买的数量
    string goodsPriceUnitNameCn = 13; // 商品计价单位名称中文
    double purchaseTargetPrice = 14; // 采购目标单价(人民币)(不高于），表示采购的最高单价限制

    int32 packingCount = 15; // 装箱数，表示每个包装箱中包含的商品数量
    double goodsLength = 16; // 商品长（单位：cm）
    double goodsWidth = 17; // 商品宽（单位：cm）
    double goodsHeight = 18; // 商品高（单位：cm）
    double dimensionCm = 19; // 长*宽*高(立方米)总数：单箱体积
    double goodsWeight = 20; // 重量（单位：kg）
    int32 stockAvailability = 22; // 有无现货，表示商品是否有现货(1:有 0:无)
    double shippingCostPerBox = 23; // 单箱运费(元)，表示每个包装箱的运输费用
    string purchaseRemarks = 24; // 采购备注，用于记录采购过程中的额外信息或说明
    GoodsNewInquiryModel goodsNewInquiry = 25;//商品最新一次询价信息
    SkuStatus skuStatus = 26;// SKU状态, 0未询，1已询
    string goodsId = 27; // 商品id
    string goodsNo = 28; // 商品编号

}

//商品最新一次询价信息
message GoodsNewInquiryModel {
    string id = 4; // sku装箱规格主键ID
    int64 inquiryFinshTime = 5; // 询价完成时间
    int32 packingCount = 14; // 装箱数，表示每个包装箱中包含的商品数量
    double goodsLength = 15; // 商品长（单位：cm）
    double goodsWidth = 16; // 商品宽（单位：cm）
    double goodsHeight = 17; // 商品高（单位：cm）
    double dimensionCm = 18; // 长*宽*高(立方米)，单箱体积
    double goodsWeight = 19; // 重量（单位：kg）
    int32 stockAvailability = 21; // 有无现货，表示商品是否有现货(1:有 0:无)
    double shippingCostPerBox = 22; // 单箱运费(元)，表示每个包装箱的运输费用
    string purchaseRemarks = 23; // 采购备注，用于记录采购过程中的额外信息或说明
}
//询价后台操作日志
message SystemInquiryModel {
    int64 cdate = 7; //操作时间
    string coperator = 8; //操作人
    InquiryStatus inquiryStatus = 9;//询价单状态（未询价：NO_INQUIRY  询价中：PROGRESS_INQUIRY 已询价：HAS_INQUIRY）
    string remark = 10; //操作内容
}

//店铺信息
message StoreModel {
    int64 inquiryStatusCount = 2; // 未询价数
    string storeName = 17; // 店铺名称，表示具体参与询价的店铺名称
    string storeId = 18; // 店铺id
    repeated StoreDetailModel storeDetailList = 19;//店铺详情
}
