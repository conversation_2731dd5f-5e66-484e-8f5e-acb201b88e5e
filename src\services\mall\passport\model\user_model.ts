/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import {
  CouponTypeStatus,
  couponTypeStatusFromJSON,
  couponTypeStatusToJSON,
} from "../../../chilat/coupon/coupon_common";
import { Page, Result } from "../../../common";
import {
  AddressLabel,
  addressLabelFromJSON,
  addressLabelToJSON,
  VerifyMailResultEnum,
  verifyMailResultEnumFromJSON,
  verifyMailResultEnumToJSON,
  VerifyMailSceneEnum,
  verifyMailSceneEnumFromJSON,
  verifyMailSceneEnumToJSON,
} from "../../../common/business";
import Long from "long";

export const protobufPackage = "mall.passport";

export interface UserDetailResp {
  result: Result | undefined;
  data: UserModel | undefined;
}

export interface UserModel {
  id: string;
  /** 姓名 */
  realName: string;
  /** 邮箱 */
  email: string;
  countryId: string;
  /** 国家 */
  countryName: string;
  whatsapp: string;
  /** 邀请码 */
  inviteCode: string;
  /** 询盘总数量 */
  inquiryCount: number;
}

export interface UserAddressResp {
  result: Result | undefined;
  data: UserAddressModel[];
}

export interface UserAddressModel {
  id: string;
  /** 国家 */
  countryId: string;
  /** 国家名称 */
  countryName: string;
  /** 省 */
  provinceCode: string;
  /** 省 */
  province: string;
  /** 市 */
  cityCode: string;
  /** 市 */
  city: string;
  /** 区 */
  regionCode: string;
  /** 区 */
  region: string;
  /** 详细地址 */
  address: string;
  /** 门牌号 */
  houseNo: string;
  /** 邮编 */
  postcode: string;
  /** 联系人 */
  contactName: string;
  /** 手机号 */
  phone: string;
  /** 是否是默认地址 */
  isDefault: boolean;
  /** 参考地标 */
  referLandmark: string;
  /** 完整地址 */
  fullAddress: string;
  /** 地址标签 */
  addressLabel: AddressLabel;
  street: string;
}

export interface SendVerifyMailResp {
  result: Result | undefined;
  data: SendVerifyMailRespModel | undefined;
}

export interface SendVerifyMailRespModel {
  isMailVerified: boolean;
}

export interface QueryVerifyMailResultResp {
  result: Result | undefined;
  data: QueryVerifyMailResultModel | undefined;
}

export interface QueryVerifyMailResultModel {
  /** 是否邮箱已激活 */
  isMailVerified: boolean;
  /** 优惠券列表 */
  couponList: CouponModel[];
  /** 验证邮件里的链接有效期 */
  expireHour: number;
}

export interface CouponModel {
  /** 券ID */
  couponId: string;
  /** 券名称 */
  couponName: string;
  /** 数量 */
  count: number;
  /** 券类型 */
  couponType: CouponTypeStatus;
}

export interface VerifyMailResp {
  result: Result | undefined;
  data: VerifyMailModel | undefined;
}

export interface VerifyMailModel {
  verifyResult: VerifyMailResultEnum;
  scene: VerifyMailSceneEnum;
}

export interface InvitedUserMailStatusResp {
  result: Result | undefined;
  page:
    | Page
    | undefined;
  /** 用户列表 */
  data: UserBasicModel[];
}

export interface UserBasicModel {
  /** 邮箱 */
  email: string;
  /** 注册时间, 毫秒级时间戳 */
  registerTime: number;
}

function createBaseUserDetailResp(): UserDetailResp {
  return { result: undefined, data: undefined };
}

export const UserDetailResp = {
  encode(message: UserDetailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      UserModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserDetailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDetailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = UserModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDetailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? UserModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: UserDetailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = UserModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDetailResp>, I>>(base?: I): UserDetailResp {
    return UserDetailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDetailResp>, I>>(object: I): UserDetailResp {
    const message = createBaseUserDetailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null) ? UserModel.fromPartial(object.data) : undefined;
    return message;
  },
};

function createBaseUserModel(): UserModel {
  return {
    id: "",
    realName: "",
    email: "",
    countryId: "",
    countryName: "",
    whatsapp: "",
    inviteCode: "",
    inquiryCount: 0,
  };
}

export const UserModel = {
  encode(message: UserModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.realName !== "") {
      writer.uint32(18).string(message.realName);
    }
    if (message.email !== "") {
      writer.uint32(26).string(message.email);
    }
    if (message.countryId !== "") {
      writer.uint32(34).string(message.countryId);
    }
    if (message.countryName !== "") {
      writer.uint32(42).string(message.countryName);
    }
    if (message.whatsapp !== "") {
      writer.uint32(50).string(message.whatsapp);
    }
    if (message.inviteCode !== "") {
      writer.uint32(58).string(message.inviteCode);
    }
    if (message.inquiryCount !== 0) {
      writer.uint32(64).int32(message.inquiryCount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.realName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.email = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.countryId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.countryName = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.whatsapp = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.inviteCode = reader.string();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.inquiryCount = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      realName: isSet(object.realName) ? globalThis.String(object.realName) : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      countryId: isSet(object.countryId) ? globalThis.String(object.countryId) : "",
      countryName: isSet(object.countryName) ? globalThis.String(object.countryName) : "",
      whatsapp: isSet(object.whatsapp) ? globalThis.String(object.whatsapp) : "",
      inviteCode: isSet(object.inviteCode) ? globalThis.String(object.inviteCode) : "",
      inquiryCount: isSet(object.inquiryCount) ? globalThis.Number(object.inquiryCount) : 0,
    };
  },

  toJSON(message: UserModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.realName !== "") {
      obj.realName = message.realName;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.countryId !== "") {
      obj.countryId = message.countryId;
    }
    if (message.countryName !== "") {
      obj.countryName = message.countryName;
    }
    if (message.whatsapp !== "") {
      obj.whatsapp = message.whatsapp;
    }
    if (message.inviteCode !== "") {
      obj.inviteCode = message.inviteCode;
    }
    if (message.inquiryCount !== 0) {
      obj.inquiryCount = Math.round(message.inquiryCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserModel>, I>>(base?: I): UserModel {
    return UserModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserModel>, I>>(object: I): UserModel {
    const message = createBaseUserModel();
    message.id = object.id ?? "";
    message.realName = object.realName ?? "";
    message.email = object.email ?? "";
    message.countryId = object.countryId ?? "";
    message.countryName = object.countryName ?? "";
    message.whatsapp = object.whatsapp ?? "";
    message.inviteCode = object.inviteCode ?? "";
    message.inquiryCount = object.inquiryCount ?? 0;
    return message;
  },
};

function createBaseUserAddressResp(): UserAddressResp {
  return { result: undefined, data: [] };
}

export const UserAddressResp = {
  encode(message: UserAddressResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      UserAddressModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserAddressResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserAddressResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(UserAddressModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserAddressResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => UserAddressModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: UserAddressResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => UserAddressModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserAddressResp>, I>>(base?: I): UserAddressResp {
    return UserAddressResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserAddressResp>, I>>(object: I): UserAddressResp {
    const message = createBaseUserAddressResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => UserAddressModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserAddressModel(): UserAddressModel {
  return {
    id: "",
    countryId: "",
    countryName: "",
    provinceCode: "",
    province: "",
    cityCode: "",
    city: "",
    regionCode: "",
    region: "",
    address: "",
    houseNo: "",
    postcode: "",
    contactName: "",
    phone: "",
    isDefault: false,
    referLandmark: "",
    fullAddress: "",
    addressLabel: 0,
    street: "",
  };
}

export const UserAddressModel = {
  encode(message: UserAddressModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.countryId !== "") {
      writer.uint32(18).string(message.countryId);
    }
    if (message.countryName !== "") {
      writer.uint32(26).string(message.countryName);
    }
    if (message.provinceCode !== "") {
      writer.uint32(34).string(message.provinceCode);
    }
    if (message.province !== "") {
      writer.uint32(42).string(message.province);
    }
    if (message.cityCode !== "") {
      writer.uint32(50).string(message.cityCode);
    }
    if (message.city !== "") {
      writer.uint32(58).string(message.city);
    }
    if (message.regionCode !== "") {
      writer.uint32(66).string(message.regionCode);
    }
    if (message.region !== "") {
      writer.uint32(74).string(message.region);
    }
    if (message.address !== "") {
      writer.uint32(82).string(message.address);
    }
    if (message.houseNo !== "") {
      writer.uint32(90).string(message.houseNo);
    }
    if (message.postcode !== "") {
      writer.uint32(98).string(message.postcode);
    }
    if (message.contactName !== "") {
      writer.uint32(106).string(message.contactName);
    }
    if (message.phone !== "") {
      writer.uint32(114).string(message.phone);
    }
    if (message.isDefault !== false) {
      writer.uint32(120).bool(message.isDefault);
    }
    if (message.referLandmark !== "") {
      writer.uint32(130).string(message.referLandmark);
    }
    if (message.fullAddress !== "") {
      writer.uint32(138).string(message.fullAddress);
    }
    if (message.addressLabel !== 0) {
      writer.uint32(144).int32(message.addressLabel);
    }
    if (message.street !== "") {
      writer.uint32(154).string(message.street);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserAddressModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserAddressModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.countryId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.countryName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.provinceCode = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.province = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.cityCode = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.city = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.regionCode = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.region = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.address = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.houseNo = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.postcode = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.contactName = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.phone = reader.string();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.isDefault = reader.bool();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.referLandmark = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.fullAddress = reader.string();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.addressLabel = reader.int32() as any;
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.street = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserAddressModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      countryId: isSet(object.countryId) ? globalThis.String(object.countryId) : "",
      countryName: isSet(object.countryName) ? globalThis.String(object.countryName) : "",
      provinceCode: isSet(object.provinceCode) ? globalThis.String(object.provinceCode) : "",
      province: isSet(object.province) ? globalThis.String(object.province) : "",
      cityCode: isSet(object.cityCode) ? globalThis.String(object.cityCode) : "",
      city: isSet(object.city) ? globalThis.String(object.city) : "",
      regionCode: isSet(object.regionCode) ? globalThis.String(object.regionCode) : "",
      region: isSet(object.region) ? globalThis.String(object.region) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      houseNo: isSet(object.houseNo) ? globalThis.String(object.houseNo) : "",
      postcode: isSet(object.postcode) ? globalThis.String(object.postcode) : "",
      contactName: isSet(object.contactName) ? globalThis.String(object.contactName) : "",
      phone: isSet(object.phone) ? globalThis.String(object.phone) : "",
      isDefault: isSet(object.isDefault) ? globalThis.Boolean(object.isDefault) : false,
      referLandmark: isSet(object.referLandmark) ? globalThis.String(object.referLandmark) : "",
      fullAddress: isSet(object.fullAddress) ? globalThis.String(object.fullAddress) : "",
      addressLabel: isSet(object.addressLabel) ? addressLabelFromJSON(object.addressLabel) : 0,
      street: isSet(object.street) ? globalThis.String(object.street) : "",
    };
  },

  toJSON(message: UserAddressModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.countryId !== "") {
      obj.countryId = message.countryId;
    }
    if (message.countryName !== "") {
      obj.countryName = message.countryName;
    }
    if (message.provinceCode !== "") {
      obj.provinceCode = message.provinceCode;
    }
    if (message.province !== "") {
      obj.province = message.province;
    }
    if (message.cityCode !== "") {
      obj.cityCode = message.cityCode;
    }
    if (message.city !== "") {
      obj.city = message.city;
    }
    if (message.regionCode !== "") {
      obj.regionCode = message.regionCode;
    }
    if (message.region !== "") {
      obj.region = message.region;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.houseNo !== "") {
      obj.houseNo = message.houseNo;
    }
    if (message.postcode !== "") {
      obj.postcode = message.postcode;
    }
    if (message.contactName !== "") {
      obj.contactName = message.contactName;
    }
    if (message.phone !== "") {
      obj.phone = message.phone;
    }
    if (message.isDefault !== false) {
      obj.isDefault = message.isDefault;
    }
    if (message.referLandmark !== "") {
      obj.referLandmark = message.referLandmark;
    }
    if (message.fullAddress !== "") {
      obj.fullAddress = message.fullAddress;
    }
    if (message.addressLabel !== 0) {
      obj.addressLabel = addressLabelToJSON(message.addressLabel);
    }
    if (message.street !== "") {
      obj.street = message.street;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserAddressModel>, I>>(base?: I): UserAddressModel {
    return UserAddressModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserAddressModel>, I>>(object: I): UserAddressModel {
    const message = createBaseUserAddressModel();
    message.id = object.id ?? "";
    message.countryId = object.countryId ?? "";
    message.countryName = object.countryName ?? "";
    message.provinceCode = object.provinceCode ?? "";
    message.province = object.province ?? "";
    message.cityCode = object.cityCode ?? "";
    message.city = object.city ?? "";
    message.regionCode = object.regionCode ?? "";
    message.region = object.region ?? "";
    message.address = object.address ?? "";
    message.houseNo = object.houseNo ?? "";
    message.postcode = object.postcode ?? "";
    message.contactName = object.contactName ?? "";
    message.phone = object.phone ?? "";
    message.isDefault = object.isDefault ?? false;
    message.referLandmark = object.referLandmark ?? "";
    message.fullAddress = object.fullAddress ?? "";
    message.addressLabel = object.addressLabel ?? 0;
    message.street = object.street ?? "";
    return message;
  },
};

function createBaseSendVerifyMailResp(): SendVerifyMailResp {
  return { result: undefined, data: undefined };
}

export const SendVerifyMailResp = {
  encode(message: SendVerifyMailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      SendVerifyMailRespModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SendVerifyMailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendVerifyMailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = SendVerifyMailRespModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendVerifyMailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? SendVerifyMailRespModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: SendVerifyMailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = SendVerifyMailRespModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendVerifyMailResp>, I>>(base?: I): SendVerifyMailResp {
    return SendVerifyMailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendVerifyMailResp>, I>>(object: I): SendVerifyMailResp {
    const message = createBaseSendVerifyMailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? SendVerifyMailRespModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseSendVerifyMailRespModel(): SendVerifyMailRespModel {
  return { isMailVerified: false };
}

export const SendVerifyMailRespModel = {
  encode(message: SendVerifyMailRespModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.isMailVerified !== false) {
      writer.uint32(8).bool(message.isMailVerified);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SendVerifyMailRespModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendVerifyMailRespModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.isMailVerified = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendVerifyMailRespModel {
    return { isMailVerified: isSet(object.isMailVerified) ? globalThis.Boolean(object.isMailVerified) : false };
  },

  toJSON(message: SendVerifyMailRespModel): unknown {
    const obj: any = {};
    if (message.isMailVerified !== false) {
      obj.isMailVerified = message.isMailVerified;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendVerifyMailRespModel>, I>>(base?: I): SendVerifyMailRespModel {
    return SendVerifyMailRespModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendVerifyMailRespModel>, I>>(object: I): SendVerifyMailRespModel {
    const message = createBaseSendVerifyMailRespModel();
    message.isMailVerified = object.isMailVerified ?? false;
    return message;
  },
};

function createBaseQueryVerifyMailResultResp(): QueryVerifyMailResultResp {
  return { result: undefined, data: undefined };
}

export const QueryVerifyMailResultResp = {
  encode(message: QueryVerifyMailResultResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      QueryVerifyMailResultModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryVerifyMailResultResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryVerifyMailResultResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = QueryVerifyMailResultModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryVerifyMailResultResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? QueryVerifyMailResultModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: QueryVerifyMailResultResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = QueryVerifyMailResultModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryVerifyMailResultResp>, I>>(base?: I): QueryVerifyMailResultResp {
    return QueryVerifyMailResultResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryVerifyMailResultResp>, I>>(object: I): QueryVerifyMailResultResp {
    const message = createBaseQueryVerifyMailResultResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? QueryVerifyMailResultModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseQueryVerifyMailResultModel(): QueryVerifyMailResultModel {
  return { isMailVerified: false, couponList: [], expireHour: 0 };
}

export const QueryVerifyMailResultModel = {
  encode(message: QueryVerifyMailResultModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.isMailVerified !== false) {
      writer.uint32(80).bool(message.isMailVerified);
    }
    for (const v of message.couponList) {
      CouponModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    if (message.expireHour !== 0) {
      writer.uint32(240).int32(message.expireHour);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryVerifyMailResultModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryVerifyMailResultModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.isMailVerified = reader.bool();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.couponList.push(CouponModel.decode(reader, reader.uint32()));
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.expireHour = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryVerifyMailResultModel {
    return {
      isMailVerified: isSet(object.isMailVerified) ? globalThis.Boolean(object.isMailVerified) : false,
      couponList: globalThis.Array.isArray(object?.couponList)
        ? object.couponList.map((e: any) => CouponModel.fromJSON(e))
        : [],
      expireHour: isSet(object.expireHour) ? globalThis.Number(object.expireHour) : 0,
    };
  },

  toJSON(message: QueryVerifyMailResultModel): unknown {
    const obj: any = {};
    if (message.isMailVerified !== false) {
      obj.isMailVerified = message.isMailVerified;
    }
    if (message.couponList?.length) {
      obj.couponList = message.couponList.map((e) => CouponModel.toJSON(e));
    }
    if (message.expireHour !== 0) {
      obj.expireHour = Math.round(message.expireHour);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryVerifyMailResultModel>, I>>(base?: I): QueryVerifyMailResultModel {
    return QueryVerifyMailResultModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryVerifyMailResultModel>, I>>(object: I): QueryVerifyMailResultModel {
    const message = createBaseQueryVerifyMailResultModel();
    message.isMailVerified = object.isMailVerified ?? false;
    message.couponList = object.couponList?.map((e) => CouponModel.fromPartial(e)) || [];
    message.expireHour = object.expireHour ?? 0;
    return message;
  },
};

function createBaseCouponModel(): CouponModel {
  return { couponId: "", couponName: "", count: 0, couponType: 0 };
}

export const CouponModel = {
  encode(message: CouponModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.couponId !== "") {
      writer.uint32(82).string(message.couponId);
    }
    if (message.couponName !== "") {
      writer.uint32(162).string(message.couponName);
    }
    if (message.count !== 0) {
      writer.uint32(240).int32(message.count);
    }
    if (message.couponType !== 0) {
      writer.uint32(320).int32(message.couponType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.couponId = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.couponName = reader.string();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.count = reader.int32();
          continue;
        case 40:
          if (tag !== 320) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponModel {
    return {
      couponId: isSet(object.couponId) ? globalThis.String(object.couponId) : "",
      couponName: isSet(object.couponName) ? globalThis.String(object.couponName) : "",
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
    };
  },

  toJSON(message: CouponModel): unknown {
    const obj: any = {};
    if (message.couponId !== "") {
      obj.couponId = message.couponId;
    }
    if (message.couponName !== "") {
      obj.couponName = message.couponName;
    }
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponModel>, I>>(base?: I): CouponModel {
    return CouponModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponModel>, I>>(object: I): CouponModel {
    const message = createBaseCouponModel();
    message.couponId = object.couponId ?? "";
    message.couponName = object.couponName ?? "";
    message.count = object.count ?? 0;
    message.couponType = object.couponType ?? 0;
    return message;
  },
};

function createBaseVerifyMailResp(): VerifyMailResp {
  return { result: undefined, data: undefined };
}

export const VerifyMailResp = {
  encode(message: VerifyMailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      VerifyMailModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): VerifyMailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyMailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = VerifyMailModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VerifyMailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? VerifyMailModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: VerifyMailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = VerifyMailModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VerifyMailResp>, I>>(base?: I): VerifyMailResp {
    return VerifyMailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyMailResp>, I>>(object: I): VerifyMailResp {
    const message = createBaseVerifyMailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? VerifyMailModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseVerifyMailModel(): VerifyMailModel {
  return { verifyResult: 0, scene: 0 };
}

export const VerifyMailModel = {
  encode(message: VerifyMailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.verifyResult !== 0) {
      writer.uint32(8).int32(message.verifyResult);
    }
    if (message.scene !== 0) {
      writer.uint32(16).int32(message.scene);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): VerifyMailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyMailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.verifyResult = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.scene = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VerifyMailModel {
    return {
      verifyResult: isSet(object.verifyResult) ? verifyMailResultEnumFromJSON(object.verifyResult) : 0,
      scene: isSet(object.scene) ? verifyMailSceneEnumFromJSON(object.scene) : 0,
    };
  },

  toJSON(message: VerifyMailModel): unknown {
    const obj: any = {};
    if (message.verifyResult !== 0) {
      obj.verifyResult = verifyMailResultEnumToJSON(message.verifyResult);
    }
    if (message.scene !== 0) {
      obj.scene = verifyMailSceneEnumToJSON(message.scene);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VerifyMailModel>, I>>(base?: I): VerifyMailModel {
    return VerifyMailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyMailModel>, I>>(object: I): VerifyMailModel {
    const message = createBaseVerifyMailModel();
    message.verifyResult = object.verifyResult ?? 0;
    message.scene = object.scene ?? 0;
    return message;
  },
};

function createBaseInvitedUserMailStatusResp(): InvitedUserMailStatusResp {
  return { result: undefined, page: undefined, data: [] };
}

export const InvitedUserMailStatusResp = {
  encode(message: InvitedUserMailStatusResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      UserBasicModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): InvitedUserMailStatusResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvitedUserMailStatusResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(UserBasicModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvitedUserMailStatusResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => UserBasicModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: InvitedUserMailStatusResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => UserBasicModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvitedUserMailStatusResp>, I>>(base?: I): InvitedUserMailStatusResp {
    return InvitedUserMailStatusResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvitedUserMailStatusResp>, I>>(object: I): InvitedUserMailStatusResp {
    const message = createBaseInvitedUserMailStatusResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => UserBasicModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserBasicModel(): UserBasicModel {
  return { email: "", registerTime: 0 };
}

export const UserBasicModel = {
  encode(message: UserBasicModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.email !== "") {
      writer.uint32(82).string(message.email);
    }
    if (message.registerTime !== 0) {
      writer.uint32(160).int64(message.registerTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserBasicModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserBasicModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.email = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.registerTime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserBasicModel {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      registerTime: isSet(object.registerTime) ? globalThis.Number(object.registerTime) : 0,
    };
  },

  toJSON(message: UserBasicModel): unknown {
    const obj: any = {};
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.registerTime !== 0) {
      obj.registerTime = Math.round(message.registerTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserBasicModel>, I>>(base?: I): UserBasicModel {
    return UserBasicModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBasicModel>, I>>(object: I): UserBasicModel {
    const message = createBaseUserBasicModel();
    message.email = object.email ?? "";
    message.registerTime = object.registerTime ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
