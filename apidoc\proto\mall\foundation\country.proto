syntax = "proto3";
package mall.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/basis/model/country_model.proto";
import "chilat/basis/param/country_param.proto";
import "common.proto";

// 字典服务
service Country {
  // 查询所有国家
  rpc listAll(common.EmptyParam) returns (chilat.basis.CountryResp);
  // 根据国家查询地区
  rpc listRegionByCountry(common.IdParam) returns (chilat.basis.ListRegionResp);
  // 根据邮编查询地区
  rpc listRegionByPostcode(chilat.basis.ListRegionByPostcodeParam) returns (chilat.basis.ListRegionResp);
}