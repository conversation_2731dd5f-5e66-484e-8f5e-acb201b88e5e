<template>
  <div class="page-wrapper">
    <!-- 搜索 -->
    <search-cate-card />
    <div
      class="w-[740px] mx-auto pt-[34px] flex-1 flex flex-col overflow-hidden"
    >
      <div
        class="text-[50px] leading-[50px] medium border-b-1 border-[#7F7F7F] pb-[24px] text-center"
      >
        {{ authStore.i18n("cm_blog_blogTitle") }}
      </div>
      <div class="pt-[42px] pb-[60px]">
        <div class="min-h-[40vh]">
          <a
            target="_blank"
            v-for="(article, index) in pageData.articleList"
            :key="article.id"
            :href="`/article?code=${article.articleCode}`"
            :class="{
              'pb-[15px] mb-[15px] border-b border-[#F2F2F2]':
                index !== pageData.articleList.length - 1,
            }"
            class="flex"
            data-spm-box="blog-article-list"
            :data-spm-index="index + 1"
          >
            <div
              class="flex-shrink-0 w-[200px] h-[132px] mr-[20px] rounded-[4px] flex justify-center items-center"
            >
              <n-image
                lazy
                preview-disabled
                :src="article?.logo"
                v-if="article?.logo"
              >
              </n-image>
              <n-image
                lazy
                v-else
                preview-disabled
                :src="configStore.pageTheme.logo"
                class="w-[168px]"
              ></n-image>
            </div>
            <n-ellipsis
              :line-clamp="2"
              :tooltip="false"
              class="text-[22px] leading-[30px] font-medium h-[fit-content]"
            >
              {{ article.title }}
            </n-ellipsis>
          </a>
        </div>

        <n-pagination
          show-size-picker
          show-quick-jumper
          :page-sizes="[5, 10, 20]"
          :item-count="pageData.pageInfo.total"
          v-model:page="pageData.pageInfo.current"
          v-model:page-size="pageData.pageInfo.size"
          :on-update:page="onUpdatePageNo"
          :on-update:page-size="onUpdatePageSize"
          class="mt-[60px] text-center flex justify-center"
        >
          <template #prefix="{ itemCount }">
            {{ authStore.i18n("cm_inquiry.total") }}
            {{ itemCount }}
          </template>
          <template #goto>{{ authStore.i18n("cm_inquiry.jumpTo") }}</template>
        </n-pagination>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const authStore = useAuthStore();
const configStore = useConfigStore();

const pageData = reactive(<any>{
  articleList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 5,
    total: 0,
  },
});

onGetBlogList();
async function onGetBlogList() {
  const res: any = await useGetBlogList({
    page: pageData.pageInfo,
    deviceType: "VISIT_DEVICE_TYPE_PC",
  });
  if (res?.result?.code === 200) {
    pageData.articleList = res?.data;
    pageData.pageInfo = res?.page;
  } else {
    showToast(res.result?.message);
  }
}

function onUpdatePageNo(page: number) {
  pageData.pageInfo.current = page;
  onGetBlogList();
}

function onUpdatePageSize(pageSize: number) {
  pageData.pageInfo.current = 1;
  pageData.pageInfo.size = pageSize;
  onGetBlogList();
}
</script>

<style scoped lang="scss">
.page-wrapper {
  min-height: 80vh;
  width: 100%;
}
</style>
