syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";

// 规格列表参数
message GoodsSpecPageQueryParam {
  common.PageParam page = 1;
  GoodsSpecQueryParam query = 2;
}

// 规格列表查询参数
message GoodsSpecQueryParam {
  string id = 1; // 规格ID
  string specName = 2; // 开始创建时间
  string categoryId = 3; //分类ID
}

// 规格保存信息
message GoodsSpecSaveParam {
  string id = 1; //规格ID
  int32 idx = 2; //顺序
  string specName = 3; //规格名称
  string remark = 4; //规格备注
  bool enabled = 5; //是否启用
  repeated string categoryIds = 6; //类目id列表
}
