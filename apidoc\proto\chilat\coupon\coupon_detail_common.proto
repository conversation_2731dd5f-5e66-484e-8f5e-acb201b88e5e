syntax = "proto3";
package chilat.coupon;


option java_package = "com.chilat.rpc.coupon.common";

/*------------------------------ 业务相关公共参数 ------------------------------*/
// 优惠券主体状态
enum TicketStatus {
     TICKET_NOT_USE = 0; //未使用
     TICKET_USE = 1; //已使用
     TICKET_LOCK = 2; //锁定
     TICKET_LOSE_EFFICACY = 3; //卡券失效
     TICKET_REFUND = 4; //卡券退回（该状态只做记录 不参与业务逻辑）
}

// 优惠券发放场景状态
enum TicketSceneStatus {
     REGISTER = 0; //注册
     INVITATIN = 1; //邀约
     INVITATION_REGISTER = 2; //邀请注册
     INQUIRY = 3; //询盘
     PAYMENT = 4; //支付
     NOSCENE = 5; //无业务场景通用
     MY_COUPON_LIST = 6; //个人中心-优惠券列表
}


