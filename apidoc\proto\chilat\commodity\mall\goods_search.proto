syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/mall/goods_search_param.proto";
import "chilat/commodity/model/mall/goods_search_model.proto";
import "common.proto";

// 商城商品搜索
service GoodsSearch {
  // 上传1688图片
  rpc uploadImage1688 (common.EmptyParam) returns (GoodsSearchImageResp) {
    option (common.webapi).upload = true;
  };
  // 抓取并上架1688商品
  rpc getGoodsDetail (common.StringParam) returns (GoodsSearchQueryResp);
  // 1688图搜
  rpc pageListByImage (GoodsSearchPageQueryParam) returns (GoodsSearchPageQueryResp);
}
