syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main.param";


import "common.proto";
import "common/business.proto";

message GetCaptchaImageParam {
  string captchaPageSource = 10; //显示验证码页面的来源代码（32个字节以内，例子：notas）
}

message CheckCaptchaImageParam {
  string captchaPageSource = 10; //显示验证码页面的来源代码（32个字节以内；例子：notas；必须与GetCaptchaImageParam中的代码相同）
  string captchaCode = 20; //用户输入的验证码（验证码错误的错误码：952401，若遇到验证码错误，前端刷新验证码后重试）
}
