/* eslint-disable */

export const protobufPackage = "chilat.support";

export enum PageType {
  PAGE_TYPE_UNKNOWN = 0,
  /** PAGE_TYPE_PC - PC */
  PAGE_TYPE_PC = 1,
  /** PAGE_TYPE_H5 - H5 */
  PAGE_TYPE_H5 = 2,
  UNRECOGNIZED = -1,
}

export function pageTypeFromJSON(object: any): PageType {
  switch (object) {
    case 0:
    case "PAGE_TYPE_UNKNOWN":
      return PageType.PAGE_TYPE_UNKNOWN;
    case 1:
    case "PAGE_TYPE_PC":
      return PageType.PAGE_TYPE_PC;
    case 2:
    case "PAGE_TYPE_H5":
      return PageType.PAGE_TYPE_H5;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PageType.UNRECOGNIZED;
  }
}

export function pageTypeToJSON(object: PageType): string {
  switch (object) {
    case PageType.PAGE_TYPE_UNKNOWN:
      return "PAGE_TYPE_UNKNOWN";
    case PageType.PAGE_TYPE_PC:
      return "PAGE_TYPE_PC";
    case PageType.PAGE_TYPE_H5:
      return "PAGE_TYPE_H5";
    case PageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
