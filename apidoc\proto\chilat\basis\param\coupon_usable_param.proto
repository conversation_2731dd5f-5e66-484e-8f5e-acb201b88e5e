syntax = "proto3";

package chilat.basis;

import "chilat/basis/model/coupon_usable_model.proto";
import "chilat/coupon/coupon_common.proto";

option java_package = "com.chilat.rpc.basis.param.coupon";

//可使用优惠券参数
message CouponUsableParam {
    string userId = 1;//用户id
    string orderNo = 2;//订单号
    coupon.CouponTypeStatus couponType = 3; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
}



//检查可使用优惠券校验参数
message CouponCheckParam {
    string orderNo = 1;//订单号
    repeated CouponUsableDetailModel selectCouponModelsList = 2;//选中优惠券集合
    repeated CouponUsableDetailModel notCouponModelsList = 3;//未选中优惠券集合
    coupon.CouponTypeStatus couponType=4;//券类型
}


//订单优惠券锁定/解锁参数
message CouponOrderNoLockParam {
    string orderNo = 1;// 订单号
    string userId = 2;//用户Id
}

//订单使用优惠券参数
message CouponOrderUseParam {
    string orderNo = 1;// 订单号
}
//订单退回优惠券参数
message CouponOrderRefundParam {
    string orderNo = 1;// 订单号
}

