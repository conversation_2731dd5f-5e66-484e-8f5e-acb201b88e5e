/* eslint-disable */
import { NameValueModel } from '../common'
import * as _m0 from 'protobufjs/minimal'

export const protobufPackage = 'common'

/** 访问设备类型 */
export enum VisitDeviceType {
    VISIT_DEVICE_TYPE_UNSPECIFIED = 0,
    VISIT_DEVICE_TYPE_PC = 1,
    VISIT_DEVICE_TYPE_H5 = 2,
    UNRECOGNIZED = -1,
}

export function visitDeviceTypeFromJSON(object: any): VisitDeviceType {
    switch (object) {
        case 0:
        case 'VISIT_DEVICE_TYPE_UNSPECIFIED':
            return VisitDeviceType.VISIT_DEVICE_TYPE_UNSPECIFIED
        case 1:
        case 'VISIT_DEVICE_TYPE_PC':
            return VisitDeviceType.VISIT_DEVICE_TYPE_PC
        case 2:
        case 'VISIT_DEVICE_TYPE_H5':
            return VisitDeviceType.VISIT_DEVICE_TYPE_H5
        case -1:
        case 'UNRECOGNIZED':
        default:
            return VisitDeviceType.UNRECOGNIZED
    }
}

export function visitDeviceTypeToJSON(object: VisitDeviceType): string {
    switch (object) {
        case VisitDeviceType.VISIT_DEVICE_TYPE_UNSPECIFIED:
            return 'VISIT_DEVICE_TYPE_UNSPECIFIED'
        case VisitDeviceType.VISIT_DEVICE_TYPE_PC:
            return 'VISIT_DEVICE_TYPE_PC'
        case VisitDeviceType.VISIT_DEVICE_TYPE_H5:
            return 'VISIT_DEVICE_TYPE_H5'
        case VisitDeviceType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum CurrencyType {
    CURRENCY_TYPE_UNSPECIFIED = 0,
    /** CURRENCY_TYPE_CNY - 人民币 */
    CURRENCY_TYPE_CNY = 10,
    /** CURRENCY_TYPE_USD - 美元 */
    CURRENCY_TYPE_USD = 20,
    /** CURRENCY_TYPE_ARS - 阿根廷比索 */
    CURRENCY_TYPE_ARS = 30,
    /** CURRENCY_TYPE_BOB - 玻利维亚诺 */
    CURRENCY_TYPE_BOB = 40,
    /** CURRENCY_TYPE_CLP - 智利比索 */
    CURRENCY_TYPE_CLP = 50,
    /** CURRENCY_TYPE_COP - 哥伦比亚比索 */
    CURRENCY_TYPE_COP = 60,
    /** CURRENCY_TYPE_CRC - 哥斯达黎加科朗 */
    CURRENCY_TYPE_CRC = 70,
    /** CURRENCY_TYPE_MXN - 墨西哥比索 */
    CURRENCY_TYPE_MXN = 80,
    /** CURRENCY_TYPE_PEN - 秘鲁索尔 */
    CURRENCY_TYPE_PEN = 90,
    UNRECOGNIZED = -1,
}

export function currencyTypeFromJSON(object: any): CurrencyType {
    switch (object) {
        case 0:
        case 'CURRENCY_TYPE_UNSPECIFIED':
            return CurrencyType.CURRENCY_TYPE_UNSPECIFIED
        case 10:
        case 'CURRENCY_TYPE_CNY':
            return CurrencyType.CURRENCY_TYPE_CNY
        case 20:
        case 'CURRENCY_TYPE_USD':
            return CurrencyType.CURRENCY_TYPE_USD
        case 30:
        case 'CURRENCY_TYPE_ARS':
            return CurrencyType.CURRENCY_TYPE_ARS
        case 40:
        case 'CURRENCY_TYPE_BOB':
            return CurrencyType.CURRENCY_TYPE_BOB
        case 50:
        case 'CURRENCY_TYPE_CLP':
            return CurrencyType.CURRENCY_TYPE_CLP
        case 60:
        case 'CURRENCY_TYPE_COP':
            return CurrencyType.CURRENCY_TYPE_COP
        case 70:
        case 'CURRENCY_TYPE_CRC':
            return CurrencyType.CURRENCY_TYPE_CRC
        case 80:
        case 'CURRENCY_TYPE_MXN':
            return CurrencyType.CURRENCY_TYPE_MXN
        case 90:
        case 'CURRENCY_TYPE_PEN':
            return CurrencyType.CURRENCY_TYPE_PEN
        case -1:
        case 'UNRECOGNIZED':
        default:
            return CurrencyType.UNRECOGNIZED
    }
}

export function currencyTypeToJSON(object: CurrencyType): string {
    switch (object) {
        case CurrencyType.CURRENCY_TYPE_UNSPECIFIED:
            return 'CURRENCY_TYPE_UNSPECIFIED'
        case CurrencyType.CURRENCY_TYPE_CNY:
            return 'CURRENCY_TYPE_CNY'
        case CurrencyType.CURRENCY_TYPE_USD:
            return 'CURRENCY_TYPE_USD'
        case CurrencyType.CURRENCY_TYPE_ARS:
            return 'CURRENCY_TYPE_ARS'
        case CurrencyType.CURRENCY_TYPE_BOB:
            return 'CURRENCY_TYPE_BOB'
        case CurrencyType.CURRENCY_TYPE_CLP:
            return 'CURRENCY_TYPE_CLP'
        case CurrencyType.CURRENCY_TYPE_COP:
            return 'CURRENCY_TYPE_COP'
        case CurrencyType.CURRENCY_TYPE_CRC:
            return 'CURRENCY_TYPE_CRC'
        case CurrencyType.CURRENCY_TYPE_MXN:
            return 'CURRENCY_TYPE_MXN'
        case CurrencyType.CURRENCY_TYPE_PEN:
            return 'CURRENCY_TYPE_PEN'
        case CurrencyType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum AddressLabel {
    /** ADDRESS_LABEL_HOME - 家 */
    ADDRESS_LABEL_HOME = 0,
    /** ADDRESS_LABEL_COMPANY - 公司 */
    ADDRESS_LABEL_COMPANY = 1,
    UNRECOGNIZED = -1,
}

export function addressLabelFromJSON(object: any): AddressLabel {
    switch (object) {
        case 0:
        case 'ADDRESS_LABEL_HOME':
            return AddressLabel.ADDRESS_LABEL_HOME
        case 1:
        case 'ADDRESS_LABEL_COMPANY':
            return AddressLabel.ADDRESS_LABEL_COMPANY
        case -1:
        case 'UNRECOGNIZED':
        default:
            return AddressLabel.UNRECOGNIZED
    }
}

export function addressLabelToJSON(object: AddressLabel): string {
    switch (object) {
        case AddressLabel.ADDRESS_LABEL_HOME:
            return 'ADDRESS_LABEL_HOME'
        case AddressLabel.ADDRESS_LABEL_COMPANY:
            return 'ADDRESS_LABEL_COMPANY'
        case AddressLabel.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum TimeInterval {
    TIME_INTERVAL_YESTERDAY = 0,
    TIME_INTERVAL_TODAY = 1,
    TIME_INTERVAL_THREE_DAY = 2,
    TIME_INTERVAL_WEEK = 3,
    TIME_INTERVAL_MONTH = 4,
    TIME_INTERVAL_QUARTER = 5,
    UNRECOGNIZED = -1,
}

export function timeIntervalFromJSON(object: any): TimeInterval {
    switch (object) {
        case 0:
        case 'TIME_INTERVAL_YESTERDAY':
            return TimeInterval.TIME_INTERVAL_YESTERDAY
        case 1:
        case 'TIME_INTERVAL_TODAY':
            return TimeInterval.TIME_INTERVAL_TODAY
        case 2:
        case 'TIME_INTERVAL_THREE_DAY':
            return TimeInterval.TIME_INTERVAL_THREE_DAY
        case 3:
        case 'TIME_INTERVAL_WEEK':
            return TimeInterval.TIME_INTERVAL_WEEK
        case 4:
        case 'TIME_INTERVAL_MONTH':
            return TimeInterval.TIME_INTERVAL_MONTH
        case 5:
        case 'TIME_INTERVAL_QUARTER':
            return TimeInterval.TIME_INTERVAL_QUARTER
        case -1:
        case 'UNRECOGNIZED':
        default:
            return TimeInterval.UNRECOGNIZED
    }
}

export function timeIntervalToJSON(object: TimeInterval): string {
    switch (object) {
        case TimeInterval.TIME_INTERVAL_YESTERDAY:
            return 'TIME_INTERVAL_YESTERDAY'
        case TimeInterval.TIME_INTERVAL_TODAY:
            return 'TIME_INTERVAL_TODAY'
        case TimeInterval.TIME_INTERVAL_THREE_DAY:
            return 'TIME_INTERVAL_THREE_DAY'
        case TimeInterval.TIME_INTERVAL_WEEK:
            return 'TIME_INTERVAL_WEEK'
        case TimeInterval.TIME_INTERVAL_MONTH:
            return 'TIME_INTERVAL_MONTH'
        case TimeInterval.TIME_INTERVAL_QUARTER:
            return 'TIME_INTERVAL_QUARTER'
        case TimeInterval.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 报价模式 */
export enum QuotationMode {
    QUOTATION_MODE_DDP = 0,
    QUOTATION_MODE_EXW = 1,
    QUOTATION_MODE_FOB = 2,
    QUOTATION_MODE_LCL = 3,
    QUOTATION_MODE_CIF = 4,
    UNRECOGNIZED = -1,
}

export function quotationModeFromJSON(object: any): QuotationMode {
    switch (object) {
        case 0:
        case 'QUOTATION_MODE_DDP':
            return QuotationMode.QUOTATION_MODE_DDP
        case 1:
        case 'QUOTATION_MODE_EXW':
            return QuotationMode.QUOTATION_MODE_EXW
        case 2:
        case 'QUOTATION_MODE_FOB':
            return QuotationMode.QUOTATION_MODE_FOB
        case 3:
        case 'QUOTATION_MODE_LCL':
            return QuotationMode.QUOTATION_MODE_LCL
        case 4:
        case 'QUOTATION_MODE_CIF':
            return QuotationMode.QUOTATION_MODE_CIF
        case -1:
        case 'UNRECOGNIZED':
        default:
            return QuotationMode.UNRECOGNIZED
    }
}

export function quotationModeToJSON(object: QuotationMode): string {
    switch (object) {
        case QuotationMode.QUOTATION_MODE_DDP:
            return 'QUOTATION_MODE_DDP'
        case QuotationMode.QUOTATION_MODE_EXW:
            return 'QUOTATION_MODE_EXW'
        case QuotationMode.QUOTATION_MODE_FOB:
            return 'QUOTATION_MODE_FOB'
        case QuotationMode.QUOTATION_MODE_LCL:
            return 'QUOTATION_MODE_LCL'
        case QuotationMode.QUOTATION_MODE_CIF:
            return 'QUOTATION_MODE_CIF'
        case QuotationMode.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 支付模式 */
export enum PayMode {
    /** PAY_MODE_ALL - 一次性支付 */
    PAY_MODE_ALL = 0,
    /** PAY_MODE_PART - 分开支付 */
    PAY_MODE_PART = 1,
    UNRECOGNIZED = -1,
}

export function payModeFromJSON(object: any): PayMode {
    switch (object) {
        case 0:
        case 'PAY_MODE_ALL':
            return PayMode.PAY_MODE_ALL
        case 1:
        case 'PAY_MODE_PART':
            return PayMode.PAY_MODE_PART
        case -1:
        case 'UNRECOGNIZED':
        default:
            return PayMode.UNRECOGNIZED
    }
}

export function payModeToJSON(object: PayMode): string {
    switch (object) {
        case PayMode.PAY_MODE_ALL:
            return 'PAY_MODE_ALL'
        case PayMode.PAY_MODE_PART:
            return 'PAY_MODE_PART'
        case PayMode.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 线上或者线下支付 */
export enum PayType {
    /** ONLINE_PAY_ONLINE - 线上支付 */
    ONLINE_PAY_ONLINE = 0,
    /** ONLINE_PAY_OFFLINE - 线下支付 */
    ONLINE_PAY_OFFLINE = 1,
    UNRECOGNIZED = -1,
}

export function payTypeFromJSON(object: any): PayType {
    switch (object) {
        case 0:
        case 'ONLINE_PAY_ONLINE':
            return PayType.ONLINE_PAY_ONLINE
        case 1:
        case 'ONLINE_PAY_OFFLINE':
            return PayType.ONLINE_PAY_OFFLINE
        case -1:
        case 'UNRECOGNIZED':
        default:
            return PayType.UNRECOGNIZED
    }
}

export function payTypeToJSON(object: PayType): string {
    switch (object) {
        case PayType.ONLINE_PAY_ONLINE:
            return 'ONLINE_PAY_ONLINE'
        case PayType.ONLINE_PAY_OFFLINE:
            return 'ONLINE_PAY_OFFLINE'
        case PayType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 线上或者线下支付 */
export enum FeeType {
    /** ALL_FEE - 全部 */
    ALL_FEE = 0,
    /** DOMESTIC_FEE - 国内费用 */
    DOMESTIC_FEE = 1,
    /** OVERSEAS_FEE - 国外费用 */
    OVERSEAS_FEE = 2,
    UNRECOGNIZED = -1,
}

export function feeTypeFromJSON(object: any): FeeType {
    switch (object) {
        case 0:
        case 'ALL_FEE':
            return FeeType.ALL_FEE
        case 1:
        case 'DOMESTIC_FEE':
            return FeeType.DOMESTIC_FEE
        case 2:
        case 'OVERSEAS_FEE':
            return FeeType.OVERSEAS_FEE
        case -1:
        case 'UNRECOGNIZED':
        default:
            return FeeType.UNRECOGNIZED
    }
}

export function feeTypeToJSON(object: FeeType): string {
    switch (object) {
        case FeeType.ALL_FEE:
            return 'ALL_FEE'
        case FeeType.DOMESTIC_FEE:
            return 'DOMESTIC_FEE'
        case FeeType.OVERSEAS_FEE:
            return 'OVERSEAS_FEE'
        case FeeType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/**
 * 订单类型
 * 状态： -100.审核前取消， 0.待审核 100.审核通过 200.部分支付 300.已支付产品成本 400.三方下单成功 500.三放已发货 600.已收货 700.已填写国际运费 800.支付国际运费(代发货) 900.已发货 1000.清关报关. 1100. 用户签收 1200.审核后取消 1300.用户已取消
 */
export enum OrderStatus {
    STATUS_ALL = 0,
    /** CANCEL_BEFORE_AUDIT - 1.审核前取消 */
    CANCEL_BEFORE_AUDIT = 1,
    /** WAITING_APPROVING - 0.待审核 */
    WAITING_APPROVING = 2,
    /** APP_APPROVED - 100.审核通过 */
    APP_APPROVED = 3,
    /** PARTIAL_PAYMENT - 200.部分支付 */
    PARTIAL_PAYMENT = 4,
    /** PAID_PRODUCT - 300.待采购 */
    PAID_PRODUCT = 5,
    /** THIRD_PARTY_ORDER_SUCCESS - 待工厂发货 */
    THIRD_PARTY_ORDER_SUCCESS = 6,
    /** THIRD_PARTY_SHIPPED - 已发货待收货 */
    THIRD_PARTY_SHIPPED = 7,
    /** RECEIVED - 待客户验货 */
    RECEIVED = 8,
    /** FILLED_IN_INTERNATIONAL_FREIGHT - 待支付国际费用 */
    FILLED_IN_INTERNATIONAL_FREIGHT = 9,
    /** PAY_INTERNATIONAL_FREIGHT - 待发货 */
    PAY_INTERNATIONAL_FREIGHT = 10,
    /** SHIPPED - 国际运输中 */
    SHIPPED = 11,
    /** CUSTOMS_DECLARATION - 当地派送中 */
    CUSTOMS_DECLARATION = 12,
    /** USER_SIGNED - 已签收 */
    USER_SIGNED = 13,
    /** CANCEL_AFTER_AUDIT - 2.审核后取消 */
    CANCEL_AFTER_AUDIT = 14,
    /** USER_CANCEL - 3.用户已取消 */
    USER_CANCEL = 15,
    /** WAITING_COMPUTE_INTERNATIONAL_FEE - 生成发货单，待计算国际费用 */
    WAITING_COMPUTE_INTERNATIONAL_FEE = 16,
    UNRECOGNIZED = -1,
}

export function orderStatusFromJSON(object: any): OrderStatus {
    switch (object) {
        case 0:
        case 'STATUS_ALL':
            return OrderStatus.STATUS_ALL
        case 1:
        case 'CANCEL_BEFORE_AUDIT':
            return OrderStatus.CANCEL_BEFORE_AUDIT
        case 2:
        case 'WAITING_APPROVING':
            return OrderStatus.WAITING_APPROVING
        case 3:
        case 'APP_APPROVED':
            return OrderStatus.APP_APPROVED
        case 4:
        case 'PARTIAL_PAYMENT':
            return OrderStatus.PARTIAL_PAYMENT
        case 5:
        case 'PAID_PRODUCT':
            return OrderStatus.PAID_PRODUCT
        case 6:
        case 'THIRD_PARTY_ORDER_SUCCESS':
            return OrderStatus.THIRD_PARTY_ORDER_SUCCESS
        case 7:
        case 'THIRD_PARTY_SHIPPED':
            return OrderStatus.THIRD_PARTY_SHIPPED
        case 8:
        case 'RECEIVED':
            return OrderStatus.RECEIVED
        case 9:
        case 'FILLED_IN_INTERNATIONAL_FREIGHT':
            return OrderStatus.FILLED_IN_INTERNATIONAL_FREIGHT
        case 10:
        case 'PAY_INTERNATIONAL_FREIGHT':
            return OrderStatus.PAY_INTERNATIONAL_FREIGHT
        case 11:
        case 'SHIPPED':
            return OrderStatus.SHIPPED
        case 12:
        case 'CUSTOMS_DECLARATION':
            return OrderStatus.CUSTOMS_DECLARATION
        case 13:
        case 'USER_SIGNED':
            return OrderStatus.USER_SIGNED
        case 14:
        case 'CANCEL_AFTER_AUDIT':
            return OrderStatus.CANCEL_AFTER_AUDIT
        case 15:
        case 'USER_CANCEL':
            return OrderStatus.USER_CANCEL
        case 16:
        case 'WAITING_COMPUTE_INTERNATIONAL_FEE':
            return OrderStatus.WAITING_COMPUTE_INTERNATIONAL_FEE
        case -1:
        case 'UNRECOGNIZED':
        default:
            return OrderStatus.UNRECOGNIZED
    }
}

export function orderStatusToJSON(object: OrderStatus): string {
    switch (object) {
        case OrderStatus.STATUS_ALL:
            return 'STATUS_ALL'
        case OrderStatus.CANCEL_BEFORE_AUDIT:
            return 'CANCEL_BEFORE_AUDIT'
        case OrderStatus.WAITING_APPROVING:
            return 'WAITING_APPROVING'
        case OrderStatus.APP_APPROVED:
            return 'APP_APPROVED'
        case OrderStatus.PARTIAL_PAYMENT:
            return 'PARTIAL_PAYMENT'
        case OrderStatus.PAID_PRODUCT:
            return 'PAID_PRODUCT'
        case OrderStatus.THIRD_PARTY_ORDER_SUCCESS:
            return 'THIRD_PARTY_ORDER_SUCCESS'
        case OrderStatus.THIRD_PARTY_SHIPPED:
            return 'THIRD_PARTY_SHIPPED'
        case OrderStatus.RECEIVED:
            return 'RECEIVED'
        case OrderStatus.FILLED_IN_INTERNATIONAL_FREIGHT:
            return 'FILLED_IN_INTERNATIONAL_FREIGHT'
        case OrderStatus.PAY_INTERNATIONAL_FREIGHT:
            return 'PAY_INTERNATIONAL_FREIGHT'
        case OrderStatus.SHIPPED:
            return 'SHIPPED'
        case OrderStatus.CUSTOMS_DECLARATION:
            return 'CUSTOMS_DECLARATION'
        case OrderStatus.USER_SIGNED:
            return 'USER_SIGNED'
        case OrderStatus.CANCEL_AFTER_AUDIT:
            return 'CANCEL_AFTER_AUDIT'
        case OrderStatus.USER_CANCEL:
            return 'USER_CANCEL'
        case OrderStatus.WAITING_COMPUTE_INTERNATIONAL_FEE:
            return 'WAITING_COMPUTE_INTERNATIONAL_FEE'
        case OrderStatus.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 账单类型 */
export enum SalesOrderBillType {
    ALL_BILL = 0,
    /** DOMESTIC_BILL - 100.国内账单 */
    DOMESTIC_BILL = 1,
    /** OVERSEAS_BILL - 200.国际账单 */
    OVERSEAS_BILL = 2,
    UNRECOGNIZED = -1,
}

export function salesOrderBillTypeFromJSON(object: any): SalesOrderBillType {
    switch (object) {
        case 0:
        case 'ALL_BILL':
            return SalesOrderBillType.ALL_BILL
        case 1:
        case 'DOMESTIC_BILL':
            return SalesOrderBillType.DOMESTIC_BILL
        case 2:
        case 'OVERSEAS_BILL':
            return SalesOrderBillType.OVERSEAS_BILL
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SalesOrderBillType.UNRECOGNIZED
    }
}

export function salesOrderBillTypeToJSON(object: SalesOrderBillType): string {
    switch (object) {
        case SalesOrderBillType.ALL_BILL:
            return 'ALL_BILL'
        case SalesOrderBillType.DOMESTIC_BILL:
            return 'DOMESTIC_BILL'
        case SalesOrderBillType.OVERSEAS_BILL:
            return 'OVERSEAS_BILL'
        case SalesOrderBillType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 账单状态 */
export enum SalesOrderBillStatus {
    BILL_STATUS_ALL = 0,
    /** BILL_CANCEL - -100.取消 */
    BILL_CANCEL = 1,
    /** BILL_WAITING_PAYMENT - 0.待支付 */
    BILL_WAITING_PAYMENT = 2,
    /** BILL_PARTIAL_PAYMENT - 100.部分支付 */
    BILL_PARTIAL_PAYMENT = 3,
    /** BILL_ALREADY_PAID - 200.已支付 */
    BILL_ALREADY_PAID = 4,
    UNRECOGNIZED = -1,
}

export function salesOrderBillStatusFromJSON(object: any): SalesOrderBillStatus {
    switch (object) {
        case 0:
        case 'BILL_STATUS_ALL':
            return SalesOrderBillStatus.BILL_STATUS_ALL
        case 1:
        case 'BILL_CANCEL':
            return SalesOrderBillStatus.BILL_CANCEL
        case 2:
        case 'BILL_WAITING_PAYMENT':
            return SalesOrderBillStatus.BILL_WAITING_PAYMENT
        case 3:
        case 'BILL_PARTIAL_PAYMENT':
            return SalesOrderBillStatus.BILL_PARTIAL_PAYMENT
        case 4:
        case 'BILL_ALREADY_PAID':
            return SalesOrderBillStatus.BILL_ALREADY_PAID
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SalesOrderBillStatus.UNRECOGNIZED
    }
}

export function salesOrderBillStatusToJSON(object: SalesOrderBillStatus): string {
    switch (object) {
        case SalesOrderBillStatus.BILL_STATUS_ALL:
            return 'BILL_STATUS_ALL'
        case SalesOrderBillStatus.BILL_CANCEL:
            return 'BILL_CANCEL'
        case SalesOrderBillStatus.BILL_WAITING_PAYMENT:
            return 'BILL_WAITING_PAYMENT'
        case SalesOrderBillStatus.BILL_PARTIAL_PAYMENT:
            return 'BILL_PARTIAL_PAYMENT'
        case SalesOrderBillStatus.BILL_ALREADY_PAID:
            return 'BILL_ALREADY_PAID'
        case SalesOrderBillStatus.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 操作类型 */
export enum SalesOrderOperationType {
    OP_ALL = 0,
    /** OP_CANCEL - -100.取消 */
    OP_CANCEL = 1,
    /** OP_CREATE_ORDER - 0.创单 */
    OP_CREATE_ORDER = 2,
    /** OP_APPROVED - 100.审核通过 */
    OP_APPROVED = 3,
    /** OP_PAYMENT - 200.支付 */
    OP_PAYMENT = 4,
    /** OP_THIRD_PARTY_ORDER - 400.第三方下单 */
    OP_THIRD_PARTY_ORDER = 5,
    /** OP_THIRD_PARTY_SHIPPED - 500.第三方发货 */
    OP_THIRD_PARTY_SHIPPED = 6,
    /** OP_RECEIVED - 600.收货 */
    OP_RECEIVED = 7,
    /** OP_FILL_IN_INTERNATIONAL_FREIGHT - 700.填写国际运费 */
    OP_FILL_IN_INTERNATIONAL_FREIGHT = 8,
    /** OP_PAY_INTERNATIONAL_FREIGHT - 800.支付国际运费 */
    OP_PAY_INTERNATIONAL_FREIGHT = 9,
    /** OP_SHIPPED - 900.发货 */
    OP_SHIPPED = 10,
    /** OP_CUSTOMS_DECLARATION - 1000.报关 */
    OP_CUSTOMS_DECLARATION = 11,
    /** OP_SIGNED - 1100.签收 */
    OP_SIGNED = 12,
    /** OP_CANCEL_AFTER_AUDIT - -200 */
    OP_CANCEL_AFTER_AUDIT = 13,
    /** OP_USER_CANCEL - -300 */
    OP_USER_CANCEL = 14,
    /** OP_USER_NOTICE - 客户通知 */
    OP_USER_NOTICE = 15,
    UNRECOGNIZED = -1,
}

export function salesOrderOperationTypeFromJSON(object: any): SalesOrderOperationType {
    switch (object) {
        case 0:
        case 'OP_ALL':
            return SalesOrderOperationType.OP_ALL
        case 1:
        case 'OP_CANCEL':
            return SalesOrderOperationType.OP_CANCEL
        case 2:
        case 'OP_CREATE_ORDER':
            return SalesOrderOperationType.OP_CREATE_ORDER
        case 3:
        case 'OP_APPROVED':
            return SalesOrderOperationType.OP_APPROVED
        case 4:
        case 'OP_PAYMENT':
            return SalesOrderOperationType.OP_PAYMENT
        case 5:
        case 'OP_THIRD_PARTY_ORDER':
            return SalesOrderOperationType.OP_THIRD_PARTY_ORDER
        case 6:
        case 'OP_THIRD_PARTY_SHIPPED':
            return SalesOrderOperationType.OP_THIRD_PARTY_SHIPPED
        case 7:
        case 'OP_RECEIVED':
            return SalesOrderOperationType.OP_RECEIVED
        case 8:
        case 'OP_FILL_IN_INTERNATIONAL_FREIGHT':
            return SalesOrderOperationType.OP_FILL_IN_INTERNATIONAL_FREIGHT
        case 9:
        case 'OP_PAY_INTERNATIONAL_FREIGHT':
            return SalesOrderOperationType.OP_PAY_INTERNATIONAL_FREIGHT
        case 10:
        case 'OP_SHIPPED':
            return SalesOrderOperationType.OP_SHIPPED
        case 11:
        case 'OP_CUSTOMS_DECLARATION':
            return SalesOrderOperationType.OP_CUSTOMS_DECLARATION
        case 12:
        case 'OP_SIGNED':
            return SalesOrderOperationType.OP_SIGNED
        case 13:
        case 'OP_CANCEL_AFTER_AUDIT':
            return SalesOrderOperationType.OP_CANCEL_AFTER_AUDIT
        case 14:
        case 'OP_USER_CANCEL':
            return SalesOrderOperationType.OP_USER_CANCEL
        case 15:
        case 'OP_USER_NOTICE':
            return SalesOrderOperationType.OP_USER_NOTICE
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SalesOrderOperationType.UNRECOGNIZED
    }
}

export function salesOrderOperationTypeToJSON(object: SalesOrderOperationType): string {
    switch (object) {
        case SalesOrderOperationType.OP_ALL:
            return 'OP_ALL'
        case SalesOrderOperationType.OP_CANCEL:
            return 'OP_CANCEL'
        case SalesOrderOperationType.OP_CREATE_ORDER:
            return 'OP_CREATE_ORDER'
        case SalesOrderOperationType.OP_APPROVED:
            return 'OP_APPROVED'
        case SalesOrderOperationType.OP_PAYMENT:
            return 'OP_PAYMENT'
        case SalesOrderOperationType.OP_THIRD_PARTY_ORDER:
            return 'OP_THIRD_PARTY_ORDER'
        case SalesOrderOperationType.OP_THIRD_PARTY_SHIPPED:
            return 'OP_THIRD_PARTY_SHIPPED'
        case SalesOrderOperationType.OP_RECEIVED:
            return 'OP_RECEIVED'
        case SalesOrderOperationType.OP_FILL_IN_INTERNATIONAL_FREIGHT:
            return 'OP_FILL_IN_INTERNATIONAL_FREIGHT'
        case SalesOrderOperationType.OP_PAY_INTERNATIONAL_FREIGHT:
            return 'OP_PAY_INTERNATIONAL_FREIGHT'
        case SalesOrderOperationType.OP_SHIPPED:
            return 'OP_SHIPPED'
        case SalesOrderOperationType.OP_CUSTOMS_DECLARATION:
            return 'OP_CUSTOMS_DECLARATION'
        case SalesOrderOperationType.OP_SIGNED:
            return 'OP_SIGNED'
        case SalesOrderOperationType.OP_CANCEL_AFTER_AUDIT:
            return 'OP_CANCEL_AFTER_AUDIT'
        case SalesOrderOperationType.OP_USER_CANCEL:
            return 'OP_USER_CANCEL'
        case SalesOrderOperationType.OP_USER_NOTICE:
            return 'OP_USER_NOTICE'
        case SalesOrderOperationType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 订单取消类型 */
export enum SalesOrderCancelType {
    /** SO_NON_CANCEL - 0.非取消 */
    SO_NON_CANCEL = 0,
    /** SO_CANCEL_BEFORE_AUDIT - 1.审核前取消 */
    SO_CANCEL_BEFORE_AUDIT = 1,
    /** SO_CANCEL_AFTER_AUDIT - 2.审核后取消 */
    SO_CANCEL_AFTER_AUDIT = 2,
    /** SO_USER_CANCEL - 3.用户已取消 */
    SO_USER_CANCEL = 3,
    UNRECOGNIZED = -1,
}

export function salesOrderCancelTypeFromJSON(object: any): SalesOrderCancelType {
    switch (object) {
        case 0:
        case 'SO_NON_CANCEL':
            return SalesOrderCancelType.SO_NON_CANCEL
        case 1:
        case 'SO_CANCEL_BEFORE_AUDIT':
            return SalesOrderCancelType.SO_CANCEL_BEFORE_AUDIT
        case 2:
        case 'SO_CANCEL_AFTER_AUDIT':
            return SalesOrderCancelType.SO_CANCEL_AFTER_AUDIT
        case 3:
        case 'SO_USER_CANCEL':
            return SalesOrderCancelType.SO_USER_CANCEL
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SalesOrderCancelType.UNRECOGNIZED
    }
}

export function salesOrderCancelTypeToJSON(object: SalesOrderCancelType): string {
    switch (object) {
        case SalesOrderCancelType.SO_NON_CANCEL:
            return 'SO_NON_CANCEL'
        case SalesOrderCancelType.SO_CANCEL_BEFORE_AUDIT:
            return 'SO_CANCEL_BEFORE_AUDIT'
        case SalesOrderCancelType.SO_CANCEL_AFTER_AUDIT:
            return 'SO_CANCEL_AFTER_AUDIT'
        case SalesOrderCancelType.SO_USER_CANCEL:
            return 'SO_USER_CANCEL'
        case SalesOrderCancelType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** 字典code */
export enum DictCode {
    /** USER_EDIT_PWD - 用户修改密码 */
    USER_EDIT_PWD = 0,
    /** TRANSPORT - 运输方式 */
    TRANSPORT = 1,
    UNRECOGNIZED = -1,
}

export function dictCodeFromJSON(object: any): DictCode {
    switch (object) {
        case 0:
        case 'USER_EDIT_PWD':
            return DictCode.USER_EDIT_PWD
        case 1:
        case 'TRANSPORT':
            return DictCode.TRANSPORT
        case -1:
        case 'UNRECOGNIZED':
        default:
            return DictCode.UNRECOGNIZED
    }
}

export function dictCodeToJSON(object: DictCode): string {
    switch (object) {
        case DictCode.USER_EDIT_PWD:
            return 'USER_EDIT_PWD'
        case DictCode.TRANSPORT:
            return 'TRANSPORT'
        case DictCode.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum SoHandleUserType {
    /** SYS - 系统 */
    SYS = 0,
    /** CONSUMER - 消费者 */
    CONSUMER = 1,
    /** CUSTOMER - 客服 */
    CUSTOMER = 2,
    UNRECOGNIZED = -1,
}

export function soHandleUserTypeFromJSON(object: any): SoHandleUserType {
    switch (object) {
        case 0:
        case 'SYS':
            return SoHandleUserType.SYS
        case 1:
        case 'CONSUMER':
            return SoHandleUserType.CONSUMER
        case 2:
        case 'CUSTOMER':
            return SoHandleUserType.CUSTOMER
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SoHandleUserType.UNRECOGNIZED
    }
}

export function soHandleUserTypeToJSON(object: SoHandleUserType): string {
    switch (object) {
        case SoHandleUserType.SYS:
            return 'SYS'
        case SoHandleUserType.CONSUMER:
            return 'CONSUMER'
        case SoHandleUserType.CUSTOMER:
            return 'CUSTOMER'
        case SoHandleUserType.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum PayStatusEnum {
    /** INIT - 未支付 */
    INIT = 0,
    /** PAYING - 已发起第三方支付,支付中 */
    PAYING = 10,
    /** PAID_SUCCESS - 支付成功 */
    PAID_SUCCESS = 20,
    /** PAID_FAIL - 支付失败 */
    PAID_FAIL = 30,
    UNRECOGNIZED = -1,
}

export function payStatusEnumFromJSON(object: any): PayStatusEnum {
    switch (object) {
        case 0:
        case 'INIT':
            return PayStatusEnum.INIT
        case 10:
        case 'PAYING':
            return PayStatusEnum.PAYING
        case 20:
        case 'PAID_SUCCESS':
            return PayStatusEnum.PAID_SUCCESS
        case 30:
        case 'PAID_FAIL':
            return PayStatusEnum.PAID_FAIL
        case -1:
        case 'UNRECOGNIZED':
        default:
            return PayStatusEnum.UNRECOGNIZED
    }
}

export function payStatusEnumToJSON(object: PayStatusEnum): string {
    switch (object) {
        case PayStatusEnum.INIT:
            return 'INIT'
        case PayStatusEnum.PAYING:
            return 'PAYING'
        case PayStatusEnum.PAID_SUCCESS:
            return 'PAID_SUCCESS'
        case PayStatusEnum.PAID_FAIL:
            return 'PAID_FAIL'
        case PayStatusEnum.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum MallOrderStatus {
    /** MALL_STATUS_ALL - 查询时用于查询全部状态 */
    MALL_STATUS_ALL = 0,
    /** MALL_WAITING_APPROVING - 待审核----仅用于admin端预览 */
    MALL_WAITING_APPROVING = 1,
    /** MALL_WAIT_PAY_PRODUCT - 支付产品成本 */
    MALL_WAIT_PAY_PRODUCT = 10,
    /** MALL_WAIT_PAY_ALL_FEE - 支付订单费用 */
    MALL_WAIT_PAY_ALL_FEE = 20,
    /** MALL_WAIT_CAL_INTER_FEE - 待计算国际费用 */
    MALL_WAIT_CAL_INTER_FEE = 30,
    /** MALL_WAIT_PAY_INTER_FEE - 支付国际费用 */
    MALL_WAIT_PAY_INTER_FEE = 40,
    /** MALL_PURCHASING - 采购 */
    MALL_PURCHASING = 50,
    /** MALL_WAIT_SEND_OUT - 待发货 */
    MALL_WAIT_SEND_OUT = 60,
    /** MALL_TRANSPORTING - 国际运输 */
    MALL_TRANSPORTING = 70,
    /** MALL_DELIVERING - 派送 */
    MALL_DELIVERING = 80,
    /** MALL_USER_SIGNED - 签收 */
    MALL_USER_SIGNED = 90,
    /** MALL_CANCELED - 已取消 */
    MALL_CANCELED = 100,
    UNRECOGNIZED = -1,
}

export function mallOrderStatusFromJSON(object: any): MallOrderStatus {
    switch (object) {
        case 0:
        case 'MALL_STATUS_ALL':
            return MallOrderStatus.MALL_STATUS_ALL
        case 1:
        case 'MALL_WAITING_APPROVING':
            return MallOrderStatus.MALL_WAITING_APPROVING
        case 10:
        case 'MALL_WAIT_PAY_PRODUCT':
            return MallOrderStatus.MALL_WAIT_PAY_PRODUCT
        case 20:
        case 'MALL_WAIT_PAY_ALL_FEE':
            return MallOrderStatus.MALL_WAIT_PAY_ALL_FEE
        case 30:
        case 'MALL_WAIT_CAL_INTER_FEE':
            return MallOrderStatus.MALL_WAIT_CAL_INTER_FEE
        case 40:
        case 'MALL_WAIT_PAY_INTER_FEE':
            return MallOrderStatus.MALL_WAIT_PAY_INTER_FEE
        case 50:
        case 'MALL_PURCHASING':
            return MallOrderStatus.MALL_PURCHASING
        case 60:
        case 'MALL_WAIT_SEND_OUT':
            return MallOrderStatus.MALL_WAIT_SEND_OUT
        case 70:
        case 'MALL_TRANSPORTING':
            return MallOrderStatus.MALL_TRANSPORTING
        case 80:
        case 'MALL_DELIVERING':
            return MallOrderStatus.MALL_DELIVERING
        case 90:
        case 'MALL_USER_SIGNED':
            return MallOrderStatus.MALL_USER_SIGNED
        case 100:
        case 'MALL_CANCELED':
            return MallOrderStatus.MALL_CANCELED
        case -1:
        case 'UNRECOGNIZED':
        default:
            return MallOrderStatus.UNRECOGNIZED
    }
}

export function mallOrderStatusToJSON(object: MallOrderStatus): string {
    switch (object) {
        case MallOrderStatus.MALL_STATUS_ALL:
            return 'MALL_STATUS_ALL'
        case MallOrderStatus.MALL_WAITING_APPROVING:
            return 'MALL_WAITING_APPROVING'
        case MallOrderStatus.MALL_WAIT_PAY_PRODUCT:
            return 'MALL_WAIT_PAY_PRODUCT'
        case MallOrderStatus.MALL_WAIT_PAY_ALL_FEE:
            return 'MALL_WAIT_PAY_ALL_FEE'
        case MallOrderStatus.MALL_WAIT_CAL_INTER_FEE:
            return 'MALL_WAIT_CAL_INTER_FEE'
        case MallOrderStatus.MALL_WAIT_PAY_INTER_FEE:
            return 'MALL_WAIT_PAY_INTER_FEE'
        case MallOrderStatus.MALL_PURCHASING:
            return 'MALL_PURCHASING'
        case MallOrderStatus.MALL_WAIT_SEND_OUT:
            return 'MALL_WAIT_SEND_OUT'
        case MallOrderStatus.MALL_TRANSPORTING:
            return 'MALL_TRANSPORTING'
        case MallOrderStatus.MALL_DELIVERING:
            return 'MALL_DELIVERING'
        case MallOrderStatus.MALL_USER_SIGNED:
            return 'MALL_USER_SIGNED'
        case MallOrderStatus.MALL_CANCELED:
            return 'MALL_CANCELED'
        case MallOrderStatus.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum VerifyMailSceneEnum {
    /** REGISTER - 注册 */
    REGISTER = 0,
    /** FIRST_GOODS_LOOKING - 首次询盘 */
    FIRST_GOODS_LOOKING = 1,
    /** INVITE_FRIEND - 邀请好友 */
    INVITE_FRIEND = 2,
    UNRECOGNIZED = -1,
}

export function verifyMailSceneEnumFromJSON(object: any): VerifyMailSceneEnum {
    switch (object) {
        case 0:
        case 'REGISTER':
            return VerifyMailSceneEnum.REGISTER
        case 1:
        case 'FIRST_GOODS_LOOKING':
            return VerifyMailSceneEnum.FIRST_GOODS_LOOKING
        case 2:
        case 'INVITE_FRIEND':
            return VerifyMailSceneEnum.INVITE_FRIEND
        case -1:
        case 'UNRECOGNIZED':
        default:
            return VerifyMailSceneEnum.UNRECOGNIZED
    }
}

export function verifyMailSceneEnumToJSON(object: VerifyMailSceneEnum): string {
    switch (object) {
        case VerifyMailSceneEnum.REGISTER:
            return 'REGISTER'
        case VerifyMailSceneEnum.FIRST_GOODS_LOOKING:
            return 'FIRST_GOODS_LOOKING'
        case VerifyMailSceneEnum.INVITE_FRIEND:
            return 'INVITE_FRIEND'
        case VerifyMailSceneEnum.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

export enum VerifyMailResultEnum {
    /** SUCCESS - 验证成功 */
    SUCCESS = 0,
    /** EXPIRED - 验证链接已失效 */
    EXPIRED = 1,
    /** ALREADY_VERIFIED - 已验证过 */
    ALREADY_VERIFIED = 2,
    UNRECOGNIZED = -1,
}

export function verifyMailResultEnumFromJSON(object: any): VerifyMailResultEnum {
    switch (object) {
        case 0:
        case 'SUCCESS':
            return VerifyMailResultEnum.SUCCESS
        case 1:
        case 'EXPIRED':
            return VerifyMailResultEnum.EXPIRED
        case 2:
        case 'ALREADY_VERIFIED':
            return VerifyMailResultEnum.ALREADY_VERIFIED
        case -1:
        case 'UNRECOGNIZED':
        default:
            return VerifyMailResultEnum.UNRECOGNIZED
    }
}

export function verifyMailResultEnumToJSON(object: VerifyMailResultEnum): string {
    switch (object) {
        case VerifyMailResultEnum.SUCCESS:
            return 'SUCCESS'
        case VerifyMailResultEnum.EXPIRED:
            return 'EXPIRED'
        case VerifyMailResultEnum.ALREADY_VERIFIED:
            return 'ALREADY_VERIFIED'
        case VerifyMailResultEnum.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED'
    }
}

/** SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同） */
export interface SkuStepRange {
    /** 起始件数（第一个阶梯的start，总是从1开始） */
    start: number
    /** 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1） */
    end: number
}

/** SKU阶梯价格 */
export interface SkuStepPrice {
    /** 起始件数（第一个阶梯的start，总是从1开始） */
    start: number
    /** 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1） */
    end: number
    /** 阶梯价（最小售卖单位的价格，即商品价格单位对应的价格；第一个阶梯的salePrice总是与sku中的salePrice相同） */
    price: number
    /** 内含单件商品的价格（与包装含量相关；chilat2.0中的包装含量总是1，因此insideOnePrice总是与stepPrice相等） */
    insideOnePrice: number
}

/** Seo标签数据 */
export interface SeoData {
    /** html 网页标题 */
    title: string
    /** meta 网页关键字 */
    keyword: string
    /** meta 网页描述 */
    description: string
    /** 若存在，插入到 head 标签开始后的位置 */
    headFirstScript: string
    /** 若存在，插入到 head 标签结束前的位置 */
    headLastScript: string
    /** 若存在，插入到 body 标签开始后的位置 */
    bodyFirstContent: string
    /** 若存在，插入到 body 标签结束前的位置 */
    bodyLastContent: string
    /** 若存在，在 response 响应头部输出: res.setHeader(name, vale) */
    responseHeaders: NameValueModel[]
}

/** 排序字段 */
export interface SortField {
    /** 排序字段名（后端提供字段名；英文大小写不敏感） */
    fieldName: string
    /** 排序类型（可选值：ASC升序，DESC降序；英文大小写不敏感） */
    sortType: string
}

function createBaseSkuStepRange(): SkuStepRange {
    return { start: 0, end: 0 }
}

export const SkuStepRange = {
    encode(message: SkuStepRange, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
        if (message.start !== 0) {
            writer.uint32(8).int32(message.start)
        }
        if (message.end !== 0) {
            writer.uint32(16).int32(message.end)
        }
        return writer
    },

    decode(input: _m0.Reader | Uint8Array, length?: number): SkuStepRange {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
        let end = length === undefined ? reader.len : reader.pos + length
        const message = createBaseSkuStepRange()
        while (reader.pos < end) {
            const tag = reader.uint32()
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break
                    }

                    message.start = reader.int32()
                    continue
                case 2:
                    if (tag !== 16) {
                        break
                    }

                    message.end = reader.int32()
                    continue
            }
            if ((tag & 7) === 4 || tag === 0) {
                break
            }
            reader.skipType(tag & 7)
        }
        return message
    },

    fromJSON(object: any): SkuStepRange {
        return {
            start: isSet(object.start) ? globalThis.Number(object.start) : 0,
            end: isSet(object.end) ? globalThis.Number(object.end) : 0,
        }
    },

    toJSON(message: SkuStepRange): unknown {
        const obj: any = {}
        if (message.start !== 0) {
            obj.start = Math.round(message.start)
        }
        if (message.end !== 0) {
            obj.end = Math.round(message.end)
        }
        return obj
    },

    create<I extends Exact<DeepPartial<SkuStepRange>, I>>(base?: I): SkuStepRange {
        return SkuStepRange.fromPartial(base ?? ({} as any))
    },
    fromPartial<I extends Exact<DeepPartial<SkuStepRange>, I>>(object: I): SkuStepRange {
        const message = createBaseSkuStepRange()
        message.start = object.start ?? 0
        message.end = object.end ?? 0
        return message
    },
}

function createBaseSkuStepPrice(): SkuStepPrice {
    return { start: 0, end: 0, price: 0, insideOnePrice: 0 }
}

export const SkuStepPrice = {
    encode(message: SkuStepPrice, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
        if (message.start !== 0) {
            writer.uint32(8).int32(message.start)
        }
        if (message.end !== 0) {
            writer.uint32(16).int32(message.end)
        }
        if (message.price !== 0) {
            writer.uint32(25).double(message.price)
        }
        if (message.insideOnePrice !== 0) {
            writer.uint32(33).double(message.insideOnePrice)
        }
        return writer
    },

    decode(input: _m0.Reader | Uint8Array, length?: number): SkuStepPrice {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
        let end = length === undefined ? reader.len : reader.pos + length
        const message = createBaseSkuStepPrice()
        while (reader.pos < end) {
            const tag = reader.uint32()
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 8) {
                        break
                    }

                    message.start = reader.int32()
                    continue
                case 2:
                    if (tag !== 16) {
                        break
                    }

                    message.end = reader.int32()
                    continue
                case 3:
                    if (tag !== 25) {
                        break
                    }

                    message.price = reader.double()
                    continue
                case 4:
                    if (tag !== 33) {
                        break
                    }

                    message.insideOnePrice = reader.double()
                    continue
            }
            if ((tag & 7) === 4 || tag === 0) {
                break
            }
            reader.skipType(tag & 7)
        }
        return message
    },

    fromJSON(object: any): SkuStepPrice {
        return {
            start: isSet(object.start) ? globalThis.Number(object.start) : 0,
            end: isSet(object.end) ? globalThis.Number(object.end) : 0,
            price: isSet(object.price) ? globalThis.Number(object.price) : 0,
            insideOnePrice: isSet(object.insideOnePrice) ? globalThis.Number(object.insideOnePrice) : 0,
        }
    },

    toJSON(message: SkuStepPrice): unknown {
        const obj: any = {}
        if (message.start !== 0) {
            obj.start = Math.round(message.start)
        }
        if (message.end !== 0) {
            obj.end = Math.round(message.end)
        }
        if (message.price !== 0) {
            obj.price = message.price
        }
        if (message.insideOnePrice !== 0) {
            obj.insideOnePrice = message.insideOnePrice
        }
        return obj
    },

    create<I extends Exact<DeepPartial<SkuStepPrice>, I>>(base?: I): SkuStepPrice {
        return SkuStepPrice.fromPartial(base ?? ({} as any))
    },
    fromPartial<I extends Exact<DeepPartial<SkuStepPrice>, I>>(object: I): SkuStepPrice {
        const message = createBaseSkuStepPrice()
        message.start = object.start ?? 0
        message.end = object.end ?? 0
        message.price = object.price ?? 0
        message.insideOnePrice = object.insideOnePrice ?? 0
        return message
    },
}

function createBaseSeoData(): SeoData {
    return {
        title: '',
        keyword: '',
        description: '',
        headFirstScript: '',
        headLastScript: '',
        bodyFirstContent: '',
        bodyLastContent: '',
        responseHeaders: [],
    }
}

export const SeoData = {
    encode(message: SeoData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
        if (message.title !== '') {
            writer.uint32(82).string(message.title)
        }
        if (message.keyword !== '') {
            writer.uint32(162).string(message.keyword)
        }
        if (message.description !== '') {
            writer.uint32(242).string(message.description)
        }
        if (message.headFirstScript !== '') {
            writer.uint32(882).string(message.headFirstScript)
        }
        if (message.headLastScript !== '') {
            writer.uint32(962).string(message.headLastScript)
        }
        if (message.bodyFirstContent !== '') {
            writer.uint32(1042).string(message.bodyFirstContent)
        }
        if (message.bodyLastContent !== '') {
            writer.uint32(1122).string(message.bodyLastContent)
        }
        for (const v of message.responseHeaders) {
            NameValueModel.encode(v!, writer.uint32(1202).fork()).ldelim()
        }
        return writer
    },

    decode(input: _m0.Reader | Uint8Array, length?: number): SeoData {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
        let end = length === undefined ? reader.len : reader.pos + length
        const message = createBaseSeoData()
        while (reader.pos < end) {
            const tag = reader.uint32()
            switch (tag >>> 3) {
                case 10:
                    if (tag !== 82) {
                        break
                    }

                    message.title = reader.string()
                    continue
                case 20:
                    if (tag !== 162) {
                        break
                    }

                    message.keyword = reader.string()
                    continue
                case 30:
                    if (tag !== 242) {
                        break
                    }

                    message.description = reader.string()
                    continue
                case 110:
                    if (tag !== 882) {
                        break
                    }

                    message.headFirstScript = reader.string()
                    continue
                case 120:
                    if (tag !== 962) {
                        break
                    }

                    message.headLastScript = reader.string()
                    continue
                case 130:
                    if (tag !== 1042) {
                        break
                    }

                    message.bodyFirstContent = reader.string()
                    continue
                case 140:
                    if (tag !== 1122) {
                        break
                    }

                    message.bodyLastContent = reader.string()
                    continue
                case 150:
                    if (tag !== 1202) {
                        break
                    }

                    message.responseHeaders.push(NameValueModel.decode(reader, reader.uint32()))
                    continue
            }
            if ((tag & 7) === 4 || tag === 0) {
                break
            }
            reader.skipType(tag & 7)
        }
        return message
    },

    fromJSON(object: any): SeoData {
        return {
            title: isSet(object.title) ? globalThis.String(object.title) : '',
            keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : '',
            description: isSet(object.description) ? globalThis.String(object.description) : '',
            headFirstScript: isSet(object.headFirstScript) ? globalThis.String(object.headFirstScript) : '',
            headLastScript: isSet(object.headLastScript) ? globalThis.String(object.headLastScript) : '',
            bodyFirstContent: isSet(object.bodyFirstContent) ? globalThis.String(object.bodyFirstContent) : '',
            bodyLastContent: isSet(object.bodyLastContent) ? globalThis.String(object.bodyLastContent) : '',
            responseHeaders: globalThis.Array.isArray(object?.responseHeaders) ? object.responseHeaders.map((e: any) => NameValueModel.fromJSON(e)) : [],
        }
    },

    toJSON(message: SeoData): unknown {
        const obj: any = {}
        if (message.title !== '') {
            obj.title = message.title
        }
        if (message.keyword !== '') {
            obj.keyword = message.keyword
        }
        if (message.description !== '') {
            obj.description = message.description
        }
        if (message.headFirstScript !== '') {
            obj.headFirstScript = message.headFirstScript
        }
        if (message.headLastScript !== '') {
            obj.headLastScript = message.headLastScript
        }
        if (message.bodyFirstContent !== '') {
            obj.bodyFirstContent = message.bodyFirstContent
        }
        if (message.bodyLastContent !== '') {
            obj.bodyLastContent = message.bodyLastContent
        }
        if (message.responseHeaders?.length) {
            obj.responseHeaders = message.responseHeaders.map((e) => NameValueModel.toJSON(e))
        }
        return obj
    },

    create<I extends Exact<DeepPartial<SeoData>, I>>(base?: I): SeoData {
        return SeoData.fromPartial(base ?? ({} as any))
    },
    fromPartial<I extends Exact<DeepPartial<SeoData>, I>>(object: I): SeoData {
        const message = createBaseSeoData()
        message.title = object.title ?? ''
        message.keyword = object.keyword ?? ''
        message.description = object.description ?? ''
        message.headFirstScript = object.headFirstScript ?? ''
        message.headLastScript = object.headLastScript ?? ''
        message.bodyFirstContent = object.bodyFirstContent ?? ''
        message.bodyLastContent = object.bodyLastContent ?? ''
        message.responseHeaders = object.responseHeaders?.map((e) => NameValueModel.fromPartial(e)) || []
        return message
    },
}

function createBaseSortField(): SortField {
    return { fieldName: '', sortType: '' }
}

export const SortField = {
    encode(message: SortField, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
        if (message.fieldName !== '') {
            writer.uint32(10).string(message.fieldName)
        }
        if (message.sortType !== '') {
            writer.uint32(18).string(message.sortType)
        }
        return writer
    },

    decode(input: _m0.Reader | Uint8Array, length?: number): SortField {
        const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input)
        let end = length === undefined ? reader.len : reader.pos + length
        const message = createBaseSortField()
        while (reader.pos < end) {
            const tag = reader.uint32()
            switch (tag >>> 3) {
                case 1:
                    if (tag !== 10) {
                        break
                    }

                    message.fieldName = reader.string()
                    continue
                case 2:
                    if (tag !== 18) {
                        break
                    }

                    message.sortType = reader.string()
                    continue
            }
            if ((tag & 7) === 4 || tag === 0) {
                break
            }
            reader.skipType(tag & 7)
        }
        return message
    },

    fromJSON(object: any): SortField {
        return {
            fieldName: isSet(object.fieldName) ? globalThis.String(object.fieldName) : '',
            sortType: isSet(object.sortType) ? globalThis.String(object.sortType) : '',
        }
    },

    toJSON(message: SortField): unknown {
        const obj: any = {}
        if (message.fieldName !== '') {
            obj.fieldName = message.fieldName
        }
        if (message.sortType !== '') {
            obj.sortType = message.sortType
        }
        return obj
    },

    create<I extends Exact<DeepPartial<SortField>, I>>(base?: I): SortField {
        return SortField.fromPartial(base ?? ({} as any))
    },
    fromPartial<I extends Exact<DeepPartial<SortField>, I>>(object: I): SortField {
        const message = createBaseSortField()
        message.fieldName = object.fieldName ?? ''
        message.sortType = object.sortType ?? ''
        return message
    },
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
    ? T
    : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
    ? ReadonlyArray<DeepPartial<U>>
    : T extends {}
    ? { [K in keyof T]?: DeepPartial<T[K]> }
    : Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin ? P : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function isSet(value: any): boolean {
    return value !== null && value !== undefined
}
