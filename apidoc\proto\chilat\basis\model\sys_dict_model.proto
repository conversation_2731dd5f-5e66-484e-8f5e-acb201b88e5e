syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";

message DictItemListResp {
  common.Result result = 1;
  repeated DictItemModel data = 2;
}

message DictItemModel {
  string dictId = 1;
  string itemCode = 2; //编码
  string itemName = 3; //名称
  string itemValue = 5; //数值
  int32 defaultFlag = 6; //默认标识
  int32 sysFlag = 7; //系统默认数据10：是，20否
}