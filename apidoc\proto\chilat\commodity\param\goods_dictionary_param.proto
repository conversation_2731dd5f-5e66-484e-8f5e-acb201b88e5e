syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsDictionaryPageQueryParam {
  common.PageParam page = 1;
  GoodsDictionaryQueryParam query = 2;
}

message GoodsDictionaryQueryParam {

}

message GoodsDictionarySaveParam {
  string id = 1; // ID
  string itemName = 2; //包装名称
  string itemAlias = 7; //包装别名
  string itemValue = 3; //包装数量
  string itemRemark = 4; //包装备注
  int32 idx = 5; //顺序
  string itemNameCn = 101; //包装名称中文
}