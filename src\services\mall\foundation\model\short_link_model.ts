/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";

export const protobufPackage = "mall.foundation";

/** 获取短链接请求返回 */
export interface GetShortLinkResp {
  result: Result | undefined;
  data: GetShortLinkModel | undefined;
}

/** 获取短链接请求结果 */
export interface GetShortLinkModel {
  /** 转换短链接后的URL */
  shortUrl: string;
  /** 短链代码（可根据短链代码，获取原链接） */
  linkCode: string;
}

/** 获取完整链接请求返回 */
export interface GetFullLinkResp {
  result: Result | undefined;
  data: GetShortLinkModel | undefined;
}

/** 获取短链接请求结果 */
export interface GetFullLinkModel {
  /** 转换完整URL（源链接） */
  fullUrl: string;
  /** 短链代码（可根据短链代码，获取源链接） */
  linkCode: string;
}

function createBaseGetShortLinkResp(): GetShortLinkResp {
  return { result: undefined, data: undefined };
}

export const GetShortLinkResp = {
  encode(message: GetShortLinkResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetShortLinkModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetShortLinkResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShortLinkResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GetShortLinkModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetShortLinkResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetShortLinkModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetShortLinkResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetShortLinkModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetShortLinkResp>, I>>(base?: I): GetShortLinkResp {
    return GetShortLinkResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetShortLinkResp>, I>>(object: I): GetShortLinkResp {
    const message = createBaseGetShortLinkResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetShortLinkModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetShortLinkModel(): GetShortLinkModel {
  return { shortUrl: "", linkCode: "" };
}

export const GetShortLinkModel = {
  encode(message: GetShortLinkModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.shortUrl !== "") {
      writer.uint32(82).string(message.shortUrl);
    }
    if (message.linkCode !== "") {
      writer.uint32(162).string(message.linkCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetShortLinkModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShortLinkModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.shortUrl = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.linkCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetShortLinkModel {
    return {
      shortUrl: isSet(object.shortUrl) ? globalThis.String(object.shortUrl) : "",
      linkCode: isSet(object.linkCode) ? globalThis.String(object.linkCode) : "",
    };
  },

  toJSON(message: GetShortLinkModel): unknown {
    const obj: any = {};
    if (message.shortUrl !== "") {
      obj.shortUrl = message.shortUrl;
    }
    if (message.linkCode !== "") {
      obj.linkCode = message.linkCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetShortLinkModel>, I>>(base?: I): GetShortLinkModel {
    return GetShortLinkModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetShortLinkModel>, I>>(object: I): GetShortLinkModel {
    const message = createBaseGetShortLinkModel();
    message.shortUrl = object.shortUrl ?? "";
    message.linkCode = object.linkCode ?? "";
    return message;
  },
};

function createBaseGetFullLinkResp(): GetFullLinkResp {
  return { result: undefined, data: undefined };
}

export const GetFullLinkResp = {
  encode(message: GetFullLinkResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetShortLinkModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetFullLinkResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetFullLinkResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GetShortLinkModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetFullLinkResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetShortLinkModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetFullLinkResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetShortLinkModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetFullLinkResp>, I>>(base?: I): GetFullLinkResp {
    return GetFullLinkResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFullLinkResp>, I>>(object: I): GetFullLinkResp {
    const message = createBaseGetFullLinkResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetShortLinkModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetFullLinkModel(): GetFullLinkModel {
  return { fullUrl: "", linkCode: "" };
}

export const GetFullLinkModel = {
  encode(message: GetFullLinkModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.fullUrl !== "") {
      writer.uint32(82).string(message.fullUrl);
    }
    if (message.linkCode !== "") {
      writer.uint32(162).string(message.linkCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetFullLinkModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetFullLinkModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.fullUrl = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.linkCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetFullLinkModel {
    return {
      fullUrl: isSet(object.fullUrl) ? globalThis.String(object.fullUrl) : "",
      linkCode: isSet(object.linkCode) ? globalThis.String(object.linkCode) : "",
    };
  },

  toJSON(message: GetFullLinkModel): unknown {
    const obj: any = {};
    if (message.fullUrl !== "") {
      obj.fullUrl = message.fullUrl;
    }
    if (message.linkCode !== "") {
      obj.linkCode = message.linkCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetFullLinkModel>, I>>(base?: I): GetFullLinkModel {
    return GetFullLinkModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFullLinkModel>, I>>(object: I): GetFullLinkModel {
    const message = createBaseGetFullLinkModel();
    message.fullUrl = object.fullUrl ?? "";
    message.linkCode = object.linkCode ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
