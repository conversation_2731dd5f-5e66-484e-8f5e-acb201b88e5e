/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { PageParam } from "../../../common";
import { TicketStatus, ticketStatusFromJSON, ticketStatusToJSON } from "../../coupon/coupon_detail_common";

export const protobufPackage = "chilat.basis";

/** 我的优惠券列表 未使用/已使用/已失效 */
export interface MyCouponDetailParam {
  /** 分页参数 */
  page:
    | PageParam
    | undefined;
  /** 优惠券状态 */
  ticketStatus: TicketStatus;
  /** 用户id */
  userId: string;
}

function createBaseMyCouponDetailParam(): MyCouponDetailParam {
  return { page: undefined, ticketStatus: 0, userId: "" };
}

export const MyCouponDetailParam = {
  encode(message: MyCouponDetailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(10).fork()).ldelim();
    }
    if (message.ticketStatus !== 0) {
      writer.uint32(16).int32(message.ticketStatus);
    }
    if (message.userId !== "") {
      writer.uint32(26).string(message.userId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MyCouponDetailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMyCouponDetailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.ticketStatus = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.userId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MyCouponDetailParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      ticketStatus: isSet(object.ticketStatus) ? ticketStatusFromJSON(object.ticketStatus) : 0,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
    };
  },

  toJSON(message: MyCouponDetailParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.ticketStatus !== 0) {
      obj.ticketStatus = ticketStatusToJSON(message.ticketStatus);
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MyCouponDetailParam>, I>>(base?: I): MyCouponDetailParam {
    return MyCouponDetailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyCouponDetailParam>, I>>(object: I): MyCouponDetailParam {
    const message = createBaseMyCouponDetailParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.ticketStatus = object.ticketStatus ?? 0;
    message.userId = object.userId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
