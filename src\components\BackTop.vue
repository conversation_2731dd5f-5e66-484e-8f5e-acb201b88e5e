<template>
  <icon-card
    size="36"
    color="#E50113"
    class="back-top"
    name="teenyicons:up-circle-solid"
    v-show="showBackTop"
    @click="onBackTop"
  ></icon-card>
</template>

<script setup lang="ts" name="BackTop">
const showBackTop = ref(false);

function handleScroll(e: any) {
  let scrollHeight =
    document.documentElement.scrollTop ||
    document.body.scrollTop ||
    e.target.scrollTop;
  const halfScreenHeight = window.innerHeight / 2;
  showBackTop.value = scrollHeight > halfScreenHeight;
}

function onBackTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
.back-top {
  position: fixed;
  bottom: 120px;
  right: 36px;
  border-radius: 50%;
  cursor: pointer;
  background: #fff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
  z-index: 99;
}
</style>
