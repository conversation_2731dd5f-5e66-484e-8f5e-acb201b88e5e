syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user";

import "chilat/user/param/role_param.proto";
import "chilat/user/model/role_model.proto";
import "common.proto";

// 角色管理
service Role {
  // 角色列表
  rpc pageList(RolePageQueryParam) returns (RolePageResp);
  // 所有角色
  rpc list(RoleQueryParam) returns (RoleListResp);
  // 添加编辑角色
  rpc save(RoleSaveParam) returns (RoleResp);
  // 删除角色
  rpc remove(common.IdParam) returns (common.ApiResult);
  // 启用禁用角色
  rpc enable(common.EnabledParam) returns (common.ApiResult);
  // 获取角色权限
  rpc getPermission(common.IdParam) returns (common.IdsResult);
  // 设置角色权限
  rpc setPermission(common.IdValuesParam) returns (common.ApiResult);
}
